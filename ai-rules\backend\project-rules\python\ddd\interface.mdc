---
description: 
globs: backend/app/interface/controllers/**/*.py
alwaysApply: false
---
# 接口层 (Interface Layer) 代码生成规则

> **核心原则：简洁优先**

## 边界
- 负责路由、输入验证、授权、输出序列化  
- 禁止直接访问仓储、事务或 ORM；仅调用 Application 层  
- 统一响应 `ApiResponse` 结构： `api_response({"data": …, "code": 0, "error": ""})`
- 记录入口与结果日志；异常抛给全局处理器  

## 约定
- URL `/api/v{n}/{resource}`；函数 `<verb>_<noun>`  
- 路由 tags=聚合名；summary/description 使用领域语言  
- 请求模型使用 `pydantic`，响应统一包装成 `ApiResponse` 结构
- 分页参数固定 `limit/offset`，返回 `PageResponse`  
- 依赖注入通过 `Depends(get_<app>_app_service)`  
- 日志`logger.info` 记录 trace‑id、入口、结果


## 示例代码
```python
router = APIRouter(prefix="/api/v1/auth", tags=["认证"])


@router.post("/login", response_model=ApiResponse[TokenResponse], summary="用户登录")
def login_user(
    req: Request,
    body: LoginRequest,
    svc: AuthAppService = Depends(get_auth_app_service),
):
    trace_id = req.headers.get("X-Trace-Id", uuid4().hex)
    logger.info("trace=%s POST /login", trace_id)

    try:
        dto = svc.login(LoginCommand(**body.dict(), ip=req.client.host, idem_key=trace_id))
    except ApplicationError as exc:
        raise HTTPException(status_code=200, detail=api_response(code=exc.code, message=exc.message).dict())

    return api_response(dto)
```