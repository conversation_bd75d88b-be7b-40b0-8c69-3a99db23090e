# PRD - AI 课中 - 练习组件 1.0 **版本管理**

<table>
<tr>
<td>版本号<br/></td><td>日期<br/></td><td>变更人<br/></td><td>变更类型<br/></td><td>变更详情<br/></td></tr>
<tr>
<td>V1.0<br/></td><td>2025.04.25<br/></td><td>张博<br/></td><td>新建<br/></td><td>- 新建文档<br/></td></tr>
</table>

**关联需求**

<table>
<tr>
<td>关联需求名称<br/></td><td>所属 PM<br/></td><td>需求进度<br/></td><td>文档链接<br/></td></tr>
<tr>
<td>AI 课中 - V1.0<br/></td><td>ou_b2709be118f67c0f1ae49e028241afd2<br/></td><td>待开发<br/></td><td>[PRD - AI课中 - V1.0](https://wcng60ba718p.feishu.cn/wiki/XtfswiCCQiKHdgkev4Qc1Nqined)<br/></td></tr>
<tr>
<td>题型支持<br/></td><td>ou_4075bb29f2fdec7724d8181104831d94<br/></td><td>UI 中<br/></td><td>[PRD -  题型支持 - 一期](https://wcng60ba718p.feishu.cn/wiki/CXIWw1jTriZkiJkWUQ9cVFyBnBd)<br/></td></tr>
</table>

**设计稿**
视觉稿：待补充

# 一、背景和目标

## 需求背景

为了优化 AI 课中整体学习体验，提升学生作答流畅度和情感反馈体验，本次练习组件在 Demo V1.0 与 V1.1 基础上进行进一步升级。
目标是通过引入更友好的转场动画、作答即时反馈机制，提升课程满意度与学习效果。

## 项目收益

- 优化课中作答体验。
- 通过即时反馈与情感激励，增强学生参与感。

## 覆盖用户

- 使用新 AI 课版本的全量学生用户

## 方案简述

本次方案主要聚焦在练习组件的交互体验升级，包括：

- 进入练习转场动效优化
- 题目间即时反馈机制
- 支持勾画与加入错题本功能复用

## 未来会做什么

1. 引入互动讲题模块，基于题目分布讲解和互动，进一步提升课中练习体验与教学效果。

# **二、名词说明**

暂无

# 三、业务流程

> 整体课程结构和 demo 阶段类似，V1.0 只支持 「文档组件」、「练习组件」、「答疑组件」
> 一节课由不同的可配置组件构成。组件之间不同的组合方式构成了不同的课程类型。
> 答疑组件在课中不能单独配置，需要挂载在「文档组件」/「练习组件」下

<table>
<tr>
<td>单节课程结构<br/></td><td>组件类型<br/></td><td>说明<br/></td></tr>
<tr>
<td rowspan="3">![in_table_IhpkwURCUh57uYbMqBhckYbNnld](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_IhpkwURCUh57uYbMqBhckYbNnld.png)<br/></td><td>文档组件<br/></td><td>课程内容为JS文档形式，支持学生在上课过程中自由浏览，支持点击/长按进行提问<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>支持各类题型（一期仅支持单选），可按策略推送题目<br/></td></tr>
<tr>
<td>答疑组件<br/></td><td>基于大模型能力和学生进行实时对话，解答学生问题<br/></td></tr>
</table>

![in_table_IhpkwURCUh57uYbMqBhckYbNnld]

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】

基于图片内容，此图描绘了“单节课程内容”的结构化流程及其关联的功能组件。

**1. 关键元素与层级结构：**

图片主要包含三个层级：

*   **第一层：课程流程层**
    *   核心是“单节课程内容”的线性流程，按顺序展示了一节课从开始到结束的各个阶段。
*   **第二层：主要功能/内容组件层**
    *   每个课程流程节点向下关联一个具体的功能页面或内容组件（如文档组件、练习组件）。
*   **第三层：辅助功能组件层**
    *   部分第二层的内容组件（文档组件、练习组件）进一步关联了“答疑组件”。

**元素间关联：**

*   第一层的课程流程节点之间是严格的**顺序关系**，通过箭头指示流程方向。
*   第一层节点与第二层组件之间是**一对一的直接关联关系**，通过虚线连接，表示该课程阶段由对应的组件承载或实现。
*   第二层组件与第三层组件之间是**一对一的关联关系**，通过虚线连接，表示该内容组件可附加答疑功能。

**2. 各组成部分拆解及功能概述：**

*   **课程流程层模块：**
    *   `课程开场页`: 课程开始时的引导或欢迎页面。
    *   `课程引入`: 对本节课程内容的介绍和导入。
    *   `知识点1`: 教授的第一个核心知识内容。
    *   `知识点2`: 教授的第二个核心知识内容。
    *   `练习1`: 针对知识点1和2的练习环节。
    *   `知识点3`: 教授的第三个核心知识内容。
    *   `知识点4`: 教授的第四个核心知识内容。
    *   `练习2`: 针对知识点3和4的练习环节。
    *   `课程总结`: 对本节课程所有知识点的回顾和总结。
    *   `学习报告`: 展示学员本节课学习情况的报告页面。

*   **主要功能/内容组件层模块 (根据关联的课程流程节点进行区分)：**
    *   `功能页面 (关联课程开场页)`: 实现课程开场页相关功能的页面。
    *   `文档组件 (关联课程引入)`: 展示课程引入内容的组件。
    *   `文档组件 (关联知识点1)`: 展示知识点1教学内容的组件。
    *   `文档组件 (关联知识点2)`: 展示知识点2教学内容的组件。
    *   `练习组件 (关联练习1)`: 提供练习1作答与反馈功能的组件。
    *   `文档组件 (关联知识点3)`: 展示知识点3教学内容的组件。
    *   `文档组件 (关联知识点4)`: 展示知识点4教学内容的组件。
    *   `练习组件 (关联练习2)`: 提供练习2作答与反馈功能的组件。
    *   `文档组件 (关联课程总结)`: 展示课程总结内容的组件。
    *   `功能页面 (关联学习报告)`: 实现学习报告展示功能的页面。

*   **辅助功能组件层模块 (根据关联的主要功能/内容组件进行区分)：**
    *   `答疑组件 (关联课程引入的文档组件)`: 为课程引入部分提供问答支持。
    *   `答疑组件 (关联知识点1的文档组件)`: 为知识点1学习提供问答支持。
    *   `答疑组件 (关联知识点2的文档组件)`: 为知识点2学习提供问答支持。
    *   `答疑组件 (关联练习1的练习组件)`: 为练习1提供问答支持。
    *   `答疑组件 (关联知识点3的文档组件)`: 为知识点3学习提供问答支持。
    *   `答疑组件 (关联知识点4的文档组件)`: 为知识点4学习提供问答支持。
    *   `答疑组件 (关联练习2的练习组件)`: 为练习2提供问答支持。
    *   `答疑组件 (关联课程总结的文档组件)`: 为课程总结部分提供问答支持。

**3. 数学公式识别：**

图片中未识别到数学公式。

**4. 流程图 (Mermaid 语法)：**

```mermaid
flowchart TD
    subgraph 单节课程内容
        A[课程开场页] --> B[课程引入]
        B --> C[知识点1]
        C --> D[知识点2]
        D --> E[练习1]
        E --> F[知识点3]
        F --> G[知识点4]
        G --> H[练习2]
        H --> I[课程总结]
        I --> J[学习报告]
    end

    A1[功能页面]
    B1[文档组件]
    C1[文档组件]
    D1[文档组件]
    E1[练习组件]
    F1[文档组件]
    G1[文档组件]
    H1[练习组件]
    I1[文档组件]
    J1[功能页面]

    B2[答疑组件]
    C2[答疑组件]
    D2[答疑组件]
    E2[答疑组件]
    F2[答疑组件]
    G2[答疑组件]
    H2[答疑组件]
    I2[答疑组件]

    A -.-> A1
    B -.-> B1
    C -.-> C1
    D -.-> D1
    E -.-> E1
    F -.-> F1
    G -.-> G1
    H -.-> H1
    I -.-> I1
    J -.-> J1

    B1 -.-> B2
    C1 -.-> C2
    D1 -.-> D2
    E1 -.-> E2
    F1 -.-> F2
    G1 -.-> G2
    H1 -.-> H2
    I1 -.-> I2
```

【============== 图片解析 END ==============】



# 四、需求概览

<table>
<tr>
<td>模块<br/></td><td>需求描述<br/></td><td>优先级<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>增加进入转场和题目间转场和反馈。<br/></td><td>P0<br/></td></tr>
</table>

# 五、详细产品方案

## 练习组件

嗯 我现在想着是 进入练习时 老师在但是内容 侧滑进来有个题的转场。
然后老师说完 题和老师滑走，然后进入题目。

<table>
<tr>
<td>模块/功能<br/></td><td>需求描述<br/></td><td>原型图<br/></td></tr>
<tr>
<td>进入/离开转场<br/></td><td>**进入练习提示：**<br/>- 练习组件最顶部，增加“即将进入练习” tip ，预告即将进入练习环节。<br/>- 判断：当练习组件前有其他组件时，展示该提示。<br/><br/>**进入练习转场：**<br/>- 首次进入练习（所有题目未作答时）：- 时机：用户完成课程后，进入练习组件时。- 转场：- 转场动效，翻页。- IP~~动效~~ + 文案。- 文案：开始练习 + 课程名称。- 展示时间： 2 s。<br/>- 再次进入练习- 时机：用户退出课程后再次进入课程时进度在练习组件时，- 转场：- IP 动效 + 文案。- 文案：继续练习 + 课程名称。- 展示时间： 2 s。<br/><br/>**离开练习转场**：<br/>- 时机：用户完成练习后（点击最后一题的继续按钮）。<br/>- 转场：- 转场动效，翻页。<br/></td><td>![in_table_XwKpbrJtaoPYEIxbkDbccUBKnYd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_XwKpbrJtaoPYEIxbkDbccUBKnYd.png)<br/><br/>翻页动效 （进入和退出）<br/>![in_table_L4WcbI0zAoClQpx9hBmcJNjXnkd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_L4WcbI0zAoClQpx9hBmcJNjXnkd.png)<br/></td></tr>
<tr>
<td>练习环节<br/></td><td>**页面框架**<br/>- **顶部导航栏：**- 退出按钮：逻辑同[课程框架](https://wcng60ba718p.feishu.cn/wiki/XtfswiCCQiKHdgkev4Qc1Nqined#share-LFnSdUOGPoNj6JxMIkwcQ5vEnHh)。- 作答计时：复用题型组件。- 每题独立正计时（格式 00:00，上限 59:59）。- 学生未作答退出，暂停计时，下次进入后在上次计时基础上继续计时（例一道题先看了 5 分钟没作答，退出后再进来，从 5 分钟开始计时）。- 作答进度：- 进度展示：- 分母：预估学生作答数量。- 分子：依据推题策略，- 如果学生答对，进度涨。- 如果学生答错，但没有相似题，进度涨。- 如果学生答错，但有相似题需要再练一道，进度不变。- 连对触发效果：- **同巩固练习，**[PRD -  巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f#share-RaX7dZ4RTo1goOxLPVjc5k4vnsd)**。**<br/>- **题型组件：**- 题型组件：各题型在练习中的展示和交互，见[PRD -  题型支持 - 一期](https://wcng60ba718p.feishu.cn/wiki/CXIWw1jTriZkiJkWUQ9cVFyBnBd)。- 题目推荐：见课中题目推荐策略ou_b2709be118f67c0f1ae49e028241afd2，见[PRD - 课中练习推题策略 - V1.0](https://wcng60ba718p.feishu.cn/wiki/VMEawv89PiINjUkaLxncnSYWnqb?source_type=message&from=message&disposable_login_token=eyJ1c2VyX2lkIjoiNzQ1MzA0MDU3OTczMjQ1NTQyNiIsImRldmljZV9sb2dpbl9pZCI6Ijc0NjkzNjk2MDgxOTgzNTY5OTQiLCJ0aW1lc3RhbXAiOjE3NDU3MzMyNDUsInVuaXQiOiJldV9uYyIsInB3ZF9sZXNzX2xvZ2luX2F1dGgiOiIxIiwidmVyc2lvbiI6InYzIiwidGVuYW50X2JyYW5kIjoiZmVpc2h1IiwicGtnX2JyYW5kIjoi6aOe5LmmIn0=.84d78b821498de18a6f5eba14341bd8e413fef02d2cd2cf9739cfb49f4e1b36f)。<br/>- **勾画组件：同巩固练习，**[PRD -  巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f#share-RaX7dZ4RTo1goOxLPVjc5k4vnsd)**。**<br/>- **错题本组件：同巩固练习，**[PRD -  巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f#share-WdgqdEfPKo1X4BxYtJ3c6FXgnhh)**。**<br/>- **答疑组件：**- **入口：**- 右上角常驻展示「问一问」.- 长按题目内容，唤起问一问。**（本期不做）**- 可触发时机：- 题目进入解析状态前屏蔽问一问入口。- 题目进入解析状态才能选中的唤起问一问。- **交互：**- 点击问一问，进入答疑组件。<br/></td><td>![in_table_A0MHb8kW1o0HTPx3oDjcmw8Unfg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_A0MHb8kW1o0HTPx3oDjcmw8Unfg.png)<br/><br/>![in_table_GhmPbn3b9ovENsxSfWZctrAEnGb](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GhmPbn3b9ovENsxSfWZctrAEnGb.png)<br/><br/></td></tr>
<tr>
<td>题目间转场<br/></td><td>- **逻辑和展示同巩固练习，**[PRD -  巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f#share-Sf2FdTzLqoUffPxG0hccJShLnKb)**。**<br/><br/></td><td>![in_table_GFWobx4zHo13YlxhwhlccN3GnRe](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GFWobx4zHo13YlxhwhlccN3GnRe.png)<br/>![in_table_BosmbvrKeo8580x9RqycMEZ7nYb](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BosmbvrKeo8580x9RqycMEZ7nYb.png)<br/></td></tr>
</table>

![in_table_XwKpbrJtaoPYEIxbkDbccUBKnYd]

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

此图片为一个在线教育课程学习界面截图。

**1. 关键元素与层级结构**

该界面主要由两部分组成：左侧的内容展示区和右侧的课程大纲导航区。

*   **内容展示区 (左侧)**
    *   **页面标题**: "等差数列的表示方法"
    *   **内容模块1**: "表示方法选择"
        *   文本描述
    *   **内容模块2**: "递推公式"
        *   文本描述
        *   公式展示1
        *   公式展示2
        *   公式展示3
    *   **底部提示**: "即将进入练习" (按钮样式)
*   **课程大纲导航区 (右侧)**
    *   列表项 (垂直排列，带状态指示)
        *   "课程引入" (已完成状态)
        *   "复数的分类" (已完成状态)
        *   "复数相等的条件" (当前学习项，附带播放进度 "00:32 / 02:44")
        *   "随堂练习1" (未解锁/待学习状态)
        *   "标题字数多折行展示效果如图" (未解锁/待学习状态)
        *   "随堂练习2" (未解锁/待学习状态)
        *   "复数的分类" (未解锁/待学习状态)
        *   "完成" (标记课程结束或最终状态)

**2. 组成部分与功能模块**

*   **内容展示区：**
    *   **页面标题 (等差数列的表示方法):**
        *   功能概述：标明当前学习内容的主题。
    *   **内容模块 - 表示方法选择:**
        *   功能概述：解释选择等差数列表示方法的原因和考虑。
    *   **内容模块 - 递推公式:**
        *   功能概述：解释等差数列的递推公式定义。
        *   包含常数 \( d \)的说明。
    *   **公式展示:**
        *   功能概述：以数学公式形式展示等差数列的递推关系。包含以下三个公式：
            1.  $$ a_{n+1} - a_n = d $$
            2.  $$ a_n - a_{n-1} = d \quad (n \geq 2) $$
            3.  $$ a_n - a_{n-1} = d \quad (n \geq 2) $$
    *   **底部提示/按钮 (即将进入练习):**
        *   功能概述：提示用户当前内容学习完毕，即将进入练习环节，可能作为导航按钮使用。
*   **课程大纲导航区：**
    *   **课程引入:**
        *   功能概述：课程的起始导入部分，标记为已完成。
    *   **复数的分类 (第一处):**
        *   功能概述：关于复数分类的教学点，标记为已完成。
    *   **复数相等的条件:**
        *   功能概述：关于复数相等条件的教学点，标记为当前正在学习，并显示当前内容的播放进度（00:32 / 02:44）。
    *   **随堂练习1:**
        *   功能概述：课程内的第一个练习环节，标记为未解锁或待进行。
    *   **标题字数多折行展示效果如图:**
        *   功能概述：一个教学点或示例说明，关于标题文字过长时的换行显示效果，标记为未解锁或待进行。
    *   **随堂练习2:**
        *   功能概述：课程内的第二个练习环节，标记为未解锁或待进行。
    *   **复数的分类 (第二处):**
        *   功能概述：另一个关于复数分类的教学点（或与前一个相关联），标记为未解锁或待进行。
    *   **完成:**
        *   功能概述：标记整个课程或当前模块的学习已结束。

【============== 图片解析 END ==============】

![in_table_L4WcbI0zAoClQpx9hBmcJNjXnkd]

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

本文档中的图片展示了多个教育应用界面的截图，主要涵盖了学习内容展示和练习答题两个核心场景。

**一、图片关键元素与组成部分层级结构**

该图片由以下主要界面截图组成，共同构成了一个在线教育应用的典型用户学习和练习流程：

1.  **学习内容展示界面 (主要为桌面或平板视图)**
    *   **1.1 标题区域:**
        *   如 “进入练习”、“等差数列的实例引入”。
    *   **1.2 内容区域:**
        *   **1.2.1 数学公式展示:** 如等差数列通项公式。
        *   **1.2.2 文本解释:** 对公式、概念或例题进行说明。
        *   **1.2.3 辅助插图/动画:** 如卡通人物对话、“老师”形象视频区。
        *   **1.2.4 实例列举:** 如“2022年9月每个星期五的日期...构成数列”。
    *   **1.3 交互控件:**
        *   返回按钮 (← 图标，OCR识别为G)。
        *   功能按钮 (“问”图标、“三”线菜单图标)。
        *   引导按钮 (如“即将进入练习”)。

2.  **练习与反馈界面 (主要为移动端视图)**
    *   **2.1 状态与导航栏:**
        *   设备状态栏 (时间、网络、电量)。
        *   返回按钮 (“<” 图标)。
        *   计时器 (如“用时01:24”)。
    *   **2.2 题目展示区域:**
        *   **2.2.1 题目类型与来源:** (如“单选 (2024春・浙江期中)”)。
        *   **2.2.2 题干:** 文字描述及几何图形。
        *   **2.2.3 选项列表:** (如 A, B, C, D 选项，包含选项内容和选择该选项的用户百分比)。
    *   **2.3 答题反馈与解析区域:**
        *   **2.3.1 正确答案显示:** (如“正确答案 B”)。
        *   **2.3.2 题目解析内容区:** (显示“题目解析”及解析文本)。
    *   **2.4 操作按钮:**
        *   “加入错题本”。
        *   “继续”。
    *   **2.5 过渡/激励元素:**
        *   卡通形象与提示语 (如“让我们进入练习空间几何~”)，通常叠加在题目或结果页之上。

**二、各组成部分功能模块概述**

*   **1. 导航栏模块:**
    *   **返回按钮 (←, <):** 功能：允许用户返回上一界面或步骤。
    *   **菜单/帮助按钮 (问, 三):** 功能：提供访问应用内帮助、目录或其他功能的入口。
*   **2. 内容学习模块:**
    *   功能：呈现教学内容，包括核心概念、公式推导、图文解释、实例展示和教师讲解视频。
*   **3. 数学公式显示模块:**
    *   功能：清晰展示数学公式及其适用条件。
    *   示例公式:
        $$
        a_n = a_1 + (n-1)d
        $$
        条件: $n \ge 2$, 及 $n=1$ 时也成立。
*   **4. 练习题呈现模块:**
    *   功能：展示练习题目，包括题目类型、来源、题干文字、几何图形、计时信息以及可供选择的答案选项。
    *   **选项格式示例:** A $(0, \pi/6]$
*   **5. 答题交互模块:**
    *   功能：允许用户选择答案，并显示各选项的选择分布情况（百分比）。
*   **6. 练习反馈与解析模块:**
    *   功能：在用户提交答案后，显示正确答案、详细的题目解析内容。
*   **7. 用户操作模块 (练习后):**
    *   **加入错题本按钮:** 功能：允许用户将当前题目收藏到错题本中，以备后续复习。
    *   **继续按钮:** 功能：引导用户进行下一题练习或学习后续内容。
*   **8. 激励与引导模块:**
    *   功能：通过卡通形象和提示语，在特定节点（如进入练习前）给予用户激励或引导。
*   **9. 系统状态显示模块:**
    *   功能：显示当前设备的基本状态信息，如时间、网络连接和电池电量。

**三、数学公式描述**

图片中包含的数学公式为等差数列的通项公式：
$$
a_n = a_1 + (n-1)d
$$
其中，$a_n$ 为数列的第 $n$ 项，$a_1$ 为首项，$d$ 为公差。该公式在 $n \ge 2$ 时成立，并且当 $n=1$ 时也成立。

题目选项中也包含数学区间表示，例如：
$(0, \pi/6]$

**四、流程图描述**

此图片本身主要为UI界面截图，并非流程图、时序图等标准图表。它通过展示不同状态的界面，暗示了一个在线学习和练习的用户流程，但图片本身不使用Mermaid等语法直接绘制流程。如果需要描绘其隐含的流程，可以抽象为：

```mermaid
graph TD
    A[学习内容展示] -->|点击“即将进入练习”或自动跳转| B(练习引导/过渡);
    B --> C[题目作答界面];
    C -- 用户选择答案 --> D[答案反馈与解析界面];
    D -->|点击“继续”| C;
    D -->|点击“加入错题本”| E((错题本记录));
    E --> C;
```
这个Mermaid图是基于图片内容推导的可能的用户流程，并非图片直接呈现的图表。

【============== 图片解析 END ==============】

![in_table_A0MHb8kW1o0HTPx3oDjcmw8Unfg]

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

作为互联网产品经理，此图片展示了一个在线教育产品中的答题界面。

**1. 关键元素与层级结构分析**

该界面可以层级化地理解为：

*   **顶层应用状态栏与导航区**
    *   系统状态信息（时间、网络、电量）
    *   返回操作区
    *   计时器与进度条区
    *   课程进度查看入口
*   **核心内容区：题目与作答**
    *   题目信息区
        *   题型与来源标识
        *   题干文本描述
        *   辅助几何图形
    *   选项作答区
        *   选项列表
*   **底部操作区**
    *   辅助功能按钮（勾画）
    *   交互按钮（不确定、提交）

**2. 功能模块拆解**

*   **状态栏信息模块:**
    *   **功能概述:** 显示当前设备的基础状态。
    *   包含元素:
        *   时间显示: "7:35 Mon Jun 3"
        *   网络/电量指示: "100%"
*   **顶部导航与进度模块:**
    *   **功能概述:** 提供返回、计时、进度展示及课程整体进度查看功能。
    *   包含元素:
        *   返回按钮: 图标为 "<"。功能为返回上一界面。
        *   用时显示: "用时01:24"。功能为显示当前答题所用时间。
        *   进度条: 橙色条状。功能为可视化展示当前答题进度或时间消耗。
        *   课程进度按钮: "课程进度"。功能为跳转至课程进度查看页面。
*   **题目内容模块:**
    *   **功能概述:** 展示题目的详细信息。
    *   包含元素:
        *   题目元信息: "单选 (2024春·浙江期中)"。功能为说明题目类型及来源。
        *   题干描述: "在四面体ABCD中，BCD为等边三角形ADB=pai/2，二面角B-AD-C的大小为a，则a的取值范围是（ ）"。功能为呈现题目问题。
        *   几何图形: 一个四面体ABCD的示意图。功能为辅助理解题意。
        *   图片查看器（隐性）：几何图形右下角的放大镜图标，提示图片可放大查看。
*   **选项模块:**
    *   **功能概述:** 提供选择题的选项。
    *   包含元素:
        *   选项A: "A (0, pai/6]"
        *   选项B: "B (0, pai/6]"
        *   选项C: "C (0, pai/6]"
        *   选项D: "D (0, pai/6]"
*   **操作交互模块:**
    *   **功能概述:** 提供答题过程中的辅助操作及最终提交。
    *   包含元素:
        *   勾画按钮: "勾画"。功能为可能提供在题目区域进行标注或草稿的功能。
        *   不确定按钮: "不确定"。功能为标记此题为不确定，方便后续检查。
        *   提交按钮: "提交"。功能为提交当前题目答案。

**3. 数学公式描述**

图片中的题干包含数学表达：
在四面体ABCD中，BCD为等边三角形，$\angle ADB = \pi/2$，二面角B-AD-C的大小为a，则a的取值范围是（ ）

图片中的选项包含数学区间：
A. $(0, \pi/6]$
B. $(0, \pi/6]$
C. $(0, \pi/6]$
D. $(0, \pi/6]$

【============== 图片解析 END ==============】

![in_table_GhmPbn3b9ovENsxSfWZctrAEnGb]

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

此图片为在线教育产品中的题目作答与解析界面。

**一、 图片关键元素及组成部分层级化结构**

该界面主要由以下几个部分构成：

1.  **顶部状态与导航栏**
    *   1.1. 系统状态栏：显示时间（7:35 Mon Jun 3）、网络信号、电量（100%）。
    *   1.2. 应用导航栏：
        *   1.2.1. 返回按钮（向左箭头图标）。
        *   1.2.2. 答题用时：“用时 01:24”。
        *   1.2.3. 答题进度/状态：“连对5题”及一个进度条。
        *   1.2.4. 辅助功能按钮：“问一问”。
        *   1.2.5. 课程导航按钮：“课程进度”。

2.  **题目内容区域**
    *   2.1. 题目信息：
        *   2.1.1. 题目类型：“单选”。
        *   2.1.2. 题目来源：“(2024春·浙江期中)”。
    *   2.2. 题干：
        *   2.2.1. 文字描述：“在四面体ABCD中，BCD为等边三角形ADB=pai/2，二面角B-AD-C的大小为a，则a的取值范围是（）”。
        *   2.2.2. 辅助图形：一个四面体ABCD的示意图。

3.  **选项与作答反馈区域**
    *   3.1. 选项A：
        *   3.1.1. 内容：“(0, pai/6]”。
        *   3.1.2. 选择比例：“20.9%”。
    *   3.2. 选项B：
        *   3.2.1. 内容：“(0, pai/6]”。
        *   3.2.2. 选择比例：“10.8%”。
        *   3.2.3. 用户选择与正确性标识：绿色高亮背景，左侧有星形标记，右侧有绿色勾选标记（表示用户选择此项且正确）。
    *   3.3. 选项C：
        *   3.3.1. 内容：“(0, pai/6]”。
        *   3.3.2. 选择比例：“50.2%”。
    *   3.4. 选项D：
        *   3.4.1. 内容：“(0, pai/6]”。
        *   3.4.2. 选择比例：“18.1%”。

4.  **答案与解析区域**
    *   4.1. 正确答案：“正确答案：B”。
    *   4.2. 题目解析标题：“题目解析”。
    *   4.3. 题目解析内容占位文本。

5.  **底部操作按钮区域**
    *   5.1. “加入错题本”按钮。
    *   5.2. “继续”按钮。

**二、 各组成部分功能模块说明**

*   **系统状态栏**:
    *   功能概述: 显示当前设备的基本状态信息，如时间、网络连接和电池电量。
*   **应用导航栏**:
    *   功能概述: 提供应用内的基本导航和状态展示。
    *   **返回按钮**: 允许用户返回到前一个界面。
    *   **答题用时**: 显示用户在当前答题会话或题目上花费的时间。
    *   **答题进度/状态 ("连对5题")**: 展示用户当前的答题表现，如连续答对题数和相应的视觉进度。
    *   **辅助功能按钮 ("问一问")**: 提供用户寻求题目相关帮助或提问的入口。
    *   **课程导航按钮 ("课程进度")**: 允许用户查看或跳转到其整体课程学习进度。
*   **题目内容区域**:
    *   功能概述: 展示当前题目的详细信息。
    *   **题目信息 (类型、来源)**: 标明题目的性质（如单选题）和出处。
    *   **题干 (文字描述、辅助图形)**: 呈现题目问题和相关的图示。
*   **选项与作答反馈区域**:
    *   功能概述: 列出题目的所有选项，并显示用户的选择、正确性及选项统计数据。
    *   **选项内容**: 显示每个备选项的具体文本。
    *   **选择比例**: 展示其他用户选择该选项的百分比统计。
    *   **用户选择与正确性标识**: 明确指出用户的选择，并通过视觉方式（如高亮、图标）反馈其选择是否正确。
*   **答案与解析区域**:
    *   功能概述: 公布正确答案并提供题目的详细解析。
    *   **正确答案**: 直接显示该题的正确选项。
    *   **题目解析**: 提供对题目的分析和解题思路。
*   **底部操作按钮区域**:
    *   功能概述: 提供用户完成当前题目后的后续操作选项。
    *   **"加入错题本"按钮**: 允许用户将当前题目收藏到错题本中，便于后续复习。
    *   **"继续"按钮**: 引导用户进入下一题或进行下一阶段的学习。

**三、 数学公式描述**

图片中的题目表述及选项包含数学符号：

题干中：角ADB的大小为 `\(\pi/2\)`，二面角B-AD-C的大小为 `\(a\)`。
选项的取值范围格式为 `\((0, \pi/6]\)`。

*   选项A: `\((0, \pi/6]\)`
*   选项B: `\((0, \pi/6]\)`
*   选项C: `\((0, \pi/6]\)`
*   选项D: `\((0, \pi/6]\)`

【============== 图片解析 END ==============】

![in_table_GFWobx4zHo13YlxhwhlccN3GnRe]

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

**一、图片关键元素与组成结构**

该图片为一个在线教育产品中，用户完成一道单选题后的答题结果反馈界面。其整体结构可以划分为顶部区域和内容区域。顶部区域主要承载导航、计时及进度等辅助信息；内容区域是核心交互区，依次展示了题目详情、用户的答题选项情况、系统给出的即时反馈与题目解析，并提供了后续操作入口。各组成部分共同构成了答题结果的完整呈现。

其主要由以下层级结构组成：

1.  **顶部区域 (Top Area)**
    1.1. 设备状态栏 (Device Status Bar)
    1.2. 应用内导航与信息栏 (In-App Navigation & Information Bar)
        1.2.1. 返回按钮 (Back Button)
        1.2.2. 答题用时 (Time Spent)
        1.2.3. 答题进度条 (Progress Bar)
2.  **内容区域 (Content Area)**
    2.1. 题目模块 (Question Module)
        2.1.1. 题目类型及来源 (Question Type & Source)
        2.1.2. 题干文本 (Question Stem Text)
        2.1.3. 题干附图 (Question Figure)
            2.1.3.1. 几何图形 (Geometric Diagram)
            2.1.3.2. 查看控件 (View Control)
    2.2. 选项模块 (Options Module)
        2.2.1. 选项列表 (Option List)
            2.2.1.1. 选项A (Option A)
            2.2.1.2. 选项B (Option B - User's Correct Answer)
            2.2.1.3. 选项C (Option C)
            2.2.1.4. 选项D (Option D)
    2.3. 反馈与解析模块 (Feedback & Explanation Module)
        2.3.1. 答题结果反馈 (Answer Result Feedback)
            2.3.1.1. 视觉反馈 (Visual Feedback - Cartoon)
            2.3.1.2. 文字反馈 (Textual Feedback)
        2.3.2. 题目解析 (Question Explanation)
            2.3.2.1. 正确答案标识 (Correct Answer Indicator)
            2.3.2.2. 解析标题 (Explanation Title)
            2.3.2.3. 解析内容区域 (Explanation Content Area)
    2.4. 操作模块 (Action Module)
        2.4.1. 加入错题本按钮 (Add to Error Book Button)
        2.4.2. 继续按钮 (Continue Button)

**二、各组成部分功能模块说明**

*   **1. 顶部区域 (Top Area)**
    *   **1.1. 设备状态栏 (Device Status Bar)**
        *   系统时间: (显示 "7:35 Mon Jun 3")
            *   功能概述: 显示当前设备的日期和时间。
        *   网络状态: (WiFi 图标)
            *   功能概述: 显示设备的网络连接状态。
        *   电量指示: (显示 "100%" 和电池图标)
            *   功能概述: 显示设备的剩余电量。
    *   **1.2. 应用内导航与信息栏 (In-App Navigation & Information Bar)**
        *   **1.2.1. 返回按钮:** (图标为"<")
            *   功能概述: 允许用户返回到前一个界面。
        *   **1.2.2. 答题用时:** (文字 "用时01:24")
            *   功能概述: 显示用户解答当前题目所花费的时间。
        *   **1.2.3. 答题进度条:** (橙色和灰色构成的条形指示器)
            *   功能概述: 可视化展示用户在当前练习或测试中的完成进度。

*   **2. 内容区域 (Content Area)**
    *   **2.1. 题目模块 (Question Module)**
        *   **2.1.1. 题目类型及来源:**
            *   题型: (文字 "单选")
                *   功能概述: 标明该题为单项选择题。
            *   来源/标签: (文字 "(2024春·浙江期中)")
                *   功能概述: 提供题目的来源信息或分类标签。
        *   **2.1.2. 题干文本:** (文字 "在四面体ABCD中，BCD为等边三角形ADB=pai/2，二面角B-AD-C的大小为a，则a的取值范围是（ ）")
            *   功能概述: 呈现题目的具体问题描述和条件。
        *   **2.1.3. 题干附图 (Question Figure)**
            *   **2.1.3.1. 几何图形:** (一个四面体的线框图，标注有B、C点)
                *   功能概述: 提供与题目相关的几何图形，以辅助理解。
            *   **2.1.3.2. 查看控件:** (几何图形右下角的放大镜图标)
                *   功能概述: 用户可能通过此控件放大查看图形细节。

    *   **2.2. 选项模块 (Options Module)**
        *   **2.2.1. 选项列表 (Option List):** 每个选项均包含选项标识（A/B/C/D）、选项内容和选择该选项的百分比。
            *   **选项A:** (文字 "A (0, pai/6]", 百分比 "20.9%")
                *   功能概述: 展示选项A的内容和选择该选项的统计比例。
            *   **选项B:** (文字 "B (0, pai/6]", 百分比 "10.8%", 带有绿色对勾图标和星型标记，背景为浅绿色)
                *   功能概述: 展示选项B的内容和选择该选项的统计比例。特殊样式标明这是用户选择的正确答案。
            *   **选项C:** (文字 "C (0, pai/6]", 百分比 "50.2%")
                *   功能概述: 展示选项C的内容和选择该选项的统计比例。
            *   **选项D:** (文字 "D (0, pai/6]", 百分比 "18.1%")
                *   功能概述: 展示选项D的内容和选择该选项的统计比例。

    *   **2.3. 反馈与解析模块 (Feedback & Explanation Module)**
        *   **2.3.1. 答题结果反馈 (Answer Result Feedback)**
            *   **2.3.1.1. 视觉反馈:** (一个卡通小鹿角色，竖起大拇指)
                *   功能概述: 通过积极的卡通形象给予用户鼓励。
            *   **2.3.1.2. 文字反馈:** (文字 "太棒了！同学巩固得很牢固")
                *   功能概述: 对用户答题表现给予肯定的文字评价。
        *   **2.3.2. 题目解析 (Question Explanation)**
            *   **2.3.2.1. 正确答案标识:** (文字 "B"，位于"题目解析"标题上方)
                *   功能概述: 清晰标示出该题的正确选项。
            *   **2.3.2.2. 解析标题:** (文字 "题目解析")
                *   功能概述: 作为题目解析内容的起始标识。
            *   **2.3.2.3. 解析内容区域:** (包含占位文本 "题目解析题目解析..."，下方有可见的滚动条的一部分)
                *   功能概述: 用于展示题目的详细解答过程、思路分析或知识点讲解。滚动条暗示内容较多可滑动查看。

    *   **2.4. 操作模块 (Action Module)**
        *   **2.4.1. 加入错题本按钮:** (白色背景，文字 "加入错题本")
            *   功能概述: 允许用户将当前题目收藏到个人错题本中，供后续复习。
        *   **2.4.2. 继续按钮:** (绿色背景，文字 "继续")
            *   功能概述: 用户点击后可进入下一题目或流程。

**三、数学公式描述**

图片中的题干和选项包含数学表达式。根据常规用法，“pai”指代圆周率 $\pi$。

1.  题干中的数学条件 "ADB=pai/2" 表示：
    $$
    \angle ADB = \frac{\pi}{2}
    $$

2.  选项内容均为 "(0, pai/6]"，表示变量a的取值范围：
    $$
    a \in (0, \frac{\pi}{6}]
    $$
    其中，选项A, B, C, D 对应的数学表达式均为 $(0, \frac{\pi}{6}]$。

【============== 图片解析 END ==============】

![in_table_BosmbvrKeo8580x9RqycMEZ7nYb]

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

As an internet product manager specializing in the internet education domain, this image depicts a user interface screen from an educational application, likely a quiz or practice module, shown after a user has submitted an answer.

**1. Overall Structure of the Interface:**

The screen is hierarchically organized into distinct sections:
*   **Top Navigation/Status Bar:** Displays system-level information and in-app session status.
*   **Question Area:** Contains the problem statement, any accompanying diagrams, and multiple-choice options.
*   **Feedback Overlay:** A pop-up message providing immediate feedback on user performance.
*   **Post-Answer Options:** Buttons or sections for further actions like viewing solutions or proceeding.

**2. Key Elements, Composition, and Relationships (Hierarchical Breakdown):**

*   **I. Top Bar:**
    *   **A. System Status (OS Level - Topmost part of the screen):**
        *   Date Display ("Mon Jun 3")
        *   (Other typical OS icons like Wi-Fi, battery, not explicitly itemized in OCR but visually present)
    *   **B. In-App Status / Navigation Bar (Below OS Status):**
        *   **Element 1: Back Navigation Arrow (Visual):**
            *   Function: Allows the user to return to a previous screen or exit the current quiz/exercise.
        *   **Element 2: Timer ("用时 01:24"):**
            *   Function: Displays the time elapsed for the current question or session.
        *   **Element 3: Progress/Streak Indicator ("连对5题"):**
            *   Function: Shows the number of consecutively answered questions correctly, acting as a gamification and encouragement element.

*   **II. Question Area:**
    *   **A. Question Meta-Information:**
        *   **Element 1: Question Type and Source ("单选 (2024春·浙江期中)"):**
            *   Function: Identifies the question as a "单选" (Single Choice) and its origin (e.g., "2024 Spring Zhejiang Midterm Exam").
    *   **B. Problem Presentation:**
        *   **Element 1: Question Stem ("在四面体ABCD中, BCD为等边三角形ADB=pai/2, 二面角B-AD-C的大小为a, 则a的取值范围是 ( )"):**
            *   Function: Presents the mathematical problem to be solved.
            *   Mathematical content:
                $$
                \text{在四面体ABCD中, BCD为等边三角形, } \angle ADB = \pi/2 \text{, 二面角B-AD-C的大小为a, 则a的取值范围是 ( )}
                $$
        *   **Element 2: Geometric Diagram (Visual):**
            *   Function: A visual representation of the tetrahedron ABCD, with vertices B and C explicitly labeled, to aid in understanding and solving the problem.
    *   **C. Answer Choices:**
        *   **Element 1: Option A ("A (0, pai/6] 20.9%"):**
            *   Function: Displays the first answer choice, its mathematical value, and the percentage of users who selected this option.
            *   Mathematical content: $(0, \pi/6]$
        *   **Element 2: Option B ("B (0, pai/6] 10.8%"):**
            *   Function: Displays the second answer choice, its mathematical value, the percentage of users who selected this option. A green checkmark indicates this was the user's selected (and correct) answer.
            *   Mathematical content: $(0, \pi/6]$
        *   **Element 3 & 4: Other Options (Partially visible, implied C and D):**
            *   Function: Display further answer choices. One of these has a "50.2%" selection rate visible.

*   **III. Feedback Overlay (Modal Pop-up):**
    *   **A. Main Feedback Message ("太棒了！"):**
        *   Function: Provides immediate positive affirmation.
    *   **B. Streak Celebration ("连对5题！实力派666"):**
        *   Function: Reinforces the positive feedback, acknowledges the streak, and uses encouraging language.
    *   **C. Mascot Image (Animated deer giving a thumbs up):**
        *   Function: Enhances user engagement and positive reinforcement with a visual character.

*   **IV. Post-Answer Action Area (Bottom of the screen):**
    *   **A. "题目解析" (Problem Analysis) Section Label:**
        *   The text "题目解析" is repeated multiple times, suggesting it's a label for a section, possibly collapsible or scrollable, containing the detailed solution.
        *   Function: Indicates the availability of a detailed explanation for the problem.
    *   **B. "继续" (Continue) Button (Green button at the bottom right):**
        *   Function: Allows the user to proceed to the next question or the next part of the learning module.

**Relationship between elements:**
The user views the **Question Area**, selects an **Answer Choice**. Upon submission (implied), the **Feedback Overlay** appears if the answer is correct and a streak is achieved. The user can then choose to view the **"题目解析"** or click **"继续"** to move on. The **Top Bar** provides context (time, progress) throughout this interaction.

【============== 图片解析 END ==============】



# 七、数据需求

## 埋点需求

<table>
<tr>
<td>模块<br/></td><td>页面<br/></td><td>动作<br/></td><td>埋点<br/></td><td>备注<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>练习页面<br/></td><td>加载题目<br/></td><td>exercise_question_load<br/></td><td>每道题加载完成<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>练习页面<br/></td><td>提交答案<br/></td><td>exercise_answer_submit<br/></td><td>记录作答时间、答案内容、是否正确<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>练习页面<br/></td><td>点击退出按钮<br/></td><td>exercise_exit_confirm_click<br/></td><td>退出时是否保存进度<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>练习页面<br/></td><td>勾画操作<br/></td><td>drawing_action<br/></td><td>包括勾画/橡皮擦/清除/撤销/恢复<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>练习页面<br/></td><td>错题收藏/取消<br/></td><td>mistakebook_toggle_click<br/></td><td>收藏或取消收藏错题动作<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>作答后反馈页<br/></td><td>展示作答反馈<br/></td><td>answer_feedback_show<br/></td><td>正确/错误/连胜类型<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>难度变化提示页<br/></td><td>展示难度调整提示<br/></td><td>difficulty_change_feedback_show<br/></td><td>上升/下降类型<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>作答过程中<br/></td><td>展示不认真作答提示<br/></td><td>inattentive_feedback_show<br/></td><td>作答过快/异常行为触发<br/></td></tr>
</table>

## 数据存储需求

<table>
<tr>
<td>模块<br/></td><td>数据项<br/></td><td>说明<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>学生选择的答题选项<br/></td><td>包括选择内容、是否确定提交<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>每题作答时长<br/></td><td>单题独立正计时，精确到秒<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>每题作答正确/错误标识<br/></td><td>用于后续练习效果统计<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>连续正确次数<br/></td><td>记录连胜链条<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>是否触发难度上升/下降<br/></td><td>标记作答后难度变化情况<br/></td></tr>
</table>