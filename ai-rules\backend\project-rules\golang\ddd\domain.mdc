---
description: 
globs: backend/app/domain/services/**/*.py
alwaysApply: false
---
# Domain 层开发规范 (Go Kratos DDD)

## 1. 核心原则

1.  **业务核心**: Domain 层是系统业务逻辑的核心，包含所有业务规则、实体和值对象。
2.  **框架无关**: Domain 层代码应尽可能独立于具体的技术框架 (Kratos, Gin) 和基础设施 (DB, Cache)。所有外部依赖通过接口 (Ports) 定义。
3.  **高内聚**: 相关业务逻辑和数据应封装在对应的实体或聚合内。
4.  **充血模型**: 鼓励将业务逻辑封装在领域实体的方法中，而不是仅仅作为数据容器。
5.  **简洁清晰**: 代码应易于理解和维护，反映真实的业务概念。

## 2. 目录结构

```
app/{biz_module}/
└── domain/
    ├── entity/          # 领域实体定义 (structs with ID and behavior)
    ├── vo/              # 值对象定义 (structs without ID, immutable)
    ├── repository/      # 仓储接口定义 (interface)
    ├── service/         # 领域服务接口与实现 (可选, 用于跨聚合逻辑)
    ├── errors/          # 领域特定错误定义 (errors.go)
    └── consts/          # 领域特定常量定义 (consts.go)
```

*   _注意: `interface.go` 和 `struct.go` 可视复杂性决定是否拆分到子目录。简单模块可直接放在 `domain/` 下。_

## 3. 领域实体 (Entity)

-   **定义**: 具有唯一标识符 (ID) 并在生命周期中保持连续性的对象。
-   **Go实现**: 使用 `struct` 定义。
-   **包含**: 业务属性 + 业务方法。
-   **状态**: 实体状态的变更和验证逻辑应封装在其方法内部。
-   **示例**:
    ```go
    // app/user/domain/entity/user.go
    package entity

    import (
        "golang.org/x/crypto/bcrypt"
        "your_project/app/user/domain/consts"
        "your_project/app/user/domain/errors"
    )

    type User struct {
        ID        int64  `json:"id"`
        Username  string `json:"username"`
        Email     string `json:"email"`
        Password  string `json:"-"` // 不序列化密码
        Status    int32  `json:"status"`
        CreatedAt int64  `json:"createdAt"` // 毫秒时间戳
        UpdatedAt int64  `json:"updatedAt"` // 毫秒时间戳
    }

    // Activate 激活用户
    func (u *User) Activate() error {
        if u.Status == consts.UserStatusActive {
            return errors.ErrUserAlreadyActive
        }
        if u.Status == consts.UserStatusBanned {
            return errors.ErrUserBanned
        }
        
        u.Status = consts.UserStatusActive
        u.UpdatedAt = time.Now().UnixMilli()
        return nil
    }

    // ChangePassword 修改密码
    func (u *User) ChangePassword(newPassword string) error {
        if len(newPassword) < 6 {
            return errors.ErrPasswordTooShort
        }
        
        hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
        if err != nil {
            return err
        }
        
        u.Password = string(hashedPassword)
        u.UpdatedAt = time.Now().UnixMilli()
        return nil
    }

    // ValidatePassword 验证密码
    func (u *User) ValidatePassword(password string) bool {
        err := bcrypt.CompareHashAndPassword([]byte(u.Password), []byte(password))
        return err == nil
    }

    // IsActive 检查用户是否激活
    func (u *User) IsActive() bool {
        return u.Status == consts.UserStatusActive
    }
        Password  string // 通常存储哈希后的密码
        Email     string
        Status    consts.UserStatus
        CreatedAt int64
        UpdatedAt int64
    }

    // NewUser 工厂方法，用于创建新用户，可包含初始验证逻辑
    func NewUser(username, plainPassword, email string) (*User, error) {
        if username == "" || plainPassword == "" || email == "" { // 基础验证
            return nil, errors.ErrInvalidUserInput
        }
        hashedPassword, err := bcrypt.GenerateFromPassword([]byte(plainPassword), bcrypt.DefaultCost)
        if err != nil {
            return nil, errors.ErrPasswordHashingFailed
        }
        now := time.Now().Unix()
        return &User{
            Username: username,
            Password: string(hashedPassword),
            Email:    email,
            Status:   consts.UserStatusInactive, // 初始状态
            CreatedAt: now,
            UpdatedAt: now,
        }, nil
    }

    // VerifyPassword 验证密码 (业务方法)
    func (u *User) VerifyPassword(plainPassword string) bool {
        err := bcrypt.CompareHashAndPassword([]byte(u.Password), []byte(plainPassword))
        return err == nil
    }

    // Activate 激活用户 (状态变更方法)
    func (u *User) Activate() error {
        if u.Status == consts.UserStatusActive {
            return errors.ErrUserAlreadyActive
        }
        if u.Status == consts.UserStatusBanned {
            return errors.ErrUserBanned
        }
        u.Status = consts.UserStatusActive
        u.UpdatedAt = time.Now().Unix()
        return nil
    }

    // IsActive 判断是否激活 (查询方法)
    func (u *User) IsActive() bool {
        return u.Status == consts.UserStatusActive
    }
    ```

## 4. 值对象 (Value Object - VO)

-   **定义**: 没有唯一标识符，通过其属性值来定义的不可变对象。
-   **Go实现**: 使用 `struct` 定义。
-   **特点**: 不可变性 (创建后其内部状态不应改变)，相等性基于属性值。
-   **示例**:
    ```go
    // app/order/domain/vo/address.go
    package vo

    import "fmt"

    type Address struct {
        street  string
        city    string
        zipCode string
    }

    // NewAddress 工厂方法确保不变性
    func NewAddress(street, city, zipCode string) (Address, error) {
        if street == "" || city == "" || zipCode == "" {
            return Address{}, fmt.Errorf("address fields cannot be empty")
        }
        return Address{street: street, city: city, zipCode: zipCode}, nil
    }

    // Getters for fields (不允许直接修改)
    func (a Address) Street() string { return a.street }
    func (a Address) City() string   { return a.city }
    func (a Address) ZipCode() string { return a.zipCode }

    // Equals 方法用于值比较
    func (a Address) Equals(other Address) bool {
        return a.street == other.street &&
               a.city == other.city &&
               a.zipCode == other.zipCode
    }
    ```

## 5. 聚合 (Aggregate)

-   **定义**: 一组相关领域对象的集群，作为一个数据修改的单元。包含一个聚合根 (Entity) 和其他实体及值对象。
-   **聚合根 (AR)**: 聚合的入口点，是聚合内唯一允许被外部直接引用的对象。
-   **不变性**: 聚合负责维护其内部对象之间的一致性规则 (Invariants)。所有修改必须通过聚合根的方法进行。
-   **边界**: 聚合边界定义了事务的一致性范围。
-   **示例**: `Order` 可以是聚合根，包含 `OrderItem` (Entity) 和 `ShippingAddress` (VO)。对 `OrderItem` 的增删改都应通过 `Order` 的方法进行。

## 6. 仓储接口 (Repository Interface)

-   **定义**: 提供对聚合根的持久化操作接口，模拟对象的集合。
-   **位置**: 定义在 `domain/repository` 目录 (或直接在 `domain/`)。
-   **Go实现**: 使用 `interface` 定义。
-   **参数/返回值**: 必须使用领域对象 (Entity, VO)。
-   **方法**: 通常包括 `Save` (Create/Update), `Delete`, `FindByID`, `FindByXXX` 等。
-   **上下文**: 必须接收 `context.Context` 作为第一个参数，以传递 Kratos 的 tracing 和控制信号。
-   **技术无关**: 接口定义不应包含任何数据库或 ORM (如 GORM) 的细节。
-   **示例**:
    ```go
    // app/user/domain/repository/user_repository.go
    package repository

    import (
        "context"
        "your_project/app/user/domain/entity"
    )

    type UserRepository interface {
        // Save 保存用户 (创建或更新)
        Save(ctx context.Context, user *entity.User) error
        // Delete 删除用户 (通常是软删除)
        Delete(ctx context.Context, id int64) error
        // FindByID 根据ID查找用户
        FindByID(ctx context.Context, id int64) (*entity.User, error)
        // FindByUsername 根据用户名查找用户
        FindByUsername(ctx context.Context, username string) (*entity.User, error)
        // FindByEmail 根据邮箱查找用户 (示例)
        FindByEmail(ctx context.Context, email string) (*entity.User, error)
        // ListUsers 分页查询用户列表 (示例，参数可定义为VO)
        ListUsers(ctx context.Context, offset, limit int) ([]*entity.User, int64, error)
    }
    ```

## 7. 领域服务 (Domain Service)

-   **定义**: 当某个业务逻辑不适合放在任何单一实体或值对象中时 (例如涉及多个聚合的操作)，可以使用领域服务。
-   **特点**: 通常是无状态的，方法接收领域对象作为参数。
-   **使用场景**: 应该尽量少用，优先考虑将逻辑放在实体或聚合根中。
-   **示例**: 用户注册时检查用户名是否唯一，这个逻辑可以放在 `UserService` (应用服务) 中调用 `UserRepository` 实现，通常不需要单独的 `UserDomainService`。
    ```go
    // app/transfer/domain/service/transfer_service.go
    package service

    import (
        "context"
        "your_project/app/account/domain/entity"
        "your_project/app/account/domain/repository"
        "your_project/app/transfer/domain/errors"
        "your_project/pkg/money" // 假设有Money值对象
    )

    // TransferService 接口定义
    type TransferService interface {
        Transfer(ctx context.Context, fromAccountID, toAccountID int64, amount money.Money) error
    }

    // transferServiceImpl 实现
    type transferServiceImpl struct {
        accountRepo repository.AccountRepository // 依赖账户仓储
    }

    func NewTransferService(repo repository.AccountRepository) TransferService {
        return &transferServiceImpl{accountRepo: repo}
    }

    // Transfer 实现跨聚合的操作
    func (s *transferServiceImpl) Transfer(ctx context.Context, fromAccountID, toAccountID int64, amount money.Money) error {
        // 1. 获取源账户和目标账户 (调用Repository)
        fromAccount, err := s.accountRepo.FindByID(ctx, fromAccountID)
        if err != nil {
            return err // 或者转换为领域错误
        }
        toAccount, err := s.accountRepo.FindByID(ctx, toAccountID)
        if err != nil {
            return err // 或者转换为领域错误
        }

        // 2. 执行转账业务逻辑 (调用Entity方法)
        if err := fromAccount.Withdraw(amount); err != nil {
            return errors.ErrInsufficientFunds // 返回领域错误
        }
        if err := toAccount.Deposit(amount); err != nil {
            // 此处可能需要回滚之前的操作，但这通常由应用层事务处理
            // 领域服务本身不处理事务，只表达业务规则
            return errors.ErrDepositFailed
        }

        // 3. 保存变更 (由应用服务层在事务中调用Repository.Save)
        // s.accountRepo.Save(ctx, fromAccount)
        // s.accountRepo.Save(ctx, toAccount)
        return nil
    }
    ```

## 8. 领域错误 (Domain Errors)

-   **定义**: 代表业务规则校验失败或业务流程中发生的预期错误。
-   **位置**: `domain/errors/errors.go`
-   **Go实现**: 使用 Go 标准 `errors` 包 (`errors.New`, `fmt.Errorf` 结合 `%w`) 或自定义错误类型。
-   **规范**: 只定义业务相关的错误，名称清晰表达业务含义。
-   **禁止**: 包含任何技术细节或依赖外部错误类型 (如 Kratos errors, GORM errors)。
-   **示例**:
    ```go
    // app/user/domain/errors/errors.go
    package errors

    import "errors"

    var (
        // 用户相关
        ErrUserNotFound     = errors.New("user not found")
        ErrUserExists       = errors.New("user already exists")
        ErrInvalidCredentials = errors.New("invalid username or password")
        ErrUserInactive     = errors.New("user is inactive")
        ErrUserBanned       = errors.New("user is banned")
        ErrUserAlreadyActive= errors.New("user is already active")
        ErrInvalidUserInput = errors.New("invalid user input")

        // 密码相关
        ErrPasswordHashingFailed = errors.New("failed to hash password")
        ErrPasswordTooShort    = errors.New("password is too short")

        // 订单相关 (示例)
        ErrOrderNotFound      = errors.New("order not found")
        ErrOrderCannotBeCancelled = errors.New("order cannot be cancelled in current state")
        ErrOrderItemNotFound  = errors.New("order item not found")

        // 账户相关 (示例)
        ErrAccountNotFound    = errors.New("account not found")
        ErrInsufficientFunds  = errors.New("insufficient funds")
        ErrDepositFailed      = errors.New("deposit failed")
    )
    ```

## 9. 领域常量 (Domain Constants)

-   **定义**: 业务中使用的固定值或枚举。
-   **位置**: `domain/consts/consts.go`
-   **Go实现**: 使用 `const` 关键字，可配合 `iota` 或自定义类型。
-   **示例**:
    ```go
    // app/user/domain/consts/user_status.go
    package consts

    type UserStatus int32

    const (
        UserStatusUnknown  UserStatus = 0
        UserStatusInactive UserStatus = 1 // 待激活
        UserStatusActive   UserStatus = 2 // 正常
        UserStatusBanned   UserStatus = 3 // 禁用
    )

    func (s UserStatus) String() string {
        switch s {
        case UserStatusInactive: return "Inactive"
        case UserStatusActive:   return "Active"
        case UserStatusBanned:   return "Banned"
        default:               return "Unknown"
        }
    }
    ```

## 10. 其他注意事项

-   **Immutability**: 值对象应设计为不可变的。实体在方法调用中改变状态。
-   **Validation**: 输入数据的格式验证通常在接口层 (Controller/DTO) 进行，业务规则验证应在 Domain 层 (实体方法、领域服务) 进行。
-   **日志**: Domain 层应尽量减少日志记录，只记录关键的业务状态变更或错误。详细的流程日志应在 Service 层记录。
-   **依赖注入**: Domain 层不应直接依赖具体实现，所有外部依赖（如 Repository）通过接口注入。