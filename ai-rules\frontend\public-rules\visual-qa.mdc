---
description: 
globs: 
alwaysApply: false
---

## 1. 目的

本规则文档旨在指导 AI 如何进行像素级视觉比对（Pixel-Perfect Visual QA）。像素级比对是确保前端开发结果与设计稿一致性的关键步骤。通过严格的视觉比对，可以验证设计与实际页面的显示是否完全一致，确保 UI 的各项细节（如颜色、布局、边距、字体等）符合设计规范，从而提高用户体验。本规则是 Phase 4 的核心依据，AI 应严格按照以下标准进行视觉比对。

## 2. 像素级视觉比对的定义

像素级视觉比对是指通过工具和手段，逐像素比较设计稿与开发完成的页面，确保页面的每个视觉细节都与设计稿一致。这包括但不限于以下方面：

* **颜色匹配**：确保页面中所有颜色与设计稿中的色值一致。
* **尺寸和布局**：确保页面元素的尺寸、边距、定位与设计稿完全一致。
* **字体样式**：确保字体、字号、行高等文本样式符合设计要求。
* **对齐和间距**：确保元素之间的对齐和间距与设计稿对齐，避免错位或不一致。

## 3. AI 执行像素级视觉比对的流程

### 3.1 设计稿与开发版本获取

AI 必须从 Figma 或设计工具中获取最新的设计稿，并确保设计稿中包含所有元素的尺寸、颜色、字体和位置。

* **设计稿获取**：AI 自动提取设计稿并转换为结构化的视觉信息，确保页面布局、颜色、字体等与设计保持一致。
* **开发版本获取**：AI 获取前端开发的页面的实际渲染内容，准备进行比对。

### 3.2 比对内容

AI 在执行像素级比对时，必须逐个对比设计与开发页面的以下元素：

* **颜色差异**：比对页面元素的色值，确保与设计稿一致。
* **尺寸与布局**：比对页面元素的尺寸、边距和定位，确保符合设计稿的要求。
* **字体样式**：比对页面文本的字体、字号、行高、字间距等，确保与设计稿一致。
* **对齐和间距**：检查页面元素之间的对齐和间距，确保没有错位。
* **响应式设计**：AI 应确保开发页面在不同屏幕尺寸下的布局和元素显示与设计稿一致。

### 3.3 自动化反馈与修正

AI 执行视觉比对后，应自动生成差异报告，并根据以下原则向开发人员或设计团队反馈问题：

* **颜色偏差**：报告哪些元素的颜色与设计稿不一致，并提供具体的色值差异。
* **尺寸偏差**：报告元素尺寸不匹配的情况，指出具体的差异点。
* **布局问题**：报告布局偏差，指出元素错位、未对齐或间距不符的部分。
* **字体问题**：报告字体样式差异，指出字体、字号、行高等不匹配的情况。
* **响应式问题**：报告在不同设备上页面显示的问题，确保响应式设计无误。

## 4. 视觉比对中的常见问题与解决方案

### 4.1 响应式设计适配

AI 应通过跨设备测试，确保在各种屏幕尺寸下页面元素位置、尺寸和对齐与设计稿一致。

### 4.2 动态元素和动画

对于动画和动态加载的元素，AI 应通过时间轴或交互式模拟比对，确保动画效果和动态元素在设计稿与开发页面中的一致性。

### 4.3 字体渲染差异

AI 应识别操作系统的字体渲染差异，并自动调整字体样式，以确保在不同平台上的一致性。

