---
description: 
globs: 
alwaysApply: false
---
# Vue.js + Vite 项目代码生成规则

> **核心原则：简洁优先，可维护性第一**

## 边界
- 遵循 ESLint 与 Prettier 规范
- 强制类型检查，使用 TypeScript 或 JSDoc 类型注解
- 禁止在生产代码中使用 `console.log`；统一使用项目日志机制
- 禁止在组件中直接调用 API；使用 composables/service 层隔离
- 状态管理与 UI 表现分离
- 资源引用使用别名路径 `@/` 而非相对路径
- 全局样式与组件样式分离

## 约定
- 命名：变量/函数使用 `camelCase`；组件使用 `PascalCase`；常量使用 `UPPER_SNAKE`
- 文件命名：组件文件使用 `PascalCase.vue`；工具/composables 文件使用 `camelCase.js/ts`
- 导入顺序：Vue → 第三方库 → 内部组件/composables → 样式；组间空行
- 组件风格：优先使用 Composition API (setup)，而非 Options API
- 样式：优先使用 `<style scoped>` 或 CSS Modules
- 路由：集中配置，使用懒加载

## Vite 特有规范
- 静态资源引用：使用 `import` 语法而非 URL 字符串
- 环境变量：统一使用 `import.meta.env.VITE_*` 访问
- 路径别名：在 `vite.config.js` 中配置，组件引用使用 `@/components/*`
- 构建优化：预加载关键依赖，代码分割

## 组件结构
```vue
<!-- GoodComponent.vue -->
<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useStore } from 'pinia';

// 第三方库
import { format } from 'date-fns';

// 内部组件/composables
import { useApiData } from '@/composables/useApiData';
import Button from '@/components/common/Button.vue';

// 类型定义
interface Props {
  title: string;
}

// Props 定义
const props = defineProps<Props>();

// Emits 定义
const emit = defineEmits<{
  'action': [];
}>();

// 状态/composables
const isActive = ref(false);
const { data, loading } = useApiData('/endpoint');
const store = useStore();
const user = computed(() => store.user);

// 生命周期钩子
onMounted(() => {
  // 初始化逻辑
  
  // 清理逻辑
  return () => {
    // 组件卸载时执行
  };
});

// 事件处理函数
const handleClick = () => {
  isActive.value = !isActive.value;
  emit('action');
};
</script>

<template>
  <div class="container" v-if="!loading">
    <h2>{{ title }}</h2>
    <p>{{ format(new Date(), 'yyyy-MM-dd') }}</p>
    <Button @click="handleClick">
      {{ isActive ? 'Active' : 'Inactive' }}
    </Button>
  </div>
  <div v-else>Loading...</div>
</template>

<style scoped>
.container {
  padding: 16px;
  border-radius: 4px;
  background-color: #f5f5f5;
}
</style>
```

## API 请求规范
```typescript
// userService.ts
import { apiClient } from '@/utils/apiClient';
import type { User } from '@/types';

export const UserService = {
  async getUser(id: number): Promise<User> {
    try {
      const response = await apiClient.get(`/users/${id}`);
      return response.data;
    } catch (error) {
      // 统一错误处理
      throw error;
    }
  },
  
  async updateUser(id: number, data: Partial<User>): Promise<User> {
    try {
      const response = await apiClient.put(`/users/${id}`, data);
      return response.data;
    } catch (error) {
      // 统一错误处理
      throw error;
    }
  }
};
```

## 路由配置
```typescript
// router.ts
import { createRouter, createWebHistory } from 'vue-router';

// 路由定义
const routes = [
  {
    path: '/',
    component: () => import('@/views/Home.vue')
  },
  {
    path: '/about',
    component: () => import('@/views/About.vue')
  },
  {
    path: '/users/:id',
    component: () => import('@/views/UserProfile.vue'),
    props: true
  }
];

// 创建路由实例
export const router = createRouter({
  history: createWebHistory(),
  routes
});
```

## 状态管理 (Pinia)
```typescript
// userStore.ts
import { defineStore } from 'pinia';
import { UserService } from '@/services/userService';

export const useUserStore = defineStore('user', {
  state: () => ({
    data: null,
    loading: false,
    error: null
  }),
  
  getters: {
    isAdmin: (state) => state.data?.role === 'admin'
  },
  
  actions: {
    async fetchUser(userId: number) {
      this.loading = true;
      this.error = null;
      
      try {
        this.data = await UserService.getUser(userId);
      } catch (error) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    },
    
    clearUser() {
      this.data = null;
    }
  }
});
```

## Composables (自定义 Hooks)
```typescript
// useApiData.ts
import { ref, onMounted } from 'vue';
import { apiClient } from '@/utils/apiClient';

export function useApiData(endpoint: string) {
  const data = ref(null);
  const loading = ref(true);
  const error = ref(null);
  
  const fetchData = async () => {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await apiClient.get(endpoint);
      data.value = response.data;
    } catch (err) {
      error.value = err;
    } finally {
      loading.value = false;
    }
  };
  
  onMounted(fetchData);
  
  return {
    data,
    loading,
    error,
    refresh: fetchData
  };
}
```

## 测试规范
```typescript
// Button.spec.ts
import { mount } from '@vue/test-utils';
import Button from './Button.vue';

describe('Button component', () => {
  test('renders with correct text', () => {
    const wrapper = mount(Button, {
      slots: {
        default: 'Click me'
      }
    });
    
    expect(wrapper.text()).toContain('Click me');
  });

  test('emits click event when clicked', async () => {
    const wrapper = mount(Button, {
      slots: {
        default: 'Click me'
      }
    });
    
    await wrapper.trigger('click');
    expect(wrapper.emitted('click')).toBeTruthy();
  });
});
```

## Vite 配置
```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
    },
  },
  build: {
    outDir: 'dist',
    minify: 'terser',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['element-plus'], // 根据使用的 UI 库调整
        },
      },
    },
  },
});
```