---
description: 
globs: backend/tests/application/**/*.py
alwaysApply: false
---
# Application 层测试代码生成规则

**核心原则：简洁优先**

## 边界
- 仅测试 `<Noun>AppService` 公共方法（成功 + 失败路径）  
- 关注用例结果、事务行为、异常映射；不深入领域内部实现  
- 使用 SQLite 内存库或 `MagicMock Session` 保证隔离  
- 允许使用真实仓储 + 内存库，或 Fake 仓储

## 约定
- 类名 `Test{AppService}`；方法 `test_{行为}_{场景}`  
- 通过 `pytest.fixture` 或 `autouse` Fixture 创建 `AppService` 并注入依赖  
- 成功断言：返回 DTO / 状态或数据库变更  
- 失败断言：`with pytest.raises(ApplicationError)`  
- 事务校验：  
  - 若使用 `MagicMock Session` → 断言 `commit` / `rollback` 调用次数  
  - 若使用真实会话 → 通过查询验证数据是否写入 / 回滚  
- 覆盖率目标 ≥ 90 %


## 示例代码
```python
class TestUserAppService(BaseApplicationTests):
    """用户注册服务单元测试"""
    
    def test_register_success(self):
        """测试用户注册成功场景"""
        # 准备数据  
        # 执行操作        
        # 验证结果
        pass
    
    def test_register_duplicate_email(self):
        """测试注册失败场景：邮箱已存在"""
        # 准备数据
        # 执行和验证异常
        with pytest.raises(EmailAlreadyExistsError):
            pass

    def test_permission_check(self):
        """测试权限控制"""
        # 准备数据：创建普通用户和管理员
        # 创建目标资源        
        # 测试普通用户无权限访问
        with pytest.raises(PermissionDeniedError):
            pass
        
        # 测试管理员有权限访问
        pass

    def test_transaction_rollback(self):
        """测试事务管理和原子性"""
        # 准备数据        
        # 模拟依赖服务抛出异常，应导致事务回滚
        with patch.object(self.notification_service, 'send_welcome_email', side_effect=Exception("Service unavailable")):
            # 执行操作，预期抛出异常
            with pytest.raises(RegistrationFailedError):
                pass
        
        # 验证数据库操作已回滚（用户未创建）
```