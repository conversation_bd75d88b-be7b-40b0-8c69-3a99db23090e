---
description: 
globs: 
alwaysApply: false
---
# Vite 前端项目通用代码生成规则

> **核心原则：简洁优先，跨框架通用**

## 边界
- 遵循 ESLint 与 Prettier 规范配置
- 强制类型检查，优先使用 TypeScript
- 禁止在生产代码中使用 `console.log`；使用框架兼容的日志机制
- 禁止在视图层直接调用 API；使用服务层隔离
- 状态管理与 UI 表现分离
- 资源引用使用别名路径 `@/` 而非相对路径
- 全局样式与组件样式分离
- 视图路由集中管理

## 命名约定
- 变量/函数：`camelCase`
- 组件：`PascalCase`
- 常量：`UPPER_SNAKE_CASE`
- 文件命名：
  - React: `.jsx/.tsx` 使用 `PascalCase`
  - Vue: `.vue` 使用 `PascalCase`
  - 工具/服务: `.js/.ts` 使用 `camelCase`

## 文件组织结构
```
project-root/
├── public/                # 静态资源，直接复制到构建输出
├── src/
│   ├── assets/            # 需要处理的静态资源
│   ├── components/        # 通用组件
│   ├── views/             # 页面级组件
│   ├── router/            # 路由配置
│   ├── store/             # 状态管理
│   ├── services/          # API 服务
│   ├── utils/             # 工具函数
│   ├── styles/            # 全局样式
│   ├── types/             # TypeScript 类型定义
│   ├── App.(jsx|vue)      # 应用入口组件
│   └── main.(js|ts)       # 应用入口文件
├── .env                   # 环境变量
├── .eslintrc              # ESLint 配置
├── .prettierrc            # Prettier 配置
├── index.html             # HTML 模板
├── package.json           # 项目依赖
├── tsconfig.json          # TypeScript 配置
└── vite.config.(js|ts)    # Vite 配置
```

## Vite 特有规范

### 1. 静态资源处理
```javascript
// ✅ 正确: 使用 import 导入静态资源
import logo from '@/assets/logo.png';
import styles from './styles.module.css';

// ❌ 避免: 硬编码 URL
const logoUrl = '/assets/logo.png';
```

### 2. 环境变量使用
```javascript
// ✅ 正确: 使用 import.meta.env 访问环境变量
const apiUrl = import.meta.env.VITE_API_URL;

// ❌ 避免: 使用 process.env (仅在兼容模式下可用)
const apiUrl = process.env.VITE_API_URL;
```

### 3. 路径别名配置 (vite.config.js)
```javascript
import { defineConfig } from 'vite';
import path from 'path';

export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
});
```

### 4. 生产构建优化
```javascript
// vite.config.js
export default defineConfig({
  build: {
    // 分块策略
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'react', 'react-dom'], // 根据使用的框架调整
          // 其他第三方库
        },
      },
    },
    // 启用压缩
    minify: 'terser',
    // 生成 sourcemap
    sourcemap: true,
  },
});
```

## 框架通用组件规范

### React 组件示例
```jsx
// UserProfile.tsx
import React, { useState, useEffect } from 'react';
import { userService } from '@/services/userService';
import styles from './UserProfile.module.css';

interface Props {
  userId: string;
}

const UserProfile: React.FC<Props> = ({ userId }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchUser() {
      try {
        const data = await userService.getUser(userId);
        setUser(data);
      } catch (error) {
        // 错误处理
      } finally {
        setLoading(false);
      }
    }
    
    fetchUser();
  }, [userId]);

  if (loading) return <div>Loading...</div>;
  
  return (
    <div className={styles.profile}>
      <h2>{user.name}</h2>
      <p>{user.email}</p>
    </div>
  );
};

export default UserProfile;
```

### Vue 组件示例
```vue
<!-- UserProfile.vue -->
<template>
  <div class="profile" v-if="!loading">
    <h2>{{ user.name }}</h2>
    <p>{{ user.email }}</p>
  </div>
  <div v-else>Loading...</div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { userService } from '@/services/userService';

const props = defineProps<{
  userId: string;
}>();

const user = ref(null);
const loading = ref(true);

const fetchUser = async () => {
  loading.value = true;
  try {
    user.value = await userService.getUser(props.userId);
  } catch (error) {
    // 错误处理
  } finally {
    loading.value = false;
  }
};

onMounted(fetchUser);

watch(() => props.userId, fetchUser);
</script>

<style scoped>
.profile {
  /* 样式定义 */
}
</style>
```

## 服务层规范 (React/Vue 通用)

```typescript
// userService.ts
import { apiClient } from '@/utils/apiClient';
import type { User } from '@/types';

export const userService = {
  async getUser(id: string): Promise<User> {
    try {
      const response = await apiClient.get(`/users/${id}`);
      return response.data;
    } catch (error) {
      // 错误处理
      throw error;
    }
  },
  
  async updateUser(id: string, data: Partial<User>): Promise<User> {
    try {
      const response = await apiClient.put(`/users/${id}`, data);
      return response.data;
    } catch (error) {
      // 错误处理
      throw error;
    }
  }
};
```

## 路由配置

### React 路由 (React Router)
```jsx
// router/index.tsx
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { lazy, Suspense } from 'react';

// 懒加载路由组件
const Home = lazy(() => import('@/views/Home'));
const About = lazy(() => import('@/views/About'));
const UserProfile = lazy(() => import('@/views/UserProfile'));

const router = createBrowserRouter([
  {
    path: '/',
    element: <Home />,
  },
  {
    path: '/about',
    element: <About />,
  },
  {
    path: '/users/:id',
    element: <UserProfile />,
  }
]);

export function Router() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <RouterProvider router={router} />
    </Suspense>
  );
}
```

### Vue 路由 (Vue Router)
```typescript
// router/index.ts
import { createRouter, createWebHistory } from 'vue-router';

const routes = [
  {
    path: '/',
    component: () => import('@/views/Home.vue'),
  },
  {
    path: '/about',
    component: () => import('@/views/About.vue'),
  },
  {
    path: '/users/:id',
    component: () => import('@/views/UserProfile.vue'),
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;
```

## 状态管理

### React 状态管理 (Redux Toolkit)
```typescript
// store/userSlice.ts
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { userService } from '@/services/userService';

export const fetchUser = createAsyncThunk(
  'user/fetchUser',
  async (userId: string) => {
    return await userService.getUser(userId);
  }
);

const userSlice = createSlice({
  name: 'user',
  initialState: {
    data: null,
    loading: false,
    error: null,
  },
  reducers: {
    // 同步 reducers
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchUser.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchUser.fulfilled, (state, action) => {
        state.data = action.payload;
        state.loading = false;
      })
      .addCase(fetchUser.rejected, (state, action) => {
        state.error = action.error.message;
        state.loading = false;
      });
  },
});

export default userSlice.reducer;
```

### Vue 状态管理 (Pinia)
```typescript
// store/userStore.ts
import { defineStore } from 'pinia';
import { userService } from '@/services/userService';
import type { User } from '@/types';

export const useUserStore = defineStore('user', {
  state: () => ({
    user: null as User | null,
    loading: false,
    error: null as string | null,
  }),
  
  actions: {
    async fetchUser(userId: string) {
      this.loading = true;
      this.error = null;
      
      try {
        this.user = await userService.getUser(userId);
      } catch (error) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    },
  },
  
  getters: {
    userName: (state) => state.user?.name,
  },
});
```

## 测试规范

### 组件测试 (框架通用原则)
- 单元测试应该关注组件的行为，而非实现细节
- 使用框架专用的测试库
  - React: React Testing Library
  - Vue: Vue Test Utils
- 模拟 API 请求和依赖
- 测试关键用户交互流程

### React 测试示例
```jsx
// UserProfile.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import UserProfile from './UserProfile';
import { userService } from '@/services/userService';

// 模拟服务
jest.mock('@/services/userService');

describe('UserProfile', () => {
  const mockUser = { id: '1', name: 'Test User', email: '<EMAIL>' };
  
  beforeEach(() => {
    userService.getUser.mockResolvedValue(mockUser);
  });

  test('显示用户信息', async () => {
    render(<UserProfile userId="1" />);
    
    // 初始加载状态
    expect(screen.getByText('Loading...')).toBeInTheDocument();
    
    // 等待数据加载
    await waitFor(() => {
      expect(screen.getByText('Test User')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });
  });
});
```

### Vue 测试示例
```javascript
// UserProfile.test.js
import { mount, flushPromises } from '@vue/test-utils';
import UserProfile from './UserProfile.vue';
import { userService } from '@/services/userService';

// 模拟服务
jest.mock('@/services/userService');

describe('UserProfile', () => {
  const mockUser = { id: '1', name: 'Test User', email: '<EMAIL>' };
  
  beforeEach(() => {
    userService.getUser.mockResolvedValue(mockUser);
  });

  test('显示用户信息', async () => {
    const wrapper = mount(UserProfile, {
      props: {
        userId: '1',
      },
    });
    
    // 初始加载状态
    expect(wrapper.text()).toContain('Loading...');
    
    // 等待数据加载
    await flushPromises();
    
    expect(wrapper.text()).toContain('Test User');
    expect(wrapper.text()).toContain('<EMAIL>');
  });
});
```

## 性能优化共通原则

1. **代码分割**
   - 使用动态导入和懒加载
   - 路由级别代码分割
   
2. **资源优化**
   - 图片压缩和适当格式选择
   - 使用 SVG 代替图标字体
   - CSS 原子化/模块化减少冗余
   
3. **构建优化**
   - 配置依赖预构建
   - 合理设置构建缓存
   - 生产环境启用压缩和代码混淆

4. **运行时优化**
   - 避免不必要的组件重渲染
   - 使用 `React.memo` / Vue `computed` 缓存计算结果
   - 大列表虚拟化滚动

## TypeScript 最佳实践

```typescript
// types/index.ts

// 通用接口定义
export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'user';
}

// 通用类型定义
export type ApiResponse<T> = {
  data: T;
  status: number;
  message: string;
};

// 通用工具类型
export type Nullable<T> = T | null;
export type Optional<T> = T | undefined;
```

## Vite 配置最佳实践

```typescript
// vite.config.ts
import { defineConfig, loadEnv } from 'vite';
import path from 'path';

// 根据所用框架导入对应插件
// import vue from '@vitejs/plugin-vue';
// import react from '@vitejs/plugin-react';

export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd());
  
  return {
    // 插件配置
    plugins: [
      // vue(),
      // react(),
    ],
    
    // 路径解析
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    
    // 开发服务器配置
    server: {
      port: 3000,
      open: true,
      proxy: {
        '/api': {
          target: env.VITE_API_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
        },
      },
    },
    
    // 构建优化
    build: {
      target: 'es2015',
      outDir: 'dist',
      assetsDir: 'assets',
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: mode === 'production',
        },
      },
      rollupOptions: {
        output: {
          // 代码分块策略
          manualChunks: (id) => {
            if (id.includes('node_modules')) {
              return 'vendor';
            }
          },
        },
      },
      // 启用依赖预打包
      optimizeDeps: {
        include: [
          // 常用依赖
        ],
      },
    },
    
    // CSS 处理
    css: {
      // CSS 模块化
      modules: {
        scopeBehavior: 'local',
        localsConvention: 'camelCaseOnly',
      },
      // 预处理器选项
      preprocessorOptions: {
        scss: {
          additionalData: '@import "@/styles/variables.scss";',
        },
      },
    },
  };
});
```