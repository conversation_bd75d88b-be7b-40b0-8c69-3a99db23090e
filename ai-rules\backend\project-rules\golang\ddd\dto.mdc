---
description: 
globs: 
alwaysApply: false
---
# DTO (数据传输对象) 层开发规范 (Go Kratos DDD)

## 1. 核心原则

1.  **数据载体**: DTO 的主要职责是在不同层之间（特别是 Service 层与 Controller/gRPC Server 层之间）传递数据，以及作为 API 的数据契约。
2.  **边界清晰**: DTO 应只包含数据及其验证规则，不应包含任何业务逻辑。
3.  **结构扁平**: 尽量保持 DTO 结构扁平化，避免过深的嵌套，以简化客户端处理。
4.  **API 导向**: DTO 的设计应服务于 API 接口的需求，可能需要根据不同的 Use Case 定义不同的 DTO。
5.  **框架适配**: DTO 结构需要适配 Kratos 框架的特性，如 Protobuf 生成、HTTP 请求绑定和验证。

## 2. 目录结构

```
app/{biz_module}/
└── dto/
    ├── request.go     # 请求相关的 DTO 定义
    ├── response.go    # 响应相关的 DTO 定义
    └── converter.go   # Domain 对象与 DTO 之间的转换函数
    # 或者将 converter.go 的内容放在 Service 层实现中
```

*   对于简单的模块，也可以将所有 DTO 定义放在一个 `dto.go` 文件中。
*   转换函数的存放位置 (`dto/converter.go` 或 `service/*.go`) 应在项目内保持一致。

## 3. 命名规范

-   **请求 DTO**: `{UseCase}RequestDTO` 或 `{Resource}CreateRequestDTO`, `{Resource}UpdateRequestDTO`, `{Resource}ListRequestDTO` 等。
-   **响应 DTO**: `{UseCase}ResponseDTO` 或 `{Resource}ResponseDTO`, `{Resource}DetailResponseDTO`, `ListResponseDTO[T]` 等。
-   **通用 DTO**: `{Concept}DTO` (如 `UserDTO`, `OrderDTO`)。
-   **Go 结构体字段**: 使用 Go 的 `CamelCase` 命名法。

## 4. 字段定义与标签

1.  **类型**: 使用标准的 Go 数据类型。
    *   **整数**: 统一使用 `int64`，避免 JavaScript 的精度问题。
    *   **时间戳**: 统一使用 `int64`，表示 **UTC 时间戳 (秒)**。
    *   **可选字段**: 使用指针类型 (`*string`, `*int64`, `*bool` 等)。
2.  **JSON 标签**: **必须**包含 `json` 标签，并且值必须是 **小写驼峰 (lowerCamelCase)** 格式。
    ```go
    type UserDTO struct {
        UserID    int64  `json:"userId"`
        Username  string `json:"username"`
        Email     string `json:"email,omitempty"` // omitempty 用于空值时不显示
        Status    int32  `json:"status"`
        CreatedAt int64  `json:"createdAt"` // UTC 秒数
        UpdatedAt int64  `json:"updatedAt"` // UTC 秒数
    }
    ```

3.  **验证标签**: 使用 `binding` 标签进行参数验证。
    ```go
    type CreateUserRequestDTO struct {
        Username string `json:"username" binding:"required,min=3,max=20"`
        Email    string `json:"email" binding:"required,email"`
        Password string `json:"password" binding:"required,min=6"`
    }
    ```

## 5. 转换器实现

### 5.1 转换器函数命名
- Domain 到 DTO：`FromDomain{Entity}` 或 `{Entity}ToDTO`
- DTO 到 Domain：`ToDomain{Entity}` 或 `DTOTo{Entity}`

### 5.2 转换器实现示例
```go
// app/user/dto/converter.go
package dto

import (
    "your_project/app/user/domain/entity"
    "your_project/app/consts"
)

// UserToDTO 将Domain User转换为DTO
func UserToDTO(user *entity.User) *UserResponseDTO {
    if user == nil {
        return nil
    }
    
    return &UserResponseDTO{
        UserID:    user.ID,
        Username:  user.Username,
        Email:     user.Email,
        Status:    user.Status,
        CreatedAt: user.CreatedAt,
        UpdatedAt: user.UpdatedAt,
    }
}

// CreateUserRequestToEntity 将创建用户请求转换为Domain实体
func CreateUserRequestToEntity(req *CreateUserRequestDTO) *entity.User {
    if req == nil {
        return nil
    }
    
    now := time.Now().UnixMilli()
    return &entity.User{
        Username:  req.Username,
        Email:     req.Email,
        Password:  req.Password, // 注意：密码需要在Service层进行哈希处理
        Status:    consts.UserStatusInactive,
        CreatedAt: now,
        UpdatedAt: now,
    }
}

// UpdateUserRequestToEntity 将更新用户请求应用到Domain实体
func UpdateUserRequestToEntity(req *UpdateUserRequestDTO, user *entity.User) {
    if req == nil || user == nil {
        return
    }
    
    if req.Email != nil {
        user.Email = *req.Email
    }
    if req.Status != nil {
        user.Status = *req.Status
    }
    
    user.UpdatedAt = time.Now().UnixMilli()
}

// UsersToDTO 批量转换用户列表
func UsersToDTO(users []*entity.User) []*UserResponseDTO {
    if users == nil {
        return nil
    }
    
    result := make([]*UserResponseDTO, 0, len(users))
    for _, user := range users {
        if dto := UserToDTO(user); dto != nil {
            result = append(result, dto)
        }
    }
    
    return result
}
```

### 5.3 分页转换器
```go
// ListUsersResponseToDTO 转换分页用户列表响应
func ListUsersResponseToDTO(users []*entity.User, total int64, page, pageSize int32) *ListUsersResponseDTO {
    return &ListUsersResponseDTO{
        Users: UsersToDTO(users),
        PageInfo: &PageInfoDTO{
            Page:       page,
            PageSize:   pageSize,
            Total:      total,
            TotalPages: int32((total + int64(pageSize) - 1) / int64(pageSize)),
        },
    }
}
```

## 6. 完整DTO示例

### 6.1 请求DTO
```go
// CreateUserRequestDTO 创建用户请求
type CreateUserRequestDTO struct {
    Username string `json:"username" binding:"required,min=3,max=20"`
    Email    string `json:"email" binding:"required,email"`
    Password string `json:"password" binding:"required,min=6"`
}

// UpdateUserRequestDTO 更新用户请求
type UpdateUserRequestDTO struct {
    UserID int64   `json:"userId" binding:"required"`
    Email  *string `json:"email,omitempty" binding:"omitempty,email"`
    Status *int32  `json:"status,omitempty"`
}

// ListUsersRequestDTO 用户列表请求
type ListUsersRequestDTO struct {
    Page     int32  `json:"page" binding:"min=1"`
    PageSize int32  `json:"pageSize" binding:"min=1,max=100"`
    Keyword  string `json:"keyword,omitempty"`
    Status   *int32 `json:"status,omitempty"`
}
```

### 6.2 响应DTO
```go
// UserResponseDTO 用户响应
type UserResponseDTO struct {
    UserID    int64  `json:"userId"`
    Username  string `json:"username"`
    Email     string `json:"email"`
    Status    int32  `json:"status"`
    CreatedAt int64  `json:"createdAt"`
    UpdatedAt int64  `json:"updatedAt"`
}

// ListUsersResponseDTO 用户列表响应
type ListUsersResponseDTO struct {
    Users    []*UserResponseDTO `json:"users"`
    PageInfo *PageInfoDTO       `json:"pageInfo"`
}

// PageInfoDTO 分页信息
type PageInfoDTO struct {
    Page       int32 `json:"page"`
    PageSize   int32 `json:"pageSize"`
    Total      int64 `json:"total"`
    TotalPages int32 `json:"totalPages"`
}
```

### 6.3 通用响应DTO
```go
// GenericResponse 通用响应结构
type GenericResponse struct {
    Code    int32       `json:"code"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
    Code    int32  `json:"code"`
    Message string `json:"message"`
    Detail  string `json:"detail,omitempty"`
}
```

## 7. 开发要求

### 7.1 技术要求
- 所有int类型使用int64
- 时间字段统一使用UTC毫秒时间戳，int64类型
- JSON标签使用小驼峰命名
- 添加适当的validation标签
- 可选字段使用指针类型

### 7.2 质量要求
- 转换器函数必须处理nil值
- 批量转换要考虑性能
- 添加详细的中文注释
- 遵循项目编码规范
        Role      *string `json:"role,omitempty"` // 可选字段
    }
    ```
3.  **验证标签**: 请求 DTO **必须**使用验证标签来定义字段约束。Kratos 通常与 Gin 结合使用 HTTP 服务，推荐使用 `binding` 标签 (基于 `go-playground/validator/v10`)。
    ```go
    type CreateUserRequestDTO struct {
        Username string `json:"username" binding:"required,min=3,max=32"`
        Password string `json:"password" binding:"required,min=6,max=64"`
        Email    string `json:"email" binding:"required,email"`
        Age      *int64 `json:"age,omitempty" binding:"omitempty,gt=0"` // 可选，但若提供必须大于0
    }
    ```
    *   常用的 `binding` 标签包括: `required`, `omitempty`, `min`, `max`, `len`, `email`, `url`, `uuid`, `gt`, `gte`, `lt`, `lte`, `oneof`, `numeric`, `alpha`, `alphanum` 等。
    *   对于复杂的跨字段验证，可以在 DTO 上实现 `Validate() error` 方法 (但不推荐在此进行业务规则验证)。
4.  **其他标签**: 根据需要可添加 `form` (用于 form-data), `uri` (用于路径参数) 等标签。

## 4.1. ID 字段与生成策略

-   **ID 类型**: 业务实体的主键 ID，如果由后端生成，推荐使用 `int64` 类型（例如雪花算法生成的ID）或 `string` 类型（例如 UUID）。
    -   如果使用雪花算法，确保生成的是标准雪花ID (64位整数)。
    -   如果使用 UUID，推荐使用 UUID v4。
-   **ID 传递**:
    -   创建操作的请求 DTO (`Create...RequestDTO`) 通常 **不包含** ID 字段，因为 ID 由后端生成。
    -   更新/查询/删除操作的请求 DTO 通常需要包含 ID 字段作为路径参数或请求体参数。
    -   响应 DTO (`...ResponseDTO`, `...DetailResponseDTO`) **必须包含** ID 字段。
-   **命名**: ID 字段名在 DTO 中统一使用 `xxxID` 的形式，例如 `userID`, `orderID`。JSON tag 对应为 `xxxId`，例如 `userId`, `orderId`。
    ```go
    type ProductDTO struct {
        ProductID   int64  `json:"productId"` // 雪花算法生成的ID
        Name        string `json:"name"`
        // ... 其他字段
    }

    type CreateOrderRequestDTO struct {
        UserID    int64   `json:"userId" binding:"required"`
        ItemIDs   []int64 `json:"itemIds" binding:"required,min=1"`
        // ... 其他字段 (不包含 orderId)
    }

    type OrderDTO struct {
        OrderID string `json:"orderId"` // UUID 生成的ID
        UserID  int64  `json:"userId"`
        // ... 其他字段
    }
    ```
-   **前端交互**:
    -   若 ID 为 `int64` (如雪花ID)，直接作为数字传递给前端。
    -   若 ID 为 `string` (如 UUID)，作为字符串传递。
    -   **避免** 将 `int64` 类型的雪花ID在传输给前端时转换为字符串，除非前端明确有此要求（例如担心JavaScript的Number精度问题，但对于标准64位整数通常不是问题）。如果确实需要转换，应在项目内明确约定并统一处理。

## 5. 与 Protobuf 的关系

-   Kratos 推荐使用 Protobuf 定义 API 接口 (`.proto` 文件)。
-   `make api` 会根据 `.proto` 文件生成 Go 代码 (`*.pb.go`)，包含对应的 Go 结构体。
-   **通常不直接使用** Protobuf 生成的结构体作为 Service 或 Controller 层的 DTO，原因：
    1.  Protobuf 结构体缺乏框架特定的标签（如 `binding`, `json` 的 lowerCamelCase 格式可能不符合要求）。
    2.  类型可能不完全匹配（如 Protobuf 的 `Timestamp` 需要转换为 `int64`）。
    3.  可能需要裁剪或合并字段以适应特定的 API 视图。
-   **推荐做法**: 定义独立的 DTO 结构体，其字段**大致对应**于 Protobuf 消息，并添加必要的标签。Service 层负责在 Protobuf 结构体和 DTO 之间进行转换 (或者 Controller 层处理 Protobuf <-> DTO)。

## 6. 数据转换 (Conversion)

-   **职责分离**: Domain 对象与 DTO 之间的转换是必要的，以隔离内部模型和外部数据契约。
-   **位置**: 转换逻辑通常放在 Service 层实现中，或者放在 `dto/converter.go` 文件内供 Service 层调用。
-   **实现**: 编写明确的转换函数。
    ```go
    // dto/converter.go
    package dto

    import (
        "your_project/app/user/domain/entity"
    )

    // Domain User 转换为 UserDTO
    func ToUserDTO(user *entity.User) *UserDTO {
        if user == nil {
            return nil
        }
        dto := &UserDTO{
            UserID:    user.ID,
            Username:  user.Username,
            Email:     user.Email,
            Status:    int32(user.Status), // 注意类型转换
            CreatedAt: user.CreatedAt,
        }
        // 可选字段处理 (示例)
        // if user.Role != "" { dto.Role = &user.Role } 
        return dto
    }

    // 从 CreateUserRequestDTO 创建 Domain User (可能需要Service层信息如密码哈希)
    // 注意：这种转换通常在Service层完成，因为它可能需要额外逻辑
    func FromCreateUserRequestDTO(dto *CreateUserRequestDTO) (*entity.User, error) {
        // 这里的密码是明文，需要在Service层哈希
        // 验证已在Controller层完成
        user := &entity.User{
            Username: dto.Username,
            Email:    dto.Email,
            // Password 和 Status 等应在Service层设置
        }
        // 可选字段处理
        // if dto.Age != nil { user.Age = *dto.Age }
        return user, nil
    }

    // 批量转换示例
    func ToUserDTOs(users []*entity.User) []*UserDTO {
        dtos := make([]*UserDTO, 0, len(users))
        for _, user := range users {
            dtos = append(dtos, ToUserDTO(user))
        }
        return dtos
    }
    ```
-   **工具**: 可以使用 `github.com/jinzhu/copier` 等库简化同名字段的复制，但要注意类型和指针的处理。

## 7. 响应结构

-   API 响应通常需要统一的结构，例如包含业务码、消息、数据等。
-   可以定义通用的响应 DTO 结构，使用 Go 泛型 (1.18+)。
    ```go
    // dto/response.go 或 util/response/response.go
    package dto

    // CommonResponse 通用API响应结构
    type CommonResponse[T any] struct {
        Code    int    `json:"code"`    // 业务状态码，0 通常表示成功
        Message string `json:"message"` // 提示信息
        Data    T      `json:"data,omitempty"` // 实际业务数据
    }

    // 成功响应辅助函数
    func Success[T any](mdc:data T) CommonResponse[T] {
        return CommonResponse[T]{
            Code:    0,
            Message: "Success",
            Data:    data,
        }
    }

    // 失败响应辅助函数
    func Error[T any](mdc:code int, message string) CommonResponse[T] {
        return CommonResponse[T]{
            Code:    code,
            Message: message,
        }
    }
    ```
    *   Controller 层负责将 Service 返回的数据或错误包装成这种通用响应结构。

## 8. 安全注意事项

-   **禁止泄露敏感信息**: 响应 DTO 中 **绝对不能** 包含如密码哈希、内部配置等敏感数据。
-   **输入验证**: 请求 DTO 的验证是防止非法数据进入系统的第一道防线，务必严格。

## 9. 示例

```go
// app/user/dto/request.go
package dto

// UpdateUserProfileRequestDTO 更新用户个人信息请求
// 注意与 CreateUserRequestDTO 的区别，可能允许部分字段为空
type UpdateUserProfileRequestDTO struct {
    Nickname *string `json:"nickname,omitempty" binding:"omitempty,max=50"`
    Avatar   *string `json:"avatar,omitempty" binding:"omitempty,url"`
    Bio      *string `json:"bio,omitempty" binding:"omitempty,max=255"`
}

// ListUsersRequestDTO 查询用户列表请求 (分页)
type ListUsersRequestDTO struct {
    Page     int    `json:"page" form:"page" binding:"required,gt=0"` // 页码
    PageSize int    `json:"pageSize" form:"pageSize" binding:"required,gt=0,max=100"` // 每页数量
    Username *string `json:"username,omitempty" form:"username,omitempty" binding:"omitempty,max=32"` // 可选过滤条件
    Status   *int32  `json:"status,omitempty" form:"status,omitempty" binding:"omitempty,oneof=1 2 3"` // 可选状态过滤
}

// app/user/dto/response.go
package dto

// UserDTO 单个用户信息
type UserDTO struct {
    UserID    int64  `json:"userId"`
    Username  string `json:"username"`
    Email     string `json:"email"`
    Status    int32  `json:"status"`
    CreatedAt int64  `json:"createdAt"`
    Nickname  *string `json:"nickname,omitempty"`
    Avatar    *string `json:"avatar,omitempty"`
}

// ListUsersResponseDTO 用户列表响应 (包含分页信息)
type ListUsersResponseDTO struct {
    Total int64      `json:"total"` // 总记录数
    List  []*UserDTO `json:"list"`  // 当前页用户列表
}
