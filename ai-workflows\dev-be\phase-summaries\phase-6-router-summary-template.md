# Phase 6: Router层完成汇总

**项目名称**: {project_name}
**业务模块**: {biz_module}
**完成时间**: {completion_time}
**执行人**: {developer_name}

## 1. 已生成文件清单

### 1.1 路由配置文件
- `internal/router/router.go` - 主路由配置
- `internal/router/{biz_module}_router.go` - {biz_module}业务路由
- `internal/router/middleware.go` - 中间件配置
- `internal/router/version.go` - 版本路由管理

### 1.2 路由组文件
- `internal/router/api/v1/router.go` - V1版本API路由
- `internal/router/api/v1/{biz_module}.go` - V1版本{biz_module}路由
- `internal/router/admin/router.go` - 管理后台路由
- `internal/router/public/router.go` - 公开接口路由

### 1.3 健康检查文件
- `internal/router/health/health.go` - 健康检查路由
- `internal/router/metrics/metrics.go` - 指标监控路由
- `internal/router/debug/debug.go` - 调试接口路由

### 1.4 静态资源文件
- `internal/router/static/static.go` - 静态资源路由
- `web/static/` - 静态资源目录
- `web/templates/` - 模板文件目录

### 1.5 配置文件
- `configs/router.yaml` - 路由配置
- `configs/middleware.yaml` - 中间件配置
- `configs/cors.yaml` - 跨域配置

### 1.6 测试文件
- `internal/router/router_test.go` - 路由测试
- `internal/router/middleware_test.go` - 中间件测试
- `test/api/` - API集成测试

## 2. 主路由配置汇总

### 2.1 主路由结构
- **文件位置**: `internal/router/router.go`
```go
// Router 路由管理器
type Router struct {
    engine      *gin.Engine
    config      *config.Config
    logger      *logger.Logger
    controllers *Controllers
}

// Controllers 控制器集合
type Controllers struct {
    // {biz_module}模块控制器
    {Entity1}Controller *{biz_module}.{Entity1}Controller
    {Entity2}Controller *{biz_module}.{Entity2}Controller
    {Entity3}Controller *{biz_module}.{Entity3}Controller
    
    // 系统控制器
    HealthController  *health.HealthController
    MetricsController *metrics.MetricsController
}

// NewRouter 创建路由管理器
func NewRouter(
    config *config.Config,
    logger *logger.Logger,
    controllers *Controllers,
) *Router {
    // 设置Gin模式
    if config.App.Debug {
        gin.SetMode(gin.DebugMode)
    } else {
        gin.SetMode(gin.ReleaseMode)
    }
    
    engine := gin.New()
    
    return &Router{
        engine:      engine,
        config:      config,
        logger:      logger,
        controllers: controllers,
    }
}

// Setup 设置路由
func (r *Router) Setup() {
    // 1. 设置全局中间件
    r.setupGlobalMiddleware()
    
    // 2. 设置健康检查路由
    r.setupHealthRoutes()
    
    // 3. 设置静态资源路由
    r.setupStaticRoutes()
    
    // 4. 设置API路由
    r.setupAPIRoutes()
    
    // 5. 设置管理后台路由
    r.setupAdminRoutes()
    
    // 6. 设置调试路由
    r.setupDebugRoutes()
    
    // 7. 设置404处理
    r.setup404Handler()
}

// GetEngine 获取Gin引擎
func (r *Router) GetEngine() *gin.Engine {
    return r.engine
}
```

### 2.2 全局中间件配置
```go
// setupGlobalMiddleware 设置全局中间件
func (r *Router) setupGlobalMiddleware() {
    // 1. 恢复中间件 - 必须在最前面
    r.engine.Use(gin.Recovery())
    
    // 2. 请求ID中间件
    r.engine.Use(middleware.RequestIDMiddleware())
    
    // 3. 日志中间件
    r.engine.Use(middleware.LoggerMiddleware(r.logger))
    
    // 4. 跨域中间件
    r.engine.Use(middleware.CORSMiddleware())
    
    // 5. 安全头中间件
    r.engine.Use(middleware.SecurityHeadersMiddleware())
    
    // 6. 指标收集中间件
    r.engine.Use(middleware.MetricsMiddleware())
    
    // 7. 全局错误处理中间件
    r.engine.Use(middleware.GlobalErrorHandler())
}
```

### 2.3 健康检查路由
```go
// setupHealthRoutes 设置健康检查路由
func (r *Router) setupHealthRoutes() {
    health := r.engine.Group("/health")
    {
        health.GET("/", r.controllers.HealthController.Check)
        health.GET("/ready", r.controllers.HealthController.Ready)
        health.GET("/live", r.controllers.HealthController.Live)
    }
    
    // 指标监控路由
    metrics := r.engine.Group("/metrics")
    {
        metrics.GET("/", r.controllers.MetricsController.Metrics)
        metrics.GET("/prometheus", r.controllers.MetricsController.Prometheus)
    }
}
```

## 3. API路由配置汇总

### 3.1 API版本路由
- **文件位置**: `internal/router/api/v1/router.go`
```go
// SetupV1Routes 设置V1版本API路由
func SetupV1Routes(router *gin.RouterGroup, controllers *Controllers) {
    v1 := router.Group("/v1")
    
    // 应用通用中间件
    v1.Use(middleware.RateLimitMiddleware(100, 200)) // 限流
    
    // 设置各业务模块路由
    setup{BizModule}Routes(v1, controllers)
    
    // 设置用户相关路由
    setupUserRoutes(v1, controllers)
    
    // 设置文件上传路由
    setupFileRoutes(v1, controllers)
}

// setup{BizModule}Routes 设置{biz_module}业务路由
func setup{BizModule}Routes(router *gin.RouterGroup, controllers *Controllers) {
    {bizModule} := router.Group("/{biz_module}")
    
    // 需要认证的路由组
    auth := {bizModule}.Group("/")
    auth.Use(middleware.AuthMiddleware())
    {
        // {Entity1}相关路由
        {entity1} := auth.Group("/{entity1}")
        {
            {entity1}.POST("/", controllers.{Entity1}Controller.Create)
            {entity1}.GET("/:id", controllers.{Entity1}Controller.GetByID)
            {entity1}.PUT("/:id", controllers.{Entity1}Controller.Update)
            {entity1}.DELETE("/:id", controllers.{Entity1}Controller.Delete)
            {entity1}.GET("/", controllers.{Entity1}Controller.List)
            
            // 业务操作路由
            {entity1}.POST("/:id/{business_action1}", controllers.{Entity1}Controller.{BusinessMethod1})
            {entity1}.PATCH("/:id/status", controllers.{Entity1}Controller.{ChangeStatus})
            {entity1}.POST("/batch", controllers.{Entity1}Controller.{BatchOperation})
        }
        
        // {Entity2}相关路由
        {entity2} := auth.Group("/{entity2}")
        {
            {entity2}.POST("/", controllers.{Entity2}Controller.Create)
            {entity2}.GET("/:id", controllers.{Entity2}Controller.GetByID)
            {entity2}.PUT("/:id", controllers.{Entity2}Controller.Update)
            {entity2}.DELETE("/:id", controllers.{Entity2}Controller.Delete)
            {entity2}.GET("/", controllers.{Entity2}Controller.List)
        }
        
        // {Entity3}相关路由
        {entity3} := auth.Group("/{entity3}")
        {
            {entity3}.POST("/", controllers.{Entity3}Controller.Create)
            {entity3}.GET("/:id", controllers.{Entity3}Controller.GetByID)
            {entity3}.PUT("/:id", controllers.{Entity3}Controller.Update)
            {entity3}.DELETE("/:id", controllers.{Entity3}Controller.Delete)
            {entity3}.GET("/", controllers.{Entity3}Controller.List)
        }
    }
    
    // 公开路由（无需认证）
    public := {bizModule}.Group("/public")
    {
        public.GET("/{entity1}/stats", controllers.{Entity1}Controller.GetStats)
        public.GET("/{entity1}/export", controllers.{Entity1}Controller.Export)
    }
}
```

### 3.2 RESTful路由设计
```go
// RESTful路由规范
const (
    // 资源路由模式
    // GET    /{resource}           - 获取资源列表
    // POST   /{resource}           - 创建资源
    // GET    /{resource}/{id}      - 获取单个资源
    // PUT    /{resource}/{id}      - 更新资源
    // PATCH  /{resource}/{id}      - 部分更新资源
    // DELETE /{resource}/{id}      - 删除资源
    
    // 子资源路由模式
    // GET    /{resource}/{id}/{sub_resource}           - 获取子资源列表
    // POST   /{resource}/{id}/{sub_resource}           - 创建子资源
    // GET    /{resource}/{id}/{sub_resource}/{sub_id}  - 获取单个子资源
    // PUT    /{resource}/{id}/{sub_resource}/{sub_id}  - 更新子资源
    // DELETE /{resource}/{id}/{sub_resource}/{sub_id}  - 删除子资源
    
    // 操作路由模式
    // POST   /{resource}/{id}/{action}  - 执行特定操作
    // PATCH  /{resource}/{id}/{field}   - 更新特定字段
)

// 路由示例
func setupRESTfulRoutes(router *gin.RouterGroup, controllers *Controllers) {
    // 基础CRUD路由
    router.GET("/{entity1}", controllers.{Entity1}Controller.List)           // 列表
    router.POST("/{entity1}", controllers.{Entity1}Controller.Create)        // 创建
    router.GET("/{entity1}/:id", controllers.{Entity1}Controller.GetByID)    // 详情
    router.PUT("/{entity1}/:id", controllers.{Entity1}Controller.Update)     // 更新
    router.DELETE("/{entity1}/:id", controllers.{Entity1}Controller.Delete)  // 删除
    
    // 状态操作路由
    router.PATCH("/{entity1}/:id/status", controllers.{Entity1}Controller.ChangeStatus)
    router.PATCH("/{entity1}/:id/enable", controllers.{Entity1}Controller.Enable)
    router.PATCH("/{entity1}/:id/disable", controllers.{Entity1}Controller.Disable)
    
    // 业务操作路由
    router.POST("/{entity1}/:id/approve", controllers.{Entity1}Controller.Approve)
    router.POST("/{entity1}/:id/reject", controllers.{Entity1}Controller.Reject)
    router.POST("/{entity1}/:id/submit", controllers.{Entity1}Controller.Submit)
    
    // 批量操作路由
    router.POST("/{entity1}/batch/delete", controllers.{Entity1}Controller.BatchDelete)
    router.POST("/{entity1}/batch/update", controllers.{Entity1}Controller.BatchUpdate)
    router.POST("/{entity1}/batch/export", controllers.{Entity1}Controller.BatchExport)
    
    // 统计查询路由
    router.GET("/{entity1}/stats", controllers.{Entity1}Controller.GetStats)
    router.GET("/{entity1}/count", controllers.{Entity1}Controller.GetCount)
    router.GET("/{entity1}/summary", controllers.{Entity1}Controller.GetSummary)
}
```

### 3.3 路由参数设计
```go
// 路径参数设计
type RouteParams struct {
    // 资源ID参数
    ID       string `uri:"id" binding:"required"`
    
    // 业务参数
    Type     string `uri:"type" binding:"required,oneof=type1 type2 type3"`
    Category string `uri:"category" binding:"required"`
    
    // 可选参数
    SubID    string `uri:"sub_id"`
    Action   string `uri:"action"`
}

// 查询参数设计
type QueryParams struct {
    // 分页参数
    Page     int `form:"page" binding:"omitempty,min=1"`
    PageSize int `form:"pageSize" binding:"omitempty,min=1,max=100"`
    
    // 排序参数
    Sort  string `form:"sort" binding:"omitempty"`
    Order string `form:"order" binding:"omitempty,oneof=asc desc"`
    
    // 筛选参数
    Status    *int32  `form:"status" binding:"omitempty"`
    Keyword   *string `form:"keyword" binding:"omitempty,max=50"`
    StartTime *int64  `form:"startTime" binding:"omitempty"`
    EndTime   *int64  `form:"endTime" binding:"omitempty"`
    
    // 业务参数
    Category *string `form:"category" binding:"omitempty"`
    Type     *string `form:"type" binding:"omitempty"`
}
```

## 4. 中间件配置汇总

### 4.1 认证中间件配置
```go
// setupAuthMiddleware 配置认证中间件
func setupAuthMiddleware(router *gin.RouterGroup) {
    // JWT认证中间件
    auth := middleware.AuthMiddleware()
    
    // 角色权限中间件
    adminRole := middleware.RoleMiddleware("admin")
    userRole := middleware.RoleMiddleware("user")
    
    // 资源权限中间件
    resourcePerm := middleware.ResourcePermissionMiddleware()
    
    // 应用中间件
    router.Use(auth)
    
    // 管理员路由
    admin := router.Group("/admin")
    admin.Use(adminRole)
    
    // 用户路由
    user := router.Group("/user")
    user.Use(userRole)
    
    // 资源路由
    resource := router.Group("/resource")
    resource.Use(resourcePerm)
}
```

### 4.2 限流中间件配置
```go
// setupRateLimitMiddleware 配置限流中间件
func setupRateLimitMiddleware(router *gin.RouterGroup) {
    // 全局限流
    globalLimit := middleware.RateLimitMiddleware(1000, 2000)
    
    // API限流
    apiLimit := middleware.RateLimitMiddleware(100, 200)
    
    // 用户限流
    userLimit := middleware.UserRateLimitMiddleware(10, 20)
    
    // IP限流
    ipLimit := middleware.IPRateLimitMiddleware(50, 100)
    
    // 应用限流中间件
    router.Use(globalLimit)
    
    api := router.Group("/api")
    api.Use(apiLimit)
    api.Use(userLimit)
    api.Use(ipLimit)
}
```

### 4.3 缓存中间件配置
```go
// setupCacheMiddleware 配置缓存中间件
func setupCacheMiddleware(router *gin.RouterGroup) {
    // 响应缓存中间件
    responseCache := middleware.ResponseCacheMiddleware(5 * time.Minute)
    
    // 页面缓存中间件
    pageCache := middleware.PageCacheMiddleware(10 * time.Minute)
    
    // API缓存中间件
    apiCache := middleware.APICacheMiddleware(1 * time.Minute)
    
    // 应用缓存中间件
    router.GET("/public/*path", pageCache)
    
    api := router.Group("/api")
    api.GET("/stats", apiCache)
    api.GET("/config", responseCache)
}
```

## 5. 路由组织结构汇总

### 5.1 按功能分组
```go
// 按功能模块组织路由
func setupFunctionalRoutes(router *gin.Engine) {
    // 认证相关路由
    auth := router.Group("/auth")
    {
        auth.POST("/login", controllers.AuthController.Login)
        auth.POST("/logout", controllers.AuthController.Logout)
        auth.POST("/refresh", controllers.AuthController.Refresh)
        auth.GET("/profile", controllers.AuthController.Profile)
    }
    
    // 用户管理路由
    users := router.Group("/users")
    users.Use(middleware.AuthMiddleware())
    {
        users.GET("/", controllers.UserController.List)
        users.POST("/", controllers.UserController.Create)
        users.GET("/:id", controllers.UserController.GetByID)
        users.PUT("/:id", controllers.UserController.Update)
        users.DELETE("/:id", controllers.UserController.Delete)
    }
    
    // 文件管理路由
    files := router.Group("/files")
    files.Use(middleware.AuthMiddleware())
    {
        files.POST("/upload", controllers.FileController.Upload)
        files.GET("/:id/download", controllers.FileController.Download)
        files.DELETE("/:id", controllers.FileController.Delete)
    }
    
    // 系统配置路由
    system := router.Group("/system")
    system.Use(middleware.AuthMiddleware())
    system.Use(middleware.AdminRoleMiddleware())
    {
        system.GET("/config", controllers.SystemController.GetConfig)
        system.PUT("/config", controllers.SystemController.UpdateConfig)
        system.GET("/logs", controllers.SystemController.GetLogs)
    }
}
```

### 5.2 按版本分组
```go
// 按API版本组织路由
func setupVersionedRoutes(router *gin.Engine) {
    api := router.Group("/api")
    
    // V1版本API
    v1 := api.Group("/v1")
    {
        setupV1{BizModule}Routes(v1)
        setupV1UserRoutes(v1)
        setupV1FileRoutes(v1)
    }
    
    // V2版本API
    v2 := api.Group("/v2")
    {
        setupV2{BizModule}Routes(v2)
        setupV2UserRoutes(v2)
        setupV2FileRoutes(v2)
    }
    
    // 默认版本（最新版本）
    setupV2{BizModule}Routes(api)
}
```

### 5.3 按权限分组
```go
// 按权限级别组织路由
func setupPermissionRoutes(router *gin.Engine) {
    // 公开路由（无需认证）
    public := router.Group("/public")
    {
        public.GET("/health", controllers.HealthController.Check)
        public.GET("/version", controllers.SystemController.Version)
        public.GET("/docs/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
    }
    
    // 需要认证的路由
    protected := router.Group("/")
    protected.Use(middleware.AuthMiddleware())
    {
        // 普通用户路由
        user := protected.Group("/user")
        {
            user.GET("/profile", controllers.UserController.Profile)
            user.PUT("/profile", controllers.UserController.UpdateProfile)
        }
        
        // 管理员路由
        admin := protected.Group("/admin")
        admin.Use(middleware.AdminRoleMiddleware())
        {
            admin.GET("/users", controllers.UserController.List)
            admin.POST("/users", controllers.UserController.Create)
            admin.DELETE("/users/:id", controllers.UserController.Delete)
        }
        
        // 超级管理员路由
        super := protected.Group("/super")
        super.Use(middleware.SuperAdminRoleMiddleware())
        {
            super.GET("/system/config", controllers.SystemController.GetConfig)
            super.PUT("/system/config", controllers.SystemController.UpdateConfig)
        }
    }
}
```

## 6. 静态资源配置汇总

### 6.1 静态文件服务
```go
// setupStaticRoutes 设置静态资源路由
func (r *Router) setupStaticRoutes() {
    // 静态文件服务
    r.engine.Static("/static", "./web/static")
    r.engine.StaticFS("/assets", http.Dir("./web/assets"))
    r.engine.StaticFile("/favicon.ico", "./web/static/favicon.ico")
    
    // 模板文件
    r.engine.LoadHTMLGlob("web/templates/*")
    
    // 文件上传目录
    r.engine.Static("/uploads", "./storage/uploads")
    
    // 下载文件路由
    r.engine.GET("/download/:filename", r.downloadFile)
}

// downloadFile 文件下载处理
func (r *Router) downloadFile(ctx *gin.Context) {
    filename := ctx.Param("filename")
    filepath := "./storage/downloads/" + filename
    
    // 安全检查
    if !isValidFilename(filename) {
        ctx.JSON(http.StatusBadRequest, gin.H{"error": "无效的文件名"})
        return
    }
    
    // 检查文件是否存在
    if _, err := os.Stat(filepath); os.IsNotExist(err) {
        ctx.JSON(http.StatusNotFound, gin.H{"error": "文件不存在"})
        return
    }
    
    // 设置下载头
    ctx.Header("Content-Disposition", "attachment; filename="+filename)
    ctx.File(filepath)
}
```

### 6.2 文件上传路由
```go
// setupFileUploadRoutes 设置文件上传路由
func setupFileUploadRoutes(router *gin.RouterGroup) {
    files := router.Group("/files")
    files.Use(middleware.AuthMiddleware())
    
    // 单文件上传
    files.POST("/upload", func(ctx *gin.Context) {
        file, err := ctx.FormFile("file")
        if err != nil {
            ctx.JSON(http.StatusBadRequest, gin.H{"error": "文件上传失败"})
            return
        }
        
        // 文件大小限制
        if file.Size > 10*1024*1024 { // 10MB
            ctx.JSON(http.StatusBadRequest, gin.H{"error": "文件大小超过限制"})
            return
        }
        
        // 文件类型检查
        if !isAllowedFileType(file.Header.Get("Content-Type")) {
            ctx.JSON(http.StatusBadRequest, gin.H{"error": "不支持的文件类型"})
            return
        }
        
        // 保存文件
        filename := generateUniqueFilename(file.Filename)
        filepath := "./storage/uploads/" + filename
        
        if err := ctx.SaveUploadedFile(file, filepath); err != nil {
            ctx.JSON(http.StatusInternalServerError, gin.H{"error": "文件保存失败"})
            return
        }
        
        ctx.JSON(http.StatusOK, gin.H{
            "message":  "文件上传成功",
            "filename": filename,
            "url":      "/uploads/" + filename,
        })
    })
    
    // 多文件上传
    files.POST("/upload/multiple", func(ctx *gin.Context) {
        form, err := ctx.MultipartForm()
        if err != nil {
            ctx.JSON(http.StatusBadRequest, gin.H{"error": "多文件上传失败"})
            return
        }
        
        files := form.File["files"]
        var uploadedFiles []string
        
        for _, file := range files {
            filename := generateUniqueFilename(file.Filename)
            filepath := "./storage/uploads/" + filename
            
            if err := ctx.SaveUploadedFile(file, filepath); err != nil {
                continue
            }
            
            uploadedFiles = append(uploadedFiles, filename)
        }
        
        ctx.JSON(http.StatusOK, gin.H{
            "message": "文件上传完成",
            "files":   uploadedFiles,
        })
    })
}
```

## 7. 错误处理路由汇总

### 7.1 404处理
```go
// setup404Handler 设置404处理
func (r *Router) setup404Handler() {
    r.engine.NoRoute(func(ctx *gin.Context) {
        // 检查是否是API请求
        if strings.HasPrefix(ctx.Request.URL.Path, "/api/") {
            ctx.JSON(http.StatusNotFound, gin.H{
                "code":    40404,
                "message": "API接口不存在",
                "path":    ctx.Request.URL.Path,
            })
            return
        }
        
        // 检查是否是静态资源请求
        if strings.HasPrefix(ctx.Request.URL.Path, "/static/") ||
           strings.HasPrefix(ctx.Request.URL.Path, "/assets/") {
            ctx.JSON(http.StatusNotFound, gin.H{
                "code":    40404,
                "message": "静态资源不存在",
                "path":    ctx.Request.URL.Path,
            })
            return
        }
        
        // 默认404页面
        ctx.HTML(http.StatusNotFound, "404.html", gin.H{
            "title": "页面不存在",
            "path":  ctx.Request.URL.Path,
        })
    })
}
```

### 7.2 方法不允许处理
```go
// setupMethodNotAllowedHandler 设置方法不允许处理
func (r *Router) setupMethodNotAllowedHandler() {
    r.engine.NoMethod(func(ctx *gin.Context) {
        ctx.JSON(http.StatusMethodNotAllowed, gin.H{
            "code":    40505,
            "message": "HTTP方法不允许",
            "method":  ctx.Request.Method,
            "path":    ctx.Request.URL.Path,
        })
    })
}
```

## 8. 调试和监控路由汇总

### 8.1 调试路由
```go
// setupDebugRoutes 设置调试路由
func (r *Router) setupDebugRoutes() {
    if !r.config.App.Debug {
        return
    }
    
    debug := r.engine.Group("/debug")
    debug.Use(middleware.IPWhitelistMiddleware([]string{"127.0.0.1", "::1"}))
    
    // pprof性能分析
    debug.GET("/pprof/*any", gin.WrapH(http.DefaultServeMux))
    
    // 系统信息
    debug.GET("/info", func(ctx *gin.Context) {
        ctx.JSON(http.StatusOK, gin.H{
            "version":    r.config.App.Version,
            "build_time": r.config.App.BuildTime,
            "go_version": runtime.Version(),
            "goroutines": runtime.NumGoroutine(),
        })
    })
    
    // 配置信息
    debug.GET("/config", func(ctx *gin.Context) {
        ctx.JSON(http.StatusOK, r.config)
    })
    
    // 路由信息
    debug.GET("/routes", func(ctx *gin.Context) {
        routes := r.engine.Routes()
        ctx.JSON(http.StatusOK, routes)
    })
}
```

### 8.2 监控路由
```go
// setupMonitoringRoutes 设置监控路由
func (r *Router) setupMonitoringRoutes() {
    monitoring := r.engine.Group("/monitoring")
    
    // Prometheus指标
    monitoring.GET("/metrics", gin.WrapH(promhttp.Handler()))
    
    // 健康检查
    monitoring.GET("/health", func(ctx *gin.Context) {
        ctx.JSON(http.StatusOK, gin.H{
            "status":    "healthy",
            "timestamp": time.Now().Unix(),
        })
    })
    
    // 就绪检查
    monitoring.GET("/ready", func(ctx *gin.Context) {
        // 检查数据库连接
        if err := r.checkDatabase(); err != nil {
            ctx.JSON(http.StatusServiceUnavailable, gin.H{
                "status": "not ready",
                "reason": "database connection failed",
            })
            return
        }
        
        // 检查Redis连接
        if err := r.checkRedis(); err != nil {
            ctx.JSON(http.StatusServiceUnavailable, gin.H{
                "status": "not ready",
                "reason": "redis connection failed",
            })
            return
        }
        
        ctx.JSON(http.StatusOK, gin.H{
            "status": "ready",
        })
    })
    
    // 存活检查
    monitoring.GET("/live", func(ctx *gin.Context) {
        ctx.JSON(http.StatusOK, gin.H{
            "status": "alive",
        })
    })
}
```

## 9. 路由安全配置汇总

### 9.1 HTTPS配置
```go
// setupHTTPS 配置HTTPS
func (r *Router) setupHTTPS() {
    if r.config.Server.TLS.Enabled {
        // 强制HTTPS重定向
        r.engine.Use(middleware.HTTPSRedirectMiddleware())
        
        // HSTS头设置
        r.engine.Use(func(ctx *gin.Context) {
            ctx.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
            ctx.Next()
        })
    }
}
```

### 9.2 IP白名单配置
```go
// setupIPWhitelist 配置IP白名单
func setupIPWhitelist(router *gin.RouterGroup, allowedIPs []string) {
    router.Use(middleware.IPWhitelistMiddleware(allowedIPs))
}

// IPWhitelistMiddleware IP白名单中间件
func IPWhitelistMiddleware(allowedIPs []string) gin.HandlerFunc {
    return func(ctx *gin.Context) {
        clientIP := ctx.ClientIP()
        
        // 检查IP是否在白名单中
        allowed := false
        for _, ip := range allowedIPs {
            if clientIP == ip {
                allowed = true
                break
            }
        }
        
        if !allowed {
            ctx.JSON(http.StatusForbidden, gin.H{
                "code":    40301,
                "message": "IP地址不在白名单中",
                "ip":      clientIP,
            })
            ctx.Abort()
            return
        }
        
        ctx.Next()
    }
}
```

### 9.3 请求大小限制
```go
// setupRequestSizeLimit 配置请求大小限制
func setupRequestSizeLimit(router *gin.Engine, maxSize int64) {
    router.Use(func(ctx *gin.Context) {
        ctx.Request.Body = http.MaxBytesReader(ctx.Writer, ctx.Request.Body, maxSize)
        ctx.Next()
    })
}
```

## 10. 路由性能优化汇总

### 10.1 路由缓存优化
```go
// setupRouteCache 设置路由缓存
func setupRouteCache(router *gin.RouterGroup) {
    // 静态内容缓存
    router.Use(func(ctx *gin.Context) {
        if ctx.Request.Method == "GET" {
            // 设置缓存头
            ctx.Header("Cache-Control", "public, max-age=3600")
            ctx.Header("ETag", generateETag(ctx.Request.URL.Path))
        }
        ctx.Next()
    })
}

// generateETag 生成ETag
func generateETag(path string) string {
    hash := sha256.Sum256([]byte(path + time.Now().Format("2006-01-02-15")))
    return fmt.Sprintf(`"%x"`, hash[:8])
}
```

### 10.2 压缩中间件
```go
// setupCompression 设置压缩中间件
func setupCompression(router *gin.Engine) {
    router.Use(gzip.Gzip(gzip.DefaultCompression, gzip.WithExcludedExtensions([]string{
        ".png", ".gif", ".jpeg", ".jpg", ".ico", ".svg",
        ".mp4", ".avi", ".mov", ".mp3", ".wav",
        ".zip", ".tar", ".gz", ".rar",
    })))
}
```

### 10.3 连接池优化
```go
// setupConnectionPool 设置连接池
func setupConnectionPool(router *gin.Engine) {
    // 设置最大空闲连接数
    router.MaxMultipartMemory = 8 << 20 // 8 MiB
    
    // 设置读写超时
    server := &http.Server{
        Addr:           ":8080",
        Handler:        router,
        ReadTimeout:    10 * time.Second,
        WriteTimeout:   10 * time.Second,
        MaxHeaderBytes: 1 << 20,
    }
    
    // 优雅关闭
    setupGracefulShutdown(server)
}
```

## 11. 路由测试汇总

### 11.1 路由单元测试
```go
// TestRoutes 路由测试
func TestRoutes(t *testing.T) {
    // 设置测试环境
    gin.SetMode(gin.TestMode)
    router := setupTestRouter()
    
    tests := []struct {
        name           string
        method         string
        path           string
        body           string
        expectedStatus int
        expectedBody   string
    }{
        {
            name:           "健康检查",
            method:         "GET",
            path:           "/health",
            expectedStatus: http.StatusOK,
            expectedBody:   `{"status":"healthy"}`,
        },
        {
            name:           "获取{entity1}列表",
            method:         "GET",
            path:           "/api/v1/{biz_module}/{entity1}",
            expectedStatus: http.StatusOK,
        },
        {
            name:           "创建{entity1}",
            method:         "POST",
            path:           "/api/v1/{biz_module}/{entity1}",
            body:           `{"name":"test","status":1}`,
            expectedStatus: http.StatusOK,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            var req *http.Request
            if tt.body != "" {
                req = httptest.NewRequest(tt.method, tt.path, strings.NewReader(tt.body))
                req.Header.Set("Content-Type", "application/json")
            } else {
                req = httptest.NewRequest(tt.method, tt.path, nil)
            }
            
            w := httptest.NewRecorder()
            router.ServeHTTP(w, req)
            
            assert.Equal(t, tt.expectedStatus, w.Code)
            if tt.expectedBody != "" {
                assert.JSONEq(t, tt.expectedBody, w.Body.String())
            }
        })
    }
}
```

### 11.2 中间件测试
```go
// TestMiddleware 中间件测试
func TestMiddleware(t *testing.T) {
    gin.SetMode(gin.TestMode)
    
    t.Run("认证中间件", func(t *testing.T) {
        router := gin.New()
        router.Use(middleware.AuthMiddleware())
        router.GET("/protected", func(ctx *gin.Context) {
            ctx.JSON(http.StatusOK, gin.H{"message": "success"})
        })
        
        // 无Token请求
        req := httptest.NewRequest("GET", "/protected", nil)
        w := httptest.NewRecorder()
        router.ServeHTTP(w, req)
        assert.Equal(t, http.StatusUnauthorized, w.Code)
        
        // 有效Token请求
        req = httptest.NewRequest("GET", "/protected", nil)
        req.Header.Set("Authorization", "Bearer valid_token")
        w = httptest.NewRecorder()
        router.ServeHTTP(w, req)
        assert.Equal(t, http.StatusOK, w.Code)
    })
    
    t.Run("限流中间件", func(t *testing.T) {
        router := gin.New()
        router.Use(middleware.RateLimitMiddleware(1, 1))
        router.GET("/limited", func(ctx *gin.Context) {
            ctx.JSON(http.StatusOK, gin.H{"message": "success"})
        })
        
        // 第一次请求成功
        req := httptest.NewRequest("GET", "/limited", nil)
        w := httptest.NewRecorder()
        router.ServeHTTP(w, req)
        assert.Equal(t, http.StatusOK, w.Code)
        
        // 第二次请求被限流
        req = httptest.NewRequest("GET", "/limited", nil)
        w = httptest.NewRecorder()
        router.ServeHTTP(w, req)
        assert.Equal(t, http.StatusTooManyRequests, w.Code)
    })
}
```

### 11.3 集成测试
```go
// TestAPIIntegration API集成测试
func TestAPIIntegration(t *testing.T) {
    // 启动测试服务器
    server := setupTestServer()
    defer server.Close()
    
    client := &http.Client{Timeout: 10 * time.Second}
    
    t.Run("完整CRUD流程", func(t *testing.T) {
        // 1. 创建资源
        createReq := `{"name":"test_{entity1}","status":1}`
        resp, err := client.Post(server.URL+"/api/v1/{biz_module}/{entity1}",
            "application/json", strings.NewReader(createReq))
        assert.NoError(t, err)
        assert.Equal(t, http.StatusOK, resp.StatusCode)
        
        var createResp map[string]interface{}
        json.NewDecoder(resp.Body).Decode(&createResp)
        id := createResp["data"].(map[string]interface{})["id"]
        
        // 2. 获取资源
        resp, err = client.Get(server.URL + "/api/v1/{biz_module}/{entity1}/" + fmt.Sprintf("%.0f", id))
        assert.NoError(t, err)
        assert.Equal(t, http.StatusOK, resp.StatusCode)
        
        // 3. 更新资源
        updateReq := `{"name":"updated_test_{entity1}","status":2}`
        req, _ := http.NewRequest("PUT", server.URL+"/api/v1/{biz_module}/{entity1}/"+fmt.Sprintf("%.0f", id),
            strings.NewReader(updateReq))
        req.Header.Set("Content-Type", "application/json")
        resp, err = client.Do(req)
        assert.NoError(t, err)
        assert.Equal(t, http.StatusOK, resp.StatusCode)
        
        // 4. 删除资源
        req, _ = http.NewRequest("DELETE", server.URL+"/api/v1/{biz_module}/{entity1}/"+fmt.Sprintf("%.0f", id), nil)
        resp, err = client.Do(req)
        assert.NoError(t, err)
        assert.Equal(t, http.StatusOK, resp.StatusCode)
    })
}
```

## 12. 路由文档汇总

### 12.1 API文档生成
```go
// setupSwagger 设置Swagger文档
func setupSwagger(router *gin.Engine) {
    // Swagger文档路由
    router.GET("/docs/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
    
    // API文档重定向
    router.GET("/", func(ctx *gin.Context) {
        ctx.Redirect(http.StatusMovedPermanently, "/docs/index.html")
    })
    
    // OpenAPI规范
    router.GET("/openapi.json", func(ctx *gin.Context) {
        ctx.File("./docs/openapi.json")
    })
}
```

### 12.2 路由信息导出
```go
// exportRoutes 导出路由信息
func exportRoutes(router *gin.Engine) {
    routes := router.Routes()
    
    // 生成路由表
    var routeTable []map[string]string
    for _, route := range routes {
        routeTable = append(routeTable, map[string]string{
            "method": route.Method,
            "path":   route.Path,
            "handler": route.Handler,
        })
    }
    
    // 保存到文件
    data, _ := json.MarshalIndent(routeTable, "", "  ")
    ioutil.WriteFile("./docs/routes.json", data, 0644)
}
```

## 13. 部署配置汇总

### 13.1 生产环境配置
```go
// setupProductionConfig 生产环境配置
func setupProductionConfig(router *gin.Engine) {
    // 设置生产模式
    gin.SetMode(gin.ReleaseMode)
    
    // 禁用调试路由
    // 不设置debug路由
    
    // 启用安全中间件
    router.Use(middleware.SecurityHeadersMiddleware())
    router.Use(middleware.HTTPSRedirectMiddleware())
    
    // 设置严格的CORS策略
    router.Use(cors.New(cors.Config{
        AllowOrigins:     []string{"https://yourdomain.com"},
        AllowMethods:     []string{"GET", "POST", "PUT", "DELETE"},
        AllowHeaders:     []string{"Origin", "Content-Type", "Authorization"},
        AllowCredentials: true,
        MaxAge:           12 * time.Hour,
    }))
    
    // 启用请求限流
    router.Use(middleware.RateLimitMiddleware(1000, 2000))
}
```

### 13.2 开发环境配置
```go
// setupDevelopmentConfig 开发环境配置
func setupDevelopmentConfig(router *gin.Engine) {
    // 设置调试模式
    gin.SetMode(gin.DebugMode)
    
    // 启用调试路由
    setupDebugRoutes(router)
    
    // 宽松的CORS策略
    router.Use(cors.New(cors.Config{
        AllowOrigins: []string{"*"},
        AllowMethods: []string{"*"},
        AllowHeaders: []string{"*"},
    }))
    
    // 详细的日志记录
    router.Use(gin.Logger())
    router.Use(gin.Recovery())
}
```

### 13.3 Docker配置
```dockerfile
# Dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY . .
RUN go mod download
RUN CGO_ENABLED=0 GOOS=linux go build -o main .

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/main .
COPY --from=builder /app/configs ./configs
COPY --from=builder /app/web ./web

EXPOSE 8080
CMD ["./main"]
```

## 14. 监控和日志汇总

### 14.1 访问日志
```go
// setupAccessLog 设置访问日志
func setupAccessLog(router *gin.Engine) {
    // 自定义日志格式
    router.Use(gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
        return fmt.Sprintf("%s - [%s] \"%s %s %s %d %s \"%s\" %s\"\n",
            param.ClientIP,
            param.TimeStamp.Format(time.RFC1123),
            param.Method,
            param.Path,
            param.Request.Proto,
            param.StatusCode,
            param.Latency,
            param.Request.UserAgent(),
            param.ErrorMessage,
        )
    }))
}
```

### 14.2 性能监控
```go
// setupPerformanceMonitoring 设置性能监控
func setupPerformanceMonitoring(router *gin.Engine) {
    // Prometheus指标
    p := ginprometheus.NewPrometheus("gin")
    p.Use(router)
    
    // 自定义指标
    router.Use(func(ctx *gin.Context) {
        start := time.Now()
        ctx.Next()
        
        duration := time.Since(start)
        path := ctx.FullPath()
        method := ctx.Request.Method
        status := ctx.Writer.Status()
        
        // 记录响应时间
        responseTimeHistogram.WithLabelValues(method, path).Observe(duration.Seconds())
        
        // 记录请求计数
        requestCounter.WithLabelValues(method, path, strconv.Itoa(status)).Inc()
        
        // 记录错误计数
        if status >= 400 {
            errorCounter.WithLabelValues(method, path, strconv.Itoa(status)).Inc()
        }
    })
}
```

## 15. 下一Phase输入资源

### 15.1 完整路由系统
- ✅ 主路由配置完成
- ✅ API版本路由完成
- ✅ 业务模块路由完成
- ✅ 中间件配置完成
- ✅ 静态资源路由完成

### 15.2 安全特性
- ✅ 认证授权机制
- ✅ HTTPS配置
- ✅ CORS策略
- ✅ 请求限流
- ✅ IP白名单
- ✅ 安全头设置

### 15.3 监控能力
- ✅ 健康检查接口
- ✅ 性能指标收集
- ✅ 访问日志记录
- ✅ 错误监控
- ✅ 调试接口

### 15.4 部署就绪
- ✅ 生产环境配置
- ✅ Docker支持
- ✅ 优雅关闭
- ✅ 配置管理
- ✅ 文档生成

## 16. 系统集成指导

### 16.1 服务启动建议
- 按顺序启动各个组件
- 检查依赖服务状态
- 验证路由配置正确性
- 确认中间件工作正常

### 16.2 负载均衡配置
- 配置上游服务器
- 设置健康检查
- 实现会话保持
- 配置故障转移

### 16.3 API网关集成
- 配置路由转发规则
- 设置认证策略
- 配置限流策略
- 实现服务发现

## 17. 注意事项

### 17.1 性能考虑
- 合理配置中间件顺序
- 避免不必要的中间件
- 优化路由匹配性能
- 实现适当的缓存策略

### 17.2 安全考虑
- 验证所有输入参数
- 实现适当的访问控制
- 防止常见Web攻击
- 保护敏感信息

### 17.3 可维护性
- 保持路由结构清晰
- 统一错误处理方式
- 完善的文档说明
- 充分的测试覆盖

## 18. 验收确认

- ✅ 主路由配置完成
- ✅ 所有业务路由注册完成
- ✅ 中间件配置完成
- ✅ 静态资源服务配置完成
- ✅ 错误处理配置完成
- ✅ 安全特性实现完成
- ✅ 监控接口配置完成
- ✅ API文档生成完成
- ✅ 路由测试全部通过
- ✅ 集成测试通过
- ✅ 性能测试通过
- ✅ 安全测试通过
- ✅ 部署配置完成
- ✅ 代码评审通过
- ✅ 文档更新完成

**Phase 6 Router层开发完成，整个DDD架构开发流程完成。系统已具备完整的六层架构，可以进行部署和上线。**