# PRD - 学生端 - 账号与登录**版本管理**

<table>
<tr>
<td>版本号<br/></td><td>日期<br/></td><td>变更人<br/></td><td>变更类型<br/></td><td>变更详情<br/></td></tr>
<tr>
<td>V1.0.0<br/></td><td>2025-04-06<br/></td><td>杨宇航<br/></td><td>新建<br/></td><td>新建文档<br/></td></tr>
<tr>
<td>V1.0.1<br/></td><td>2025-04-18<br/></td><td>杨宇航<br/></td><td>补充<br/></td><td>补充登录判定逻辑，见5.3<br/></td></tr>
</table>

**关联需求**

<table>
<tr>
<td>关联需求名称<br/></td><td>所属 PM<br/></td><td>需求进度<br/></td><td>文档链接<br/></td></tr>
<tr>
<td>账号升级<br/></td><td>杨宇航<br/></td><td>开发中<br/></td><td>[运营后台产品_2期_账号升级_需求PRD](https://wcng60ba718p.feishu.cn/wiki/NtKXwty5oiS9DjkHtshcLlmrnWd)<br/></td></tr>
</table>

**设计稿**
[https://www.figma.com/design/COdZQZZOFHZHsdI1zWX8Tm/%E5%AD%A6%E7%94%9F%E7%AB%AF-1.0?node-id=64-6450&p=f&t=bCxLcVocfLM35KWq-0](https://www.figma.com/design/COdZQZZOFHZHsdI1zWX8Tm/%E5%AD%A6%E7%94%9F%E7%AB%AF-1.0?node-id=64-6450&p=f&t=bCxLcVocfLM35KWq-0)

### 一、背景和目标

#### 1.1、需求背景

结合业务第一阶段账号相关需求，提供相应的账号登录、管控等流程及能力。

#### 1.2、项目收益

确保业务第一阶段能够比较好的兼顾管控及非管控场景的账号登录和使用

#### 1.3、 覆盖用户

全量学生角色的用户

#### 1.4、方案简述

设计了学生端产品独立的登录页，仅支持账号密码登录；
管控场景中明确定义了学生账号的绑定及解绑流程，；

#### 1.5、未来会做什么

1. 未来登录方式也会拓展到可以通过手机号登录
2. 未来账号会支持家长角色，在登录完成后当校验账号同时存在学生及家长账号时，需要用户自行选择并进入

### **二、名词说明**

MDM：全称是「移动设备管理」（Mobile Device Management），简要来说就是一套能让企业或学校等组织，在电脑上统一管理大量手机、平板等设备的系统，例如控制学生仅可在设备中安装访问特定的产品。

### 三、业务流程

暂无

### 四、需求概览

<table>
<tr>
<td>序号<br/></td><td>需求名称<br/></td><td>内容概述<br/></td><td>优先级<br/></td></tr>
<tr>
<td>1<br/></td><td>学生端通用登录页<br/></td><td>通用登录流程<br/></td><td>P0<br/></td></tr>
<tr>
<td>2<br/></td><td>MDM场景账号流程<br/></td><td>账号登录、绑定、解绑环节<br/></td><td>P1<br/></td></tr>
<tr>
<td>3<br/></td><td>登录判定逻辑<br/></td><td>第一阶段账号可登录条件判定<br/></td><td>P0<br/></td></tr>
</table>

### 五、详细产品方案

#### 5.1 学生端通用登录页

<table>
<tr>
<td>模块<br/></td><td>图示<br/></td><td>详述<br/></td></tr>
<tr>
<td>登录页<br/></td><td>初始<br/>![in_table_SkQebLwG3ocTQVxkpd8cK9XbnQf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_SkQebLwG3ocTQVxkpd8cK9XbnQf.png)<br/>键盘弹出<br/>![in_table_KWXwbrvdNof5skx0XRmcrOEGnaf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_KWXwbrvdNof5skx0XRmcrOEGnaf.png)<br/><br/></td><td>- 首次打开App时，默认进入登录页- 若未设置网络时，引导设置网络- 若用户未登录，页面会一直停留在登录页。只有通过正确的学生账号登录后，才可以进入Launcher，进行其他的一切学习行为<br/>- 登录页核心模块- 标题+logo- 产品名称+logo图- 无输入操作时，标题部分正常显示- 有账号或密码输入，即拉起键盘时，标题区隐藏，密码输入框及服务协议与登录按钮均上移；- 账号密码区- 用于输入账号密码，逻辑详见下方- 服务协议区- 服务协议入口- 登录按钮- 点击执行登录操作校验<br/></td></tr>
<tr>
<td>账号密码区<br/></td><td>密码隐藏<br/>![in_table_AysKbVSQmotDiXxsckLcFMA4ndb](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_AysKbVSQmotDiXxsckLcFMA4ndb.png)<br/>密码显示<br/>![in_table_LWqNbjV3wotALoxi0YjcHF3Fn6f](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_LWqNbjV3wotALoxi0YjcHF3Fn6f.png)<br/>输入超长<br/>![in_table_CBL2bYwFFoLjoIxAFisc5huFnxc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_CBL2bYwFFoLjoIxAFisc5huFnxc.png)<br/><br/></td><td>- 进入页面时，不拉起键盘。点击输入框拉起<br/>- 账号输入区- 包含两种默认状态，从未登录成功过 及 登录成功过并退登- 从未登录过状态下，账号输入框显示“请输入个人账号”，点击拉起键盘- 登录成功过并退登状态下，账号输入框显示之前已登录过的账号- 输入账号时，拉起键盘，输入区上移。键盘切换到英文键盘模式- 输入框限制- 只可输入英文字母+数字，区分大小写- 输入至少1位后-  账号密码均为必填项，未录入信息时，「登录按钮」置灰，点击无反馈；<br/>- 密码输入区- 密码框不记录信息，每次登录时，都需要重新输入密码；-  输入密码时，拉起键盘，输入区上移。键盘切换到英文键盘模式。- 输入框限制「不超过16位数字和字母」，输入超过16位数字，键盘不收起- 默认密码不显示，用“*”替占位- 输入至少1位后- 输入框右侧出现「显示密码」，点击显示密码内容；按钮切换为「隐藏密码」，点击隐藏密码内容；<br/><br/></td></tr>
<tr>
<td>服务协议区<br/><br/></td><td>![in_table_RwKPbLxAWo0LXkxHlpscBiahnFe](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_RwKPbLxAWo0LXkxHlpscBiahnFe.png)<br/><br/></td><td>- 入口文案- 已阅读并同意【服务协议】和【隐私协议】<br/>- 入口勾选框- 默认不勾选<br/>- 点击「用户协议」或「隐私协议」，进入对应协议页面- 协议页面支持上下滑动，有进度条- 点击后退或物理键盘后退回到登录页<br/>- 若为弱网或者断网状态，进入缺省页- 文案：哎呀！加载失败- 按钮：点击刷新- 点击后，刷新页面，拉取数据<br/>- 具体内容待上线前补充<br/></td></tr>
<tr>
<td>登录按钮<br/></td><td>![in_table_WYBKbYbQxoYFnpxDAltcfTl8nhb](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_WYBKbYbQxoYFnpxDAltcfTl8nhb.png)<br/>![in_table_FXsKbSFlWot1lVxY2yycsDD9nbg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_FXsKbSFlWot1lVxY2yycsDD9nbg.png)<br/>![in_table_LiukbiCqaoxB05xywmzck8Nfn3d](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_LiukbiCqaoxB05xywmzck8Nfn3d.png)<br/>异常状态<br/>![in_table_E0WtbcLv1oGHeLxZer3cl3g3nYe](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_E0WtbcLv1oGHeLxZer3cl3g3nYe.png)<br/>![in_table_LVj5bI33UoA0B2xlvVZcZSL3nOf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_LVj5bI33UoA0B2xlvVZcZSL3nOf.png)<br/></td><td>- 当账号密码输入框至少有一个为空时，登录按钮置灰，无法点击 <br/>- 当账号密码输入框均有内容时，登录按钮为高亮状态，点击判断服务协议区勾选状态- 若已勾选，则拉取接口做校验；若未勾选，则弹窗引导同意协议，点击按钮后默认勾选并拉取接口做登录校验- 若网络关闭、异常或报错，则toast提示：“请检查网络后再试”；- 若服务报错，则toast提示：“服务异常请稍后重试”；- 若网络开启且账号密码有误，则toast提示：“请输入正确的账号密码”；- 若均无误，登录成功，则页面跳转到「Launcher首页」；- 键盘无论是否拉起，都可以点击登录；<br/><br/></td></tr>
</table>

![in_table_SkQebLwG3ocTQVxkpd8cK9XbnQf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_SkQebLwG3ocTQVxkpd8cK9XbnQf.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为用户登录界面的UI设计稿。其关键元素和组成部分结构如下：

*   **页面整体：登录界面**
    *   **设备状态栏 (顶部)**
        *   左上角：时间与日期显示
            *   时间: "7:35"
            *   日期: "Mon Jun 3"
        *   右上角：系统图标
            *   Wi-Fi信号图标 (通常存在，根据图片样式推断)
            *   电池电量: "100%" 及电池图标
    *   **应用内容区**
        *   **顶部导航/功能区**
            *   右上角：网络设置入口
                *   图标：齿轮设置图标
                *   文本："网络设置"
        *   **Logo区域 (居中)**
            *   占位符文本："LOGO占位"
        *   **登录表单 (居中)**
            *   **账号输入框**:
                *   提示文字："请输入账号"
            *   **密码输入框**:
                *   提示文字："请输入密码"
                *   右侧：密码可见性切换图标 (眼睛样式)
        *   **操作区域**
            *   **登录按钮**:
                *   按钮文字："登录"
            *   **协议同意区域**:
                *   勾选框 (圆形，当前未勾选状态)
                *   文本："勾选同意 用户服务协议 与 隐私协议"
                    *   "用户服务协议" (可点击的链接样式)
                    *   "隐私协议" (可点击的链接样式)

【============== 图片解析 END ==============】

![in_table_KWXwbrvdNof5skx0XRmcrOEGnaf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_KWXwbrvdNof5skx0XRmcrOEGnaf.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个用户登录界面，主要包含以下层级结构化的元素：

1.  **状态栏信息**
    *   左上角：时间 "7:35 Mon Jun 3"
    *   右上角：Wi-Fi图标、电池电量 "100%"

2.  **登录表单区域**
    *   **用户名输入框**：
        *   占位符或已输入内容："nk81004"
    *   **密码输入框**：
        *   占位符或已输入内容："0618033"
        *   右侧包含一个密码可见性切换图标（眼睛样式）。
    *   **登录按钮**：
        *   按钮文本："登录"
    *   **协议勾选区域**：
        *   勾选框：已勾选状态，显示一个绿色对勾。
        *   文本描述："勾选同意 用户服务协议 与 隐私协议"
            *   "用户服务协议"：可点击链接。
            *   "隐私协议"：可点击链接。

3.  **虚拟键盘区域**
    *   标准QWERTY键盘布局。
    *   包含字母、数字、符号按键。
    *   特殊功能键：
        *   左下角："?123" (切换到数字/符号键盘)
        *   "中/英" (中英文切换)
        *   空格键 (带有麦克风图标，可能支持语音输入)
        *   表情符号键
        *   右下角：蓝色高亮的 "下一项" 按钮。
        *   删除键
        *   Shift键
    *   键盘上方工具栏：
        *   左侧：应用切换或多任务图标。
        *   右侧：隐藏键盘或下拉建议图标。

【============== 图片解析 END ==============】

![in_table_AysKbVSQmotDiXxsckLcFMA4ndb](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_AysKbVSQmotDiXxsckLcFMA4ndb.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】
*   **主容器 (Main Container)**: 整体为一个圆角矩形区域。
    *   **上部区域 (Upper Area)**:
        *   **文本内容 (Text Content)**: 显示为 "nk81004"。
    *   **分隔线 (Separator Line)**: 一条水平细线，区隔上下区域。
    *   **下部区域 (Lower Area)**:
        *   **掩码字符 (Masked Characters)**: 显示为七个点状字符 (`• • • • • • •`)。
        *   **图标 (Icon)**: 显示为一个“闭眼”图标，位于掩码字符的右侧。
【============== 图片解析 END ==============】

![in_table_LWqNbjV3wotALoxi0YjcHF3Fn6f](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_LWqNbjV3wotALoxi0YjcHF3Fn6f.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

**图片内容描述：**
该图片展示了一个用户登录表单的UI设计局部。

**关键元素及构成：**

1.  **输入区域** (白色圆角矩形背景):
    *   **账号输入字段** (上部):
        *   已填内容: `nk81004`
    *   **分隔线**: 一条水平细线，区隔上下输入字段。
    *   **密码输入字段** (下部, 位于分隔线下方):
        *   已填内容: `0618033`
        *   辅助图标: 字段右侧有一个眼睛形状的密码可见性切换图标。
2.  **操作按钮**:
    *   **登录按钮** (橙色圆角矩形，位于输入区域下方):
        *   按钮文本: "登录"

【============== 图片解析 END ==============】

![in_table_CBL2bYwFFoLjoIxAFisc5huFnxc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_CBL2bYwFFoLjoIxAFisc5huFnxc.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个用户界面（UI）输入组件，具体构成如下：

1.  **组件外观**：
    *   一个具有圆角的矩形框体，内部区域为白色。

2.  **组件内部结构**：
    *   **上部区域（已输入内容区）**：
        *   **显示文本**：为字符串 "k81004810048100481004810048100481"。
        *   **光标**：文本末尾有一个橙色的垂直条状光标，指示当前为活动输入状态。
    *   **中部区域（分隔元素）**：
        *   **分隔线**：一条细的浅灰色水平线，区隔上下部分。
    *   **下部区域（提示与控件区）**：
        *   **提示文字（左侧）**：浅灰色文本 "请输入密码"。
        *   **功能图标（右侧）**：一个闭眼形状的图标，通常用于密码可见性切换。

【============== 图片解析 END ==============】

![in_table_RwKPbLxAWo0LXkxHlpscBiahnFe](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_RwKPbLxAWo0LXkxHlpscBiahnFe.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个应用程序的界面，具体元素如下：

*   **屏幕顶层（系统状态栏）：**
    *   左侧：显示时间 "7:35" 和日期 "Mon Jun 3"。
    *   右侧：显示 Wi-Fi 图标 (视觉识别) 和电池电量 "100%" 及电池图标。
*   **页面标题（主内容区外部）：**
    *   左上角：文本 "登录"。
*   **主内容区域（白色背景）：**
    *   **导航栏：**
        *   左侧：一个向左的箭头图标，用于返回。
        *   中间：标题文本 "用户服务协议"。
    *   **内容主体：**
        *   中央区域：有浅色文字 "嵌入页面"。

【============== 图片解析 END ==============】

![in_table_WYBKbYbQxoYFnpxDAltcfTl8nhb](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_WYBKbYbQxoYFnpxDAltcfTl8nhb.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了用户登录界面的一部分，主要包含以下关键元素及其层级关系：

1.  **登录按钮**:
    *   **文本**: “登录”
    *   **功能**: 用户点击后触发登录流程。

2.  **协议同意区域**:
    *   **勾选框**:
        *   **初始状态**: 未勾选。
        *   **关联文本**: “勾选同意”
    *   **协议文本**:
        *   “用户服务协议”
        *   “与” (连接词)
        *   “隐私协议”
    *   **功能**: 用户需勾选此框以表示同意“用户服务协议”与“隐私协议”，通常是执行“登录”操作的前提条件。其中“用户服务协议”和“隐私协议”一般为可点击链接，指向具体的协议内容。

**元素间关联**:
*   “协议同意区域”位于“登录按钮”下方。
*   用户需要勾选“协议同意区域”中的勾选框，才能成功执行“登录按钮”代表的操作。

【============== 图片解析 END ==============】

![in_table_FXsKbSFlWot1lVxY2yycsDD9nbg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_FXsKbSFlWot1lVxY2yycsDD9nbg.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

此图片展示了一个用户界面（UI）片段，包含以下关键元素及其层级关系：

1.  **主要行动按钮区域**
    *   **行动按钮 (顶部)**:
        *   外观: 橙色背景，圆角矩形。
        *   内容: 中央有三个水平排列的圆点，第一个圆点为实心白色，后两个为半透明白色，通常表示加载中或处理中状态。
2.  **协议勾选状态区域 (底部)**
    *   **勾选状态指示符**:
        *   图标: 绿色圆形背景，内含白色对勾（✓）。
        *   含义: 表示用户已勾选或同意。
    *   **协议文本**:
        *   内容: "勾选同意 用户服务协议 与 隐私协议"
        *   组成部分:
            *   "勾选同意": 提示用户已执行勾选操作以表示同意。
            *   "用户服务协议": 指向用户服务协议的文本或链接。
            *   "与": 连接词。
            *   "隐私协议": 指向隐私协议的文本或链接。

**元素间关联**:
*   底部的“协议勾选状态区域”通常是触发顶部“主要行动按钮”可用性的前置条件。图片中显示已勾选同意。
*   顶部的“行动按钮”上的加载状态指示器（三个点）表明，在用户勾选同意协议后，可能已经触发了某个操作，该操作正在进行中，或者是按钮本身正在等待某个条件完成后变为可交互状态。

【============== 图片解析 END ==============】

![in_table_LiukbiCqaoxB05xywmzck8Nfn3d](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_LiukbiCqaoxB05xywmzck8Nfn3d.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】
该图片展示了一个用户登录前需进行同意操作的界面局部。

1.  **提示信息气泡**
    *   形态：白色圆角矩形气泡，指向右下方。
    *   文本内容：“请勾选同意后登录”

2.  **同意选项区域**
    *   **勾选控件**
        *   形态：一个未勾选的圆形选择框。
    *   **描述文本**
        *   文本内容：“勾选同意 用户服务协议 与 隐私协议”
            *   其中，“用户服务协议” 和 “隐私协议” 通常为可点击链接，指向对应协议的详细内容。

**元素间关联：**
*   “提示信息气泡” 指示用户操作 “同意选项区域”。
*   用户需点选 “勾选控件” 以表示同意 “描述文本” 中提及的 “用户服务协议” 与 “隐私协议”，然后才能进行登录操作。
【============== 图片解析 END ==============】

![in_table_E0WtbcLv1oGHeLxZer3cl3g3nYe](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_E0WtbcLv1oGHeLxZer3cl3g3nYe.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为“小鹿爱学”应用的登录界面UI。

1.  **状态栏区域** (界面顶部)
    *   左上角显示时间：“7:35 Mon Jun 3”
    *   右上角显示系统状态：Wi-Fi图标、电池电量“100%”
2.  **页面主体内容区域**
    *   **网络提示信息** (位于应用名称上方)
        *   文本内容：“请检查网络后再试”
        *   此元素为一个胶囊状的提示条。
    *   **应用名称**
        *   文本内容：“小鹿爱学”
    *   **登录表单区域** (位于应用名称下方，白色圆角矩形背景)
        *   **账号输入框**
            *   当前展示内容：“nk81004”
        *   **密码输入框**
            *   当前展示内容：“· · · · · ·”（掩码形式）
            *   右侧有一个可切换密码可见性的眼睛图标。
        *   **登录按钮** (位于密码输入框下方，橙色圆角矩形)
            *   文本内容：“登录”
        *   **协议勾选区域** (位于登录按钮下方)
            *   勾选框：已勾选状态 (绿色勾号图标)
            *   文本：“勾选同意”
            *   链接文本1：“用户服务协议”
            *   连接词：“与”
            *   链接文本2：“隐私协议”
    *   **网络设置链接** (位于界面右上角，应用名称右上方)
        *   图标：齿轮设置图标
        *   文本内容：“网络设置”

**元素间关联：**

*   “请检查网络后再试”提示信息出现时，用户可能需要点击“网络设置”进行网络相关操作。
*   用户在“账号输入框”和“密码输入框”中输入凭据。
*   用户需“勾选同意 用户服务协议 与 隐私协议”（当前已勾选）才能进行登录。
*   点击“登录”按钮会尝试使用输入的账号和密码进行登录。

【============== 图片解析 END ==============】

![in_table_LVj5bI33UoA0B2xlvVZcZSL3nOf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_LVj5bI33UoA0B2xlvVZcZSL3nOf.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个移动应用或平板应用的登录界面，关键元素及组成部分层级如下：

1.  **系统状态栏** (界面顶部)
    *   **时间与日期**：左上角显示 `7:35 Mon Jun 3`。
    *   **系统图标**：右上角显示 Wi-Fi 连接图标和电池电量 `100%` 及电池图标。

2.  **登录界面主体**
    *   **顶部提示信息**：一个橙色背景的提示条，文字内容为：“请输入正确的账号密码”。
    *   **应用名称/Logo**：大号字体居中显示 “小鹿爱学”。
    *   **登录信息输入区** (白色圆角矩形背景)：
        *   **账号输入框**：上方输入行，已输入内容为 `nk81004`。
        *   **密码输入框**：下方输入行，内容以 `......` (点状掩码) 形式显示，输入框右侧有一个眼睛图标（用于切换密码可见性）。
    *   **登录按钮**：一个较大的橙色圆角按钮，文字为 “登录”。
    *   **协议同意区** (位于登录按钮下方)：
        *   **勾选框**：显示为已勾选状态（绿色对勾图标）。
        *   **协议文本**：紧随勾选框的文字为 “勾选同意 用户服务协议 与 隐私协议”。
    *   **网络设置入口**：位于界面右上角，包含一个齿轮图标，旁边文字为 “网络设置”。

【============== 图片解析 END ==============】



#### 5.2 MDM 场景学生账号流程

学生端需要区分 MDM 管控场景和非管控场景
管控场景下学生在设备中将小鹿爱学 app 作为 launcher，即仅支持访问小鹿爱学页面，无法与设备内其他模块接触（包括且不限于设置、浏览器、其他第三方 App）
在此基础上，登录页需要新增网络设置按钮，以确保用户有办法在登录前接入无线网络。
同时需要确保未来安装包能够区分是否是服务于管控场景。

<table>
<tr>
<td>模块<br/></td><td>原型图<br/></td><td>详述<br/></td></tr>
<tr>
<td>管控场景-网络设置按钮<br/><br/></td><td>![in_table_GyyhbPSjMoxETQxRGBRccgusn9f](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GyyhbPSjMoxETQxRGBRccgusn9f.png)<br/></td><td>登录页右侧增加网络设置按钮，点击触发网络设置<br/><br/></td></tr>
</table>

![in_table_GyyhbPSjMoxETQxRGBRccgusn9f](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GyyhbPSjMoxETQxRGBRccgusn9f.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为“小鹿爱学”应用的登录界面UI。

1.  **状态栏区域 (顶部)**
    *   **时间显示**: "7:35 Mon Jun 3"
    *   **网络状态**: Wi-Fi 图标
    *   **电池电量**: "100%" 及电池图标

2.  **页面主体区域 (中部)**
    *   **应用名称**: "小鹿爱学"
    *   **登录表单框**:
        *   **账号输入框**: 包含预设文本 "nk81004"
        *   **密码输入框**: 密码以圆点（"• • • • • •"）形式遮蔽，右侧有密码可见性切换图标（闭眼图标）
        *   **登录按钮**: 橙色背景，文字为 "登录"
        *   **协议勾选区**:
            *   **勾选框**: 已勾选状态
            *   **文本**: "勾选同意"
            *   **链接**: "用户服务协议"
            *   **文本**: "与"
            *   **链接**: "隐私协议"
    *   **网络设置按钮 (右上角)**:
        *   **图标**: 设置齿轮图标
        *   **文本**: "网络设置"

**元素间关联阐述:**

*   整个界面是用户登录"小鹿爱学"应用的入口。
*   顶部状态栏显示当前设备的基础信息。
*   应用名称 "小鹿爱学" 明确了产品身份。
*   登录表单框是核心交互区域，用户需在此输入账号和密码。
    *   账号输入框用于填写用户账号。
    *   密码输入框用于填写用户密码，密码可见性切换图标可控制密码的显示/隐藏。
    *   "登录"按钮用于提交表单信息以完成登录操作。
    *   协议勾选区是登录的前提条件，用户需勾选同意"用户服务协议"与"隐私协议"才能进行登录，协议文本为可点击链接，指向相应协议详情。
*   "网络设置"按钮提供了一个访问网络相关配置的入口。

【============== 图片解析 END ==============】


**管控场景下首次登录逻辑**
![FVgHwDveNh46FmboDM6c6Cq1ngh](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_FVgHwDveNh46FmboDM6c6Cq1ngh.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】
该图片是一个业务流程图，清晰地展示了平板设备上用户登录及应用启动的完整过程，涉及四个关键系统模块的交互。

**关键元素与组成部分层级化解析：**

1.  **泳道（参与模块）**：流程图在逻辑上划分为四个并行的泳道，代表了不同的系统组件或处理阶段。
    *   **MDM平台**：作为流程的起点，并进行首次登录状态判断。
    *   **平板内产品launcher登录模块**：负责处理用户通过产品内置Launcher进行的登录认证流程。
    *   **平板内MDM（移动设备管理）**：负责在产品登录后，设备向MDM后台进行注册或信息更新，并最终负责拉起主应用程序。
    *   **平板内产品主程序**：表示最终用户使用的应用程序的启动和自动登录过程。

2.  **核心流程节点与关联**：

    *   **起始与首次判断（MDM平台）**
        *   流程从“开始”节点启动。
        *   进入“已登录”判断节点：
            *   如果 **是 (Y)**：流程直接跳转至“平板内MDM”模块的“拉起产品主程序并浮于桌面之上”步骤。
            *   如果 **否 (N)**：流程进入“平板内产品launcher登录模块”。

    *   **Launcher登录流程（平板内产品launcher登录模块）**
        *   “唤起登录页面”。
        *   用户“输入产品账号和密码”。
        *   系统判断“登录成功”。
        *   成功后，“向MDM传递账号字段信息”。此信息传递给“平板内MDM”模块。

    *   **MDM交互与应用拉起（平板内MDM）**
        *   接收到账号信息后，“自动向MDM后台发起注册/更新”。
        *   进入“注册/更新成功”判断节点：
            *   如果 **是 (Y)**：“拉起产品主程序并浮于桌面之上”。流程随后进入“平板内产品主程序”模块。
            *   如果 **否 (N)**：流程返回至“自动向MDM后台发起注册/更新”步骤，进行重试。

    *   **主程序启动（平板内产品主程序）**
        *   “启动主程序”。
        *   “自动登录成功”。
        *   流程至“结束”节点。

**Mermaid 流程图描述：**

```mermaid
flowchart TD
    subgraph MDM平台
        A[开始] --> B{已登录};
    end

    subgraph 平板内产品launcher登录模块
        C[唤起登录页面] --> D[输入产品账号和密码];
        D --> E[登录成功];
        E --> F[向MDM传递账号字段信息];
    end

    subgraph 平板内MDM
        G[自动向MDM后台发起注册/更新] --> H{注册/更新成功};
        H -- Y --> I[拉起产品主程序并浮于桌面之上];
        H -- N --> G;
    end

    subgraph 平板内产品主程序
        J[启动主程序] --> K[自动登录成功];
        K --> L[结束];
    end

    B -- N --> C;
    B -- Y --> I;
    F --> G;
    I --> J;
```
【============== 图片解析 END ==============】


具体用户流程为：接收平板 - 打开平板 - 登录页面- 设置网络 - 使用老师下发的账号密码登录-自动完成（「学生」-「账号」-「MDM」-「平板」）的绑定  -  进入产品首页
完成以上流程后，平板无法自主退登及切换其他账号。
**解除管控流程**
通过 MDM 后台人工完成解绑，支持批量解绑
需要在产品开发后完成相关流程校验

#### 5.3 登录判定逻辑

当账号和密码输入正确时，逐次以下校验逻辑

- 校验当前账号下是否包含家长角色或学生角色- 若包含学生角色，且学生角色存在订单，订单状态处于试用中或付费中状态，则默认以学生身份及其所属组织下的信息完成登录（第一阶段仅会有一个组织）；其余均无法登录成功，弹出 toast“服务异常请稍后重试”；

注：未来拓展方向

1. 家长角色，支持以家长角色登录 1. 当账号下仅存在家长角色，登录后默认进入家长角色相关场景。学生端后续会单独设计，开发完成后该逻辑生效。
2. 账号下多角色或多角色 + 多组织 1. 例如两个家长角色、一个学生角色下同时有两所学校，则完成账号密码登录后弹窗从 4 个角色中引导选择，选择后提取相关信息登录

### 六、数据需求

#### 6.1 效果追踪

记录登录触发事件及失败事件

#### 6.2 数据埋点

好的，以下是增加了登录页面展示埋点后的完整代码埋点采集方案：

|事件标识符	|事件名	|事件类型	|埋点触发时机	|属性标识符	|属性名	|属性类型	|属性备注|
|:---:	|:---:	|:---:	|:---:	|:---:	|:---:	|:---:	|:---:|
|login_page_view	|登录页面展示	|0	|登录页面加载完成	|-	|-	|-	|-|
|login_page_network_setting_click	|网络设置点击	|0	|用户点击网络设置入口	|-	|-	|-	|-|
|login_page_account_input_click	|账号输入点击	|0	|用户点击账号输入入口	|-	|-	|-	|-|
|login_page_password_input_click	|密码输入点击	|0	|用户点击密码输入入口	|-	|-	|-	|-|
|login_page_show_password_click	|展示密码点击	|0	|用户点击展示密码按钮	|is_visible	|密码是否可见	|String	|值为"true"或"false"|
|login_page_login_button_click	|登录按钮点击	|0	|用户点击登录按钮	|account	|账号	|String	|用户输入的账号|
|	|登录按钮点击	|0	|用户点击登录按钮	|login_status	|登录状态	|String	|值为"success"或"failure"|

login_page_view：当登录页面加载完成时触发，用于记录用户进入登录页面的行为。该事件无属性，仅用于统计页面展示次数。

### 七、a/b 实验需求

暂无

### 附：评审记录

********** - 初评纪要**

1. 确定下账号密码生成规则

- 已敲定，共 10 位，前四位小写字母，后六位数字，去除易混字符 `i`, `l`, `o`

1. 埋点文档需要考虑如何确保不重名、结构不混乱

- 已完成，并确定好了相关规范