---
description: 
globs: frontend/**/*.css,frontend/**/*.js
alwaysApply: false
---
# 🧩 UI 组件生成规则（适用于 `apps/[应用名称]/app/components/` 与业务组件子目录）

## 📁 目录结构与位置规范

1. **通用组件**：
   - 所有可复用、无业务依赖的组件放入：`apps/[应用名称]/app/components/`
   - 每个组件独立子目录，目录名为组件名的 `kebab-case` 形式，例如：`card/`、`button/`

2. **业务组件（绑定业务上下文）**：
   - 放入业务视图目录下的 `components/` 子目录，例如：`apps/[应用名称]/app/views/dashboard/components/`
   - 命名方式同上，保持一致。

3. **每个组件目录应包含以下文件**：
   ```
   ├── [component].tsx               # 组件本体
   ├── [component].stories.tsx      # Storybook 示例
   ├── [component].test.tsx         # 可选：测试文件（如有逻辑）
   └── index.ts                     # 统一导出组件
   ```

## 📌 命名规范与数据变量

- 所有用于展示的数据字段，**根据设计图写死内容，但变量命名要精准反映业务含义**，如：
  ```tsx
  const userName = '张三';
  const statValue = '87%';
  ```

- 保持变量名语义清晰、一致性强，后期易于接入后端。
- 禁止使用无意义命名，如 `data1`、`text2`，必须有业务语义。

## 🔁 可复用组件策略

- 在生成新组件前，**先检查 `app/components/README.md` 中已有组件**是否可复用。
- 如果组件样式结构大体一致但有细微差异，应抽出基础组件，使用 `props` 或 `slots` 支持定制。
- **严禁重复创建已有组件功能的结构**（如重复定义按钮、卡片、标签等）。

示例：
```tsx
<Button type="primary" icon="plus">
  添加成员
</Button>
```

## 📖 `README.md` 自动维护规范

每次新增组件，需自动更新所在目录的 `README.md`，内容包括：

```md
## 已完成组件列表

- Card：通用卡片容器组件，用于包裹内容。
- Button：通用按钮组件，支持 icon、自定义类型等。
- StatPanel：仪表盘统计面板，用于展示数值摘要信息。
```

## 🧪 Storybook 示例要求

- 所有组件需编写 `*.stories.tsx` 文件
- 演示组件的典型用法（一个 default + 可选变体）
- 示例中使用写死数据（与变量名保持一致）

## 💡 其它注意事项

- 组件文件注释使用**简洁中文**说明结构与用途
- 所有样式应使用 Tailwind CSS
- 如果组件涉及动画，建议使用 `framer-motion`
- 所有组件必须有 `props` 类型定义（使用 TypeScript）
