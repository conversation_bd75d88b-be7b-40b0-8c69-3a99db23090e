---
【Cursor开发中rules配置文件】后端版（适配数据库mysql场景）
---

# 模块开发进度跟踪 & 规范检查

**模块名称:** [例如：用户认证模块 / Order Management Module]
**版本:** [例如：v1.0.0]
**负责人:** [开发者姓名]
**最后更新时间:** [YYYY-MM-DD]

---

## 整体状态概览

*   **设计阶段 (Phase 1):** [✅/⏳/❌]
*   **实现阶段 (Phase 2):** [✅/⏳/❌]
*   **测试阶段 (Phase 3):** [✅/⏳/❌]
*   **文档与评审 (Phase 4):** [✅/⏳/❌]
*   **集成与部署 (Phase 5):** [✅/⏳/❌]
*   **最终检查分析 (Phase 6):** [✅/⏳/❌]
*   **当前阻塞点:** [无 / 描述阻塞问题]

*(状态说明: ✅=全部完成, ⏳=进行中, ❌=未开始/有阻塞)*

---

## 详细开发阶段与任务状态

*(请使用 ✅=完成, ⏳=进行中, ❌=未开始/未完成 来标记各项状态)*

### 1. 需求与设计阶段
- [ ] **需求分析与澄清**:
- [ ] **领域建模 (DDD)** (`domain` 接口/模型):
- [ ] **API 设计**:
    - [ ] 接口定义 (路径/方法/参数/响应):
    - [ ] DTO 定义 (`model/dto`, 小写驼峰, UTC秒):
    - [ ] Swagger 初稿:
- [ ] **数据库设计** (如需):
    - [ ] 表结构设计 (snake_case, 时间字段, 软删):
    - [ ] 迁移脚本:
- [ ] **技术方案设计** (遵循原则):
- [ ] **方案评审**: (评审意见: [简述或链接])
- [ ] **架构决策记录 (ADR)**:

### 2. 编码实现阶段
- [ ] **项目结构** (遵循 DDD 分层):
- [ ] **常量定义** (`app/consts`):
- [ ] **DTO 实现**:
- [ ] **数据访问层 (DAL)** (GORM, 性能考虑):
- [ ] **领域逻辑** (`domain` 核心业务):
- [ ] **应用服务层** (CQRS Handlers, 编排):
- [ ] **接口层** (Kratos/Gin Endpoints, 参数校验):
- [ ] **错误处理集成** (统一错误码):
- [ ] **日志记录集成** (`logger_new`, 结构化, 上下文):
- [ ] **依赖注入 (Wire)**:
- [ ] **配置管理** (无硬编码):
- [ ] **代码注释** ("为什么"):

### 3. 测试阶段
- [ ] **单元测试** (>80% 覆盖率):
    - [ ] DAL 测试:
    - [ ] Domain 测试:
    - [ ] App Service 测试:
    - [ ] Mock 隔离:
- [ ] **集成测试** (关键业务流程):

### 4. 文档与评审阶段
- [ ] **API 文档 (Swagger)** 更新:
- [ ] **README 更新**:
- [ ] **Lint 检查** (`make lint` 通过):
- [ ] **代码审查 (Code Review)**:
    - [ ] PR 提交:
    - [ ] 评审完成 & 合并:

### 5. 集成与部署阶段
- [ ] **构建** (`make build` 成功):
- [ ] **集成联调** (测试/预发环境):
- [ ] **CI/CD 配置** (如需):
- [ ] **监控配置** (指标, 追踪, 告警):

---

## 6. 模块完成后检查与分析 (Post-Development Check & Analysis)

*该阶段在上述 1-5 阶段主要任务标记完成后进行，作为最终质量保证环节。*

- [ ] **最终功能验收**: 是否满足所有明确的需求和验收标准？
- [ ] **代码质量复核**:
    - [ ] 是否遵循 SOLID, KISS 原则？
    - [ ] 函数/文件长度、复杂度是否合规？
    - [ ] 注释是否清晰有效？无冗余/待优化代码？
- [ ] **规范符合性检查**:
    - [ ] 是否严格遵循 DDD 分层架构？无跨层依赖？
    - [ ] API 设计是否符合规范 (版本/格式/状态码)？
    - [ ] 错误处理和日志记录是否到位且符合标准？
    - [ ] 数据库设计和操作是否符合规范 (命名/字段/软删/性能/防注入)？
    - [ ] 安全性考虑是否充分 (输入验证等)？
- [ ] **测试覆盖率确认**: 单元测试覆盖率是否达标 (>80%)？关键集成测试是否通过？
- [ ] **文档完整性检查**: API 文档、README 是否更新并准确反映了最终实现？
- [ ] **可观察性检查**: 日志、监控、追踪是否满足需求？
- [ ] **知识沉淀**: 是否有值得记录到知识库的解决方案或经验教训？
- [ ] **遗留问题/技术债务**: 是否有已知的遗留问题或需要后续优化的技术债务？(记录如下)
    *   [记录点1]
    *   [记录点2]

---

## 备注、当前进行中、问题、风险点

*   **当前进行中任务详情:**
    *   [例如：正在实现 OrderService 的 CancelOrder 方法...]
*   **遇到的问题/阻塞:**
    *   [例如：依赖的库存服务接口响应超时，需要协调解决...]
*   **待确认事项:**
    *   [例如：需要与产品确认优惠券叠加规则...]
*   **风险点:**
    *   [例如：新引入的库可能存在性能瓶颈...]
*   **后续计划:**
    *   [例如：完成应用层开发；开始编写单元测试...]