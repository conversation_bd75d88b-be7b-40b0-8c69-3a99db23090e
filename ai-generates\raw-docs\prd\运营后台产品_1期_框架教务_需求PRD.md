运营后台产品_1期_框架&教务_需求PRD

**版本管理**

| 版本号 | 日期 | 变更人 | 变更类型 | 变更详情 |
| --- | --- | --- | --- | --- |
| V1.0.0 | 02-06 | 袁全 | 新建 | 新建文档 |
| V5.0.0 | 02-20 | 袁全 | 细化 | 补充核心页面UI图 |
| V5.0.1 | 03-03 | 袁全 | 更新图 | “学校管理员”UI图更新 |
|  | 03-04 | 袁全 | 细评会议 | 沟通结论：-本期不做角色配置，下一期再做「角色管理」模块-修改了UI图中：1.学科-教材管理 ：去掉添加学科，改为☑️学科2.班级课表-编辑：可重复 文案修改 临时调课 or 修改全部课表3.上传课表：添加文案：上传成功后将覆盖原课表 |
|  |  | 袁全 | 更新图：批量导入课表 | ![in_table_image_JuLEb4uryoep4LxGOugckqMBnFc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523913025.png) |
|  | 3-10 | 袁全 | 添加：批量导入-后台执行中提示 | ![in_table_image_VF7rbXXZLomp9VxzW1UcYuyvnpf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523913585.png) |
|  | 3-13 | 袁全 | 添加：角色管理 前端设计页 | 建议本期补充“角色管理”前端设计，避免后续不好改补充：角色管理页面，入口：系统权限管理 > 运营后台账号管理tab + 角色权限tab：点击查看修改 |

![analyze_in_table_image_JuLEb4uryoep4LxGOugckqMBnFc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523913025.png)

![analyze_in_table_image_VF7rbXXZLomp9VxzW1UcYuyvnpf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523913585.png)

![image_MNmwbc5AeoqTUhxpRkocVV9Dnrc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523914130.png)

![image_CTtib9WnKoQzs8xlCDicZtimngc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523914764.png)

**关联需求**

| 关联需求名称 | 所属PM | 需求进度 | 文档链接 |
| --- | --- | --- | --- |
| 账号需求 | 袁全 | 完成细评，开发中 |  |

**设计稿**

https://mastergo.com/file/149512448061157?fileOpenFrom=project&page_id=0%3A3

**相关文档**

[运营后台-字典](https://wcng60ba718p.feishu.cn/wiki/Oze8wV4eZitNEukQfTIc4oGgnMb)

### 一、背景和目标

#### 1.1、需求背景

运营后台的作用是连接：学校创建/管理 & 业务团队。

- 业务层面：
- 对于总部的运营：
- 合作校管理：承载了全部学校的创建和管理，支持各校变更合作关系的审批工作
- 权限管理：支持了对系统人员、单校运营人员 进行权限设置
- 对于单校的运营：
- 教务管理：可对单校的学期、教材、课表等信息进行设置
- 账号管理：可对单校的班级关系、老师和学生账号进行管理
- 技术层面：
- 基于中的P0部分，是整个系统底层最基本的数据关系
|  | 合作校管理 | 教务管理 | 账号管理（拆分至part2） | 权限管理 | 数据看板 | 其他 |
| --- | --- | --- | --- | --- | --- | --- |
| P0核心功能 | 合作校创建和管理管理合作状态 | 学期管理学科教材管理课表管理 | 学生管理教师管理用户登录 | 系统管理员（总部运营）学校管理员（驻校运营） |  |  |
| 未来功能 | 商机管理试用/签约流程 | 学年管理 |  | 销售/代理商人员审批人员 | 学校日常运营看板学校教师使用看板试用校数据预警 | 代理商管理考试学情 |

#### 1.2、项目收益

完成运营后台P0核心功能，确保在6月上线的版本可以跑通整个业务MVP流程



#### 1.3、覆盖用户

目标用户：

- 角色1:管理全部学校运营的总部运营人员
- 角色2:管理单个学校使用的学校运营人员
- 未来其他：各类合作流程的审批节点人


#### 1.4、方案简述

- 一个支持创建学校 和 合作关系管理的 CRM系统 
- 关键词：以学校为核心，进行数据构造
- 一个可管理公司权限：总部运营人员，单校权限：学校运营人员 的权限管理系统
- 关键词：区分角色权限
- 一个可管理学校内部：学期、教材、课表信息的 教务管理系统
**本期：P0核心功能 vs 学校账号生命周期**

![board_E7KLwM5SmhGGbabopAccTkA5nBc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523910558.png)

附：运营平台--全景图，可见[新产品_运营后台](https://fqspoay1wny.feishu.cn/docx/VGJ8d3FYioel9bxM4jjcMQdBn2d#share-Te8idvHcCoYPhSxMiFEcBc7snle)



### 二、名词说明

- 角色
- 不同角色权限的人员，在登入运营后台后的页面权限/操作权限不同，本期主要支持两类
- 角色1:总部运营人员：所有学校的数据权限
- 角色2:学校运营人员：某一所学校的数据权限
- 实体
- 合作校：录入到系统中的学校
- 若一个学校同时拥有：初中部、高中部，拆分为两个学校id创建
- 一般以“学年”为单位更新教务数据
- 班级结构：学校 > 年级 > 班级 > 学生账号
- 年级：（高一\高二\高三\高三复读，初一\初二\初三\初四 ）
- 班级：本期仅支持「行政班」模式
- 学生关系：一个学生只属于一个行政班；通过行政班创建账号，可转班
- 老师关系：由班主任管理、配置多个学科的老师
- 未来可能扩展「教学班」模式（适配走班，同一课堂，部分学政治、部分学历史）
- 老师结构：学校 > 老师账号
- 职务：校长 / 年级主任 / 学科组长 / 学科教师 / 班主任...
- 老师，在一个学校内可以有多个职务，也可能跨学校授课
- 关键属性：
- 学段属性：初中、高中... ，受“学段”影响的属性：
- 年级
- 学科（初中叫：道德与法制，高中叫：政治；仅高中有：日语、俄语、西班牙语）
- 教材
- 真实/测试属性：
- 在学校、年级、班级、学生账号、教师账号中都具备这个字段，以便剔除测试数据
- 合作状态属性：
- 在学校、学生账号、教师账号中都具备这个字段，以便管理：该校 & 各账号生命周期
- 教材名称：同内容后台的知识树名称
- 数据来源：内容后台
- 各类课程、题 以多级知识点的形式挂在知识树上


### 三、业务流程

**角色 & 核心页面关系图**

- 总部运营管理：可查看全部合作校、具备创建学校权限；可设置系统管理员
- 学校运营管理：只能看我的合作校；只可设置 单校的管理员
![board_TKtBwvVuPhnEhob1EOgcww3Knuf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523911626.png)





**各模块核心页面 及 属性、操作**

![board_HPbfwEfnohRjeLbvZJucbnhRntg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523912415.png)



### 四、需求概览

| 序号 | 需求名称 | 内容概述 | 优先级 |
| --- | --- | --- | --- |
| 1 | 运营后台框架 | 区分角色展示运营后台 | P0 |
| 2 | 权限管理 |  | P0 |
| 3 | 合作校管理 |  | P0 |
| 4 | 教务管理 |  | P0 |
|  | 账号管理拆分至 |  | P0 |

- 角色1：总部运营人员
- 角色2：学校运营人员
- 总部运营人员 的管理
- 学校运营人员 的管理
- 角色1：总部运营人员
- 创建学校
- 编辑合作状态
- 管理学校人员
- 角色2：学校运营人员
- 申请合作流程
- 管理学校人员
- 学期管理
- 学科-教材管理
- 课表管理
- 学生管理
- 教师管理
### 五、详细产品方案

#### 5.1 运营后台框架

用户完成登录验证后，根据其身份，展示不同页面：

- 总部运营人员（角色1）：默认展示“合作校管理”
- 学校运营人员（角色2）：默认展示“我的合作校”
边界说明：若用户同时具备两种角色，以角色1权限进行页面展示

优先级：P0为本期必须需求，P2为后续需求

| 角色 | 一级导航 | 二级导航 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- | --- |
|  | 合作校管理 |  | 创建合作校查看全部学校编辑学校信息查看学校详情 | ![in_table_image_KI8FbVKjyoIGtJxdJzMcS9hbnOc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523915339.png) | P0 |
|  |  | >进入:xx学校后教务管理账号管理权限管理 | 详见5.4 | ![in_table_image_AmHEbndW2o4fo3xW6STcoVNWn5b](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523916068.png) | P0 |
|  | 系统权限管理 | -- | 添加系统管理员编辑权限取消授权 | ![in_table_image_Qme7b2ibBo9SXaxKZXVcktmLnwg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523916640.png) | P1 |
|  | 审批管理 | 后续需求补充 | 配置审批工作流查看全部审批查看我的审批 |  | P2 |
|  | 数据看板 | 同上 | 查看数据看板 |  | P2 |
|  | 我的合作校 |  | 查看我的合作校查看学校详情 | ![in_table_image_CAbqb2pzeoh58axfFQ1c33uHnzh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523917193.png) | P0 |
|  |  | >进入:xx学校后教务管理账号管理权限管理 | 详见5.4 | ![in_table_image_SnnfbioaJomiUUxP606cGzCJnGb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523918294.png)

（学校管理员） | P0 |
|  | 我的审批 | 后续需求补充 | 查看我的审批 |  | P2 |
|  | 数据看板 | 同上 | 查看数据看板 |  | P2 |

- 总部运营人员（角色1）
![analyze_in_table_image_KI8FbVKjyoIGtJxdJzMcS9hbnOc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523915339.png)

![analyze_in_table_image_AmHEbndW2o4fo3xW6STcoVNWn5b](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523916068.png)

![analyze_in_table_image_Qme7b2ibBo9SXaxKZXVcktmLnwg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523916640.png)

- 学校运营人员（角色2）
![analyze_in_table_image_CAbqb2pzeoh58axfFQ1c33uHnzh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523917193.png)

![analyze_in_table_image_Z6bmbjTbRo7rhgxIKKUc8r5KnQe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523917767.png)

![analyze_in_table_image_SnnfbioaJomiUUxP606cGzCJnGb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523918294.png)

#### 5.2 权限管理

| 角色 | 一级导航 | 二级导航 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- | --- |
|  | 系统权限管理 | 运营后台账号管理 | 添加和管理：可进入运营后台的人员编辑权限离职 | ![in_table_image_JnGBb9Z8ko6MV1xepepcQp9Pnbd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523919375.png) | P1 |
|  |  | 角色权限 | 对系统中的角色进行添加 和 管理 | ![in_table_image_VAPmb0Lfvofnq2x52mGc5vFYnsh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523920474.png) |  |
| & | xx学校 | >进入:xx学校后权限管理（创建学校管理员） | 前置要求添加学校管理员：抽屉中添加编辑权限取消授权 | ![in_table_image_AVNyb3JLco0yslx978ycLhxGnMh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523921890.png) | P0 |

- 总部运营人员（角色1）
![analyze_in_table_image_VINTbx4hJo2rSRxDx3rcdmNon4J](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523918828.png)

![analyze_in_table_image_JnGBb9Z8ko6MV1xepepcQp9Pnbd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523919375.png)

![analyze_in_table_image_EOJObwLY0oIKgaxvwSfcJLn0nie](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523919868.png)

![analyze_in_table_image_VAPmb0Lfvofnq2x52mGc5vFYnsh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523920474.png)

- 总部运营人员（角色1）
- 学校运营人员（角色2）
![analyze_in_table_image_W0zdbKjvDoAF5xxVcZdcBKT7nbh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523921196.png)

![analyze_in_table_image_AVNyb3JLco0yslx978ycLhxGnMh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523921890.png)

#### 5.3 合作校管理

##### 角色1:总部运营人员

| 所属页面 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- |
| 合作校管理 | 创建新学校 | 操作：1.点击“创建新学校”，在弹窗中展示如下信息，点击“提交”2.提交成功后，后台完成创建学校，并在当前页面创建一条最新学校的数据，刷新列表，展示在列表中（*代表必填字段）学校基本信息交互 | ![in_table_image_N2itbIV39oFt7txilZDc9BgWnFd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523923170.png) | P0 |
|  | 查询学校 | 操作：1.通过一些搜索&查询维度，实现快速查询学校 2.可在列表中查看基本信息，继而进入学校查看详情查询维度 & 交互合作学校列表 | ![in_table_image_LSZ2bJsraovMMkxo467capYrnyf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523923723.png) |  |
|  | 编辑：学校基础信息 | 操作：1.在「合作学校列表」中，点击「编辑」，打开弹窗：进入编辑模式交互 |  |  |
|  | 变更：合作状态 | 操作：1.在「合作学校列表」中，点击「合作」，打开弹窗：进入编辑模式2.在弹窗中，可以：合作信息（这部分和「账号需求」密切相关）交互 | ![in_table_image_ADUPb13GAotQpox3oEXcM4rQnec](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523925639.png) |  |

![analyze_in_table_image_N2itbIV39oFt7txilZDc9BgWnFd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523923170.png)

![analyze_in_table_image_LSZ2bJsraovMMkxo467capYrnyf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523923723.png)

![analyze_in_table_image_ZdL5bEZL0ou2nexsqfzcF65bnYg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523924453.png)

![analyze_in_table_image_GZQOb5HA0oqL5ixoL56cIJd4nfd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523925076.png)

![analyze_in_table_image_ADUPb13GAotQpox3oEXcM4rQnec](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523925639.png)

##### 角色2:学校运营人员

说明：页面功能和角色1大体一致，页面抬头：「我的合作校」

区别：

1.可以看到的学校数量少于角色1，随“合作”中是否设置本人展示

2.缺少：创建学校权限、修改合作状态权限

| 所属页面 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- |
| 我的合作校 | 创建新学校 | 无权限 |  | P0 |
|  | 查询学校 | 同角色1区别：数据权限 仅限  在合作信息中设置本人信息的学校 | ![in_table_image_J2CxbgftjoB59HxljtYctq5pnic](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523926161.png) |  |
|  | 编辑：学校基础信息 | 同角色1 |  |  |
|  | 合作：学校合作状态 | 同角色1区别：没有修改学校合作状态的权限，只能发起流程 |  |  |

![analyze_in_table_image_J2CxbgftjoB59HxljtYctq5pnic](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523926161.png)

#### 5.4 xx学校 > 教务管理

##### 学期管理

在学期管理之前，先需要设置学年：

业务背景：每年7月～8月，随各学校教学周期升学年（明年启动升学年功能）

功能说明：学年关联到学生账号毕业、重新分班等业务流程

本期设计：后台当统一当前学年为：****“2025～2026学年”****，无前端页面功能

对于学期管理：

业务背景：随学校教学周期，划分为：第一学期（秋季学期）、寒假、第二学期（春季学期）、暑假 四个学期

功能说明：学期关联到学生课表功能的展示和使用

| 所属页面 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- |
| xx学校下 > 教务管理 > 学期管理tab |  | 本期设计：基于当前学年，可查看 / 对各学期的起止日期进行设置 | ![in_table_image_FfxWbE13QogCTexYiSscLg48nXd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523927292.png) |  |

- 学期管理
- 对于「高中」：
- 「查看模式」
- 默认展示：“高一、高二、高三、复读高三”
- 对每个年级的各学期：默认日期都设置为“暂未设置”
- 点击“编辑”后进入「编辑模式」
- 可对4个学期的起止日期进行设置
- 可以在一个年级下点击“应用到其他年级”，在弹窗中勾选对应的年级，复制当前年级的数据到其他年级
- 点击“保存”后完成设置，点击取消不保存
- 对于「初中」：
- 「查看模式」默认展示：“初一、初二、初三、初四”
- 「编辑模式」操作同高中
![analyze_in_table_image_MhxYbtFOBopl3fxps7dcXiqpn5f](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523926755.png)

![analyze_in_table_image_FfxWbE13QogCTexYiSscLg48nXd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523927292.png)

##### 教材管理

业务背景：不同学校使用的教材版本各异，同校选文/理的学生也可能选用不同教材

功能说明：关联到 学生/教师可查看的教材版本，直接影响任务布置、课程学习

| 所属页面 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- |
| xx学校下 > 教务管理 > 教材管理tab |  | 本期设计： | ![in_table_image_COxlbu7JeoHMCaxGSgkcGJXxnRd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523929176.png) |  |

- 学科-教材管理
- 教材先关联到对应的学年，本期为“2024～2025学年”
- 对于「高中」：
「查看模式」

- 按4年级，区分tab展示对应各年级的设置结果，默认在“高一tab”
- 同年级内，分成三类展示：不分文理、文科班、理科班
- 每类均按学科依次展示：用户已勾选的 高中各学科的名称+教材列表：
- 例如：数学：新人教版A版、新人教B版...
- 学科顺序如下：语文、数学、英语、物理、化学、生物、政治、历史、地理、日语、俄语、西班牙语（后台请预留未来扩展学科）
对每个年级，点击“编辑”后进入「编辑模式」

- 可对三类班级，分别进行勾选设置
- 每类均按学科依次展示：当前高中内容库 已有的&正式发布的 各学科的名称+教材列表：用户可点击复选框进行选择
- 可勾选：数学（若不选，则可不选教材信息）
- 可重复勾选：新人教版A版、新人教B版、北师大版、全国乙卷总复习、北京版....
- 点击“保存”后完成设置，点击取消不保存
- 对于「初中」：
「查看模式」

- 年级tab依次展示：“初一、初二、初三、初四”
- 同年级内，不区分文科/理科
- 按学科依次展示：用户已勾选的 初中各学科的名称+教材列表：
- 例如：数学：新人教版A版、新人教B版...
- 学科顺序如下：语文、数学、英语、物理、化学、生物、道德与法制、历史、地理（后台请预留未来扩展学科）
「编辑模式」操作同高中

![analyze_in_table_image_Zis3bU8XEoPCmixT7MfcNjBqnAe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523927863.png)

![analyze_in_table_image_WDXUbKZtxo7dFGxnwSYchNxEnZf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523928461.png)

![analyze_in_table_image_COxlbu7JeoHMCaxGSgkcGJXxnRd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523929176.png)

##### 课表管理

业务背景：课表是承载课堂教学安排的重要部分，学生、老师两类角色，需要分别按班级、按老师查看课表

功能说明：关联学生：课表功能 & 教师：课堂学情的 数据监控&讲解

| 所属页面 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- |
| xx学校下 > 教务管理 > 班级课表tab | 按年级设置课表模版 | 本期设计： | ![in_table_image_XJufbUaPoomY7SxmtrtcXiyXnrb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523930301.png) |  |
|  | 按年级批量导入课表 | 在页面右侧，可点击“批量导入课表”，对当前年级各班级的课表进行批量导入      每_1_周循环1次：默认1，数字可修改为2、3、4、5、6通知提示框 | ![in_table_image_NgEtbnXkXooBHsxfaHocvHKznSf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523932496.png) |  |
|  | 按班级查看课表 |  | ![in_table_image_C8dzbR51woHd6Yx5CrqcOPIjn1F](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523933053.png) |  |
|  | 按班级编辑课表 | 在上述查看页面下，点击“编辑课表”，整个页面即进入「编辑模式」：该模式下，收起页面左侧页面名称：编辑课表-草稿-xx班级 | ![in_table_image_WwUgbdYNSoI29txOwnhcjSXOn8g](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523935750.png) |  |
|  | 按班级导入课表 | 基本同“按年级导入”，区别是：只能在这里上传这一个班的sheet | ![in_table_image_U3cjbEuJUomziWx1LUYcLSEjnNe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523936288.png) |  |
| xx学校下 > 教务管理 > 老师课表tab | 按老师查看课表 | 在当前学年下，可在页面左侧：搜索姓名 / 点击查看 列表中每个老师的课表页面右侧： | ![in_table_image_EH5hbb2SfoVk5dxeC9ocxK3Inhg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523936899.png) |  |

- 班级在页面左侧，按进行多级：收放展示
- 教材先关联到对应的学年，本期为“2024～2025学年”
- 再关联至对应年级\班级：
- 高一
- 真实班
- 1班
- ...
- 测试班
- 高二
- 高三
- 复读高三
- 选定对应年级后，在页面右侧，可查看 / 设置该年级的课表模版
- 年级名称
- 当前学期，当前第几周（例如：2.10～2.16），左右翻页，选日历跳转
- 设置年级课表模版
- 设置生效日期（修改下周以后的时间，否则误操作更改可能影响巨大）
- 上午
- 第一节课 06:45~7:20 可修改时间 可删除
- ...
- 添加课程
- 下午
- 晚上
- 点击“保存”即生效，刷新当前页面展示结果；取消不保存修改
- 冷启动状态：
- 不显示课表，显示“尚未设置模版，点击此处创建”
![analyze_in_table_image_RTOtbwtmkoQuhAxAeZscA3Fsnlf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523929750.png)

![analyze_in_table_image_XJufbUaPoomY7SxmtrtcXiyXnrb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523930301.png)

- 课分成4类：课程、学科自习、自由自习、休息
- 点击后，在弹窗中选择：
1. 下载模版：
1. 设置生效日期：
日历控件

可选起始日期需为：今天之后本学期的各个周一，其他日期时间不可选

1. 上传课程表：
文案：

点击、拖拽/复制文件到这里上传

上传成功后将覆盖原课表

（仅支持：xls、xlsx、csv）

上传过程中：展示进度条

校验初步异常：1.模版格式（比如每x周是否匹配正确）2.生效日期未填 3.模版sheet名称不匹配

上传完毕：展示该文件绿色icon，文件名称、大小，可删除

1. 导入数据
点击“导入”后，在上传完毕情况下，后台开始解析xls文件，并返回解析结果和失败原因：

第1行:表格共：10条数据，成功导入：10条，失败：xx条（xx标红色）

第2行:若有失败，展示链接“下载失败原因”，可在本地可打开文档：

名称：xx学校xx年级课表失败原因.xls

sheet页名称：同之前

页面内容：

1. 第一行：同之前
1. 第二行：课表列名+一列失败原因
以分号隔开，展示当前一行各类失败数据的原因

正确：学科自习/数学/李雷 ，以下为例子

1. 第1周周一老师不存在 （例子：错写为 李鹏）
1. 第1周周二老师未设置 （错写为 学科自习/数学/）
1. 第1周周三学科名有误 （错写为 数雪）
1. 第1周周四课程类型有误（错写为 自习/数学/李雷）
第3行:提示：课表草稿已导入（请核对班级课表后发布，方可生效：红字）

完成导入后，可“关闭”当前窗口

关闭窗口后，不在年级下展示课表预览，需点击进入对应班级查看

![analyze_in_table_image_VxDtbSoZfo0s52ximvbcfDiKnaf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523931497.png)

![analyze_in_table_image_E2MIbnyhoogg48xBV8ucEwWmn3b](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523932006.png)

![analyze_in_table_image_NgEtbnXkXooBHsxfaHocvHKznSf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523932496.png)

- 在页面左侧选定对应班后，则在页面右侧，展示该班的课表信息
- 班级名称
- 当前学期，当前第几周（例如：2.10～2.16），左右翻页，选日历跳转
- 课表详情：
- 对应4类课，做简单展示：课程（数学 李雷）、学科自习（数学自习 李雷）、自由自习（自习）、休息（空白）
- 列：2.10（周一）、2.11（周二）、...、2.16(周日)
- 行：
- 上午
- 第一节课（ 06:45~7:20 ）：英语自习 韩梅梅
- 第二节课（ 07:50~8:30 ）：数学 李雷
- ...
- 下午
- 第六节课（14:30～15:10）：体育 赵亮亮
- 第七节课（15:25～16:10）：音乐 陈晓红
- ...
- 第九节课（ 15:40~16:10 ）：自习 
- 晚上
- 边界：有的班，同一时段有两节课，则显示为两个格子：英语 韩梅梅 & 日语 柯南
![analyze_in_table_image_C8dzbR51woHd6Yx5CrqcOPIjn1F](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523933053.png)

- 当前学期，当前第几周（例如：2.10～2.16），左右翻页，选日历跳转
- 课表详情：
- 在课表上可一次点击多个课程格子（再点一次表取消），“填写课程内容”按钮亮起，可进入抽屉页编辑
- 校验：本周不可修改当天及之前的课程
- 抽屉页内容：
- 已选时间：
- 2月12日（周三）第四节课（10:35～11：20）
- 2月13日（周四）第四节课（10:35～11：20）
- 课表内容：
- 类型：课程 / 学科自习 / 自由自习 / 休息
- 对“课程”：还需点选学科 + 搜索输入教师名称
- 对“学科自习”：还需点选学科 + 搜索输入教师名称
- 对“自由自习”& “休息”：无需其他信息
- 新增并列课程：此处支持同一个班学生，上不同老师的课（走班）
- 点击“新增并列课程”，按上述类似方式设置即可
- 此处也可删除已设置的课，直至只有一课
- 是否临时调课（文案修改）：
- 可在下来菜单中选择：仅本周修改、修改全部课表
- 完成后，点击“确定”，即可在课表中预览草稿效果；点取消则不保存
- 草稿效果并不会在系统内生效，需要点击“发布课表”，才会生效
- 否则，点击“返回”即不生效
![analyze_in_table_image_IIETbZ6l1oNZ9RxZwsTcuDSKnyb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523933630.png)

![analyze_in_table_image_PcKvbX9uNoZPaZxue43cMtTcnre](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523934180.png)

![analyze_in_table_image_YnPHbTtjNoxks2xB5n6cmfThn3k](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523934940.png)

![analyze_in_table_image_WwUgbdYNSoI29txOwnhcjSXOn8g](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523935750.png)

![analyze_in_table_image_U3cjbEuJUomziWx1LUYcLSEjnNe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523936288.png)

- 老师名称
- id、手机号、数据类型（真实/测试）、教学班级：xx\xx\xx...
- 当前学期，当前第几周（例如：2.10～2.16），左右翻页，选日历跳转
- 课表详情：同班级视图
![analyze_in_table_image_EH5hbb2SfoVk5dxeC9ocxK3Inhg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523936899.png)



### 六、数据需求（暂不需要）

#### 6.1 效果追踪

描述计划监控数据效果的方式及所需数据字段

#### 6.2 数据埋点

| 页面 | 模块 | 动作 | 埋点名称 | 图示（如需） |
| --- | --- | --- | --- | --- |
|  |  |  |  |  |
|  |  |  |  |  |
|  |  |  |  |  |
|  |  |  |  |  |

### 七、a/b实验需求

若有，则写清楚实验方案

**实验目的**

XXX

**实验对象**

限制条件：端/版本/用户

实验组策略：

对照组策略：

**实验及分流**

启动时间、启动时用户量、预计持续时间

**评价指标**

结果指标（标明预期提升值）、过程指标

**暂无**



### 附：评审记录

记录存档历次需求评审中的会议纪要，方便追踪

举例：

**20250204 - 初评纪要**

1. 会后尽快确定巩固类型本期题目类型范围@杨宇航
已确认，并补充进文档

1. 数据下发方式待服务端和客户端最终确认@服务端rd @客户端rd
确定服务端下发，客户端仅做兜底逻辑

1. ...


**20250321 - 二次评审纪要**

1. XX
1. 
