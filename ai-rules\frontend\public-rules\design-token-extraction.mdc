---
description: 
globs: 
alwaysApply: false
---

## 1. 目的

本文档旨在规范如何从 Figma 中精确提取 Design Tokens，这是 Phase 2.2 的核心依据。通过遵循这些规则，确保设计与开发之间的设计元素（如颜色、字体、间距等）能够准确、系统地转化为可重用的设计令牌（Design Tokens），从而提高前端开发的效率和一致性。

## 2. 什么是 Design Tokens

Design Tokens 是设计系统中基本的设计元素的抽象表示，它们通过一组标准化的值来描述设计的颜色、字体、间距、阴影、尺寸等属性。Design Tokens 使设计和开发之间的沟通更为流畅，并确保前端实现与设计的高度一致。

## 3. 设计令牌提取的基本原则

### 3.1 统一性

提取的 Design Tokens 必须符合设计系统中的统一规范，确保在不同项目中，设计元素能够保持一致。例如，设计中使用的颜色、字体、间距等必须在不同页面或模块中保持相同的值和名称。

### 3.2 可扩展性

Design Tokens 的命名与结构应该具有良好的可扩展性，便于未来扩展或更新。例如，添加新的颜色、字体或间距时，不会破坏现有的规则和代码结构。

### 3.3 可读性与易用性

提取的 Design Tokens 应该具有良好的可读性和易用性，便于开发团队理解和使用。每个 Design Token 应有清晰的命名，并且能够简洁明了地表达其用途。

### 3.4 与设计稿的一致性

提取的 Design Tokens 必须严格遵循 Figma 设计稿中的设计元素，确保开发过程中不会出现与设计不符的情况。通过精确提取，保证设计与实现的完美对接。

## 4. 从 Figma 中提取 Design Tokens

### 4.1 提取颜色（Color Tokens）

在 Figma 中，颜色通常以样式定义，开发团队需要提取这些颜色并转化为 Design Tokens。具体步骤如下：

* **基础色**：从 Figma 中提取设计中定义的基础颜色，如主色、辅色、背景色、文本色等。
* **状态色**：包括不同状态下的颜色（如 hover、active、disabled、error、success 等），确保每种状态的颜色提取完整。
* **透明度**：提取透明度（例如 rgba 值中的 alpha 值），并以独立的 Design Token 进行表示。
* **命名规则**：颜色的命名应该直观清晰，如 `color-primary`, `color-secondary`, `color-background`, `color-text` 等。

#### 示例：

```json
{
  "color-primary": "#6200EE",
  "color-secondary": "#03DAC6",
  "color-background": "#FFFFFF",
  "color-text": "#000000"
}
```

### 4.2 提取字体（Typography Tokens）

字体相关的 Design Tokens 主要包括字体类型、字体大小、行高、字重等。具体步骤如下：

* **字体类型**：提取 Figma 中使用的字体家族，并为每个字体样式（如正文、标题、按钮等）设置不同的设计令牌。
* **字体大小**：提取各个组件和文本的字体大小。
* **行高与字重**：提取设计中定义的行高（line-height）和字重（font-weight）。
* **命名规则**：字体相关的 Design Tokens 应该以 `font` 开头，描述字体家族、大小、行高和字重等属性。

#### 示例：

```json
{
  "font-body": {
    "font-family": "Roboto, sans-serif",
    "font-size": "16px",
    "line-height": "1.5",
    "font-weight": "400"
  },
  "font-heading": {
    "font-family": "Roboto, sans-serif",
    "font-size": "24px",
    "line-height": "1.3",
    "font-weight": "700"
  }
}
```

### 4.3 提取间距（Spacing Tokens）

间距相关的 Design Tokens 包括内边距、外边距、间距单位等。应从 Figma 中提取页面元素之间的间距，并转化为设计令牌：

* **内外边距**：提取组件、容器、按钮等元素的内边距（padding）和外边距（margin）。
* **间距单位**：如果 Figma 中使用了统一的间距单位（例如 8px、16px 的倍数关系），则需要将这些间距转化为统一的 Design Token。
* **命名规则**：间距的命名应使用 `spacing` 前缀，如 `spacing-small`, `spacing-medium`, `spacing-large` 等。

#### 示例：

```json
{
  "spacing-small": "8px",
  "spacing-medium": "16px",
  "spacing-large": "32px",
  "spacing-xlarge": "64px"
}
```

### 4.4 提取阴影（Shadow Tokens）

阴影相关的 Design Tokens 描述了 Figma 中使用的阴影效果，包括阴影的颜色、偏移、模糊半径等：

* **阴影属性**：提取阴影的颜色、偏移量（horizontal, vertical）、模糊半径、扩展半径等。
* **命名规则**：阴影的命名应以 `shadow` 开头，如 `shadow-small`, `shadow-medium`, `shadow-large` 等。

#### 示例：

```json
{
  "shadow-small": "0 1px 2px rgba(0, 0, 0, 0.1)",
  "shadow-medium": "0 4px 6px rgba(0, 0, 0, 0.15)",
  "shadow-large": "0 10px 15px rgba(0, 0, 0, 0.2)"
}
```

### 4.5 提取边框（Border Tokens）

边框相关的 Design Tokens 主要包括边框的颜色、宽度、样式等。具体步骤如下：

* **边框颜色**：提取 Figma 中定义的边框颜色。
* **边框宽度**：提取不同组件的边框宽度。
* **边框样式**：例如实线、虚线等。
* **命名规则**：边框的命名应使用 `border` 前缀，如 `border-primary`, `border-radius` 等。

#### 示例：

```json
{
  "border-primary": "1px solid #6200EE",
  "border-radius-small": "4px",
  "border-radius-large": "12px"
}
```

## 5. Design Tokens 的存储与管理

### 5.1 统一管理

所有提取的 Design Tokens 应存储在统一的文件中（例如 `design-tokens.json`），并按照颜色、字体、间距、阴影等类别进行分类，方便在开发过程中统一引用。



