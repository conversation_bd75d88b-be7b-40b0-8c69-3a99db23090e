产课工具_3期_AI课配课+课程管理工具

[产课工具_1期_All-in-one](https://wcng60ba718p.feishu.cn/wiki/TBw0wvyxKi9BSuko87ScEy2unSf)

结合教研管理的需求：[产课流程](https://uathqw3qd4.feishu.cn/docx/SCxJdGjcnohMbRxLSHxcVRIAn8g)

技术文档：

**版本管理**

| 版本号 | 日期 | 变更人 | 变更类型 | 变更详情 |
| --- | --- | --- | --- | --- |
| V1.0.0 | 2025-4-18 | 袁全 | 新建 | 新建文档 |
| V5.0.0 | 4-24 | 袁全 | 升稿 | 完成核心页面交互图和prd说明 |
| V9.0.0 | 4-25 | 袁全 | 升稿 | 完成详细prd 和 修改各元素的影响说明 |
|  | 5-7 | 袁全 | 评审沟通修改 | ![in_table_image_SE2vbxWbColZTUxhxbrcneJInNf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844362164.png)

Cc |
|  | 5-8 | 袁全 | 课中练习：算法初始化题目数据 | 1.可继续复用现有配课工具交互界面2.工作流：后端读取 算法提供的：稿件 & 练习组件的位置、包含的题目id（id、顺序、【相似题组】）cc |

![analyze_in_table_image_Oyw3bPtyBortA7xTKhJcIBPCnfg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844361635.png)

![analyze_in_table_image_SE2vbxWbColZTUxhxbrcneJInNf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844362164.png)

**设计稿**

UE：https://mastergo.com/file/149512448061157?fileOpenFrom=home&page_id=0%3A2



### 一、背景和目标

#### 1.1、需求背景

本期区分不同角色，产品目标有两个：

1.完成供教研老师使用的AI课生产第3步工具，让审核老师、生产老师 2类角色可以在PC:web浏览器内输入对应课程地址后，****完成AI课生产的第3步：配课、查看整课预览。****

为确保快速上架，本期：配课模块、整课审核模块 访问地址只需内网vpn，不做任务管理；

2.由总部教研管理老师：****完成课程创建 和上架管理****。

为了确保线上课程数据安全，本期：**课程管理和上架模块，****需要对地址访问做登录验证+内网vpn。**

- 业务层面：
- 教研老师：
- 配题角色
- 审核角色
- 总部教研管理老师：
- 课程管理角色
|  | 教研老师（内部管理） | 教研老师（其他） |
| --- | --- | --- |
| P0核心功能 | 手机号登录验证课程创建课程修改课程上架/更新 | 配课工具审课地址 |
| 未来功能 |  | 审核工作流 |

#### 1.2、项目收益

完成产品工具P0核心功能，确保在6月上线的版本可以跑通整个产课业务MVP流程，可供老师使用

 

#### 1.3、覆盖用户

目标用户：教研教师、总部教研管理老师



#### 1.4、方案简述

1. 上下游关系：
1. 第3步  课程管理 （角色：总部教研管理）
1. 第4步  配课 （角色：教研生产 + 审课老师）
1. 第5步 上架（角色：总部教研管理）
![board_KsH2wbllvhB1o1b9eIQcPy0ynYe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844356945.png)

1. 用户访问路径：
课程管理：

1. 总部教研管理：手机号验证码登录vpn内网，完成课程ID创建；通过feishu表格管理课程id，并分配生产老师
![board_Gd1Owxj5AhLSDsb36dQcVEAhnJb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844357547.png)

配课 & 审核：

1. 教研生产老师：复制本课程id粘贴到web浏览器中拼成“url：地址3”，回车后可进行编辑；
1. 审核老师：在教研老师完成配课并提交后，直接在浏览器中输入“url：审核地址”，回车后可观看完整课程。
![board_G9tkwTt6ghdFVAbS96EcarUjncd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844358199.png)

上架：总部教研管理：

1. 直接找到审核完成的对应课程完成批量上架
1. 对于线上提交反馈，找到生产老师分享修改链接，完成修改后；总部教研统一发布更新
![board_TkdLwlV1WhQ0MHbwPUAcmtcKnBf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844358829.png)

1. 配课工具交互
产课教研老师，通过feishu外部分享的课程id，经过url地址拼接，得到“xx课程 配课工具地址”，可完成：0.UI模版修改 1.添加组件 2.选择题组内容 3.完成配课

3.1添加组件

![image_R64AbLuO5o5cmYxTbN6cHvmNnUe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844362654.png)

1.点击+选择要插入的位置

![image_YFqKbNbOwoniQ2xy7QvcYzO3nmc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844363098.png)

2.加组件

![image_Gw1Cb9dUaockHBx3mfBcbrOqnHd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844363561.png)

3.添加完成效果

![image_OZXbbOCK0o5DCwxNoj3c1vSWn4g](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844364013.png)

4.编辑位置

本期支持3类组件

![image_ShPVbaJplowMxnxix9UcCw3wnIe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844364446.png)

练习组件

![image_WhdkbTKtIoPRIExl84ycRpRrnVh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844364918.png)

视频组件

猜你想问（待核对）



3.2选择练习组件的题目

![image_ZVrObI1ZOoxBOXx6kChcEdRmnFd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844365626.png)

点击创建

![image_Gyj9bc4jOo5ktXxvARcco2IWnJg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844366553.png)

按id批量添加题目

![image_Bsq7bysxho5kkfxCoLxcIbJMnMf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844367050.png)

编辑题组信息

![image_Acrybr38uoFcHQxOVLbc707bnJg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844367511.png)

保存后数据更新

3.3上传视频组件的视频

![image_RnofbqTwhoQMt3xvHiScDNktnTd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844367912.png)

点击创建

![image_R4JKb7mhFowRRTxcpi2cqpt2ngc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844368471.png)

添加视频名称和本地文件

![image_LNsEbZSz6o2XQxxQ9OgcqEc1nHc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844368971.png)

完成视频上传，可提交保存

1. 随用户创建组件 & 更新题目/视频内容，实时修改预览地址中的内容
1. 点击「提交」后提交所有内容至服务器
![image_IMfEbs0TgogbDcxQOzYcAc3fnDh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844369441.png)

![image_J57gbNm2RoLlMkxi9vIcSVdEnHf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844369976.png)

![image_LSwxbTLDaorEcFxXreeckaqUnTd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844370562.png)

### 二、名词说明

![board_K9g3wSi4jhxxzybiTi4cPZ0XnFL](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844359323.png)

- 配题工具的实体
- 视频组件
- 用户使用该组件，可将本地视频插入到课程中的指定位置
- 组件内容：视频名称 + 视频文件上传
- 练习组件       
- 用户使用该组件，可输入题库中的题目id，并将题组插入到课程中的指定位置      
- 组件内容： 题目id 列表+ 预览编辑（换题、上/下移动、删除）       
- UI模版（本期无需展示前端修改）：
- 本期按学段-学科构建每个学科的唯一模版，后续可能迭代更换
- 模版内容：头图、插图、尾图的图片内容 + 20个预留part图标
-   课程实体
- 课程内容初始化（Part）源于稿件地址：
- 批量创建的情况下，为了避免老师导入“稿件地址”是人工误操作，需要忽略已被使用过的稿件地址
- 单个创建的情况下，不同课程可以关联之前使用过的稿件地址
- 一个课程id 可以同时对应 多棵：业务树知识点，比如：人教版-1.1.1集合的基本性质 和苏教版-2.1.1集合的基本概念
**已对齐，上架后修改当前id课程的预期**

一个课程id 在上架后，在用户报错情况下，会有以下两类修改课程的情况

1. 「纠错类」修改，目的是让新、老用户都能看到修改结果
1. 「升级类」修改，目的是让新用户能看到最新用户，并通知老用户有新课可以看
说明：

- 本期主要讨论「纠错类」下各类问题的修改工作流；
- 「纠错类」在当前课程id上进行修改：一般不影响学生端和老师端的 用户数据（进度、答题结果、学情报告等）；
- 「升级类」改动较大，一般会涉及课程id变更，本期先支持“已有用户学习数据的看得还是老课，新用户看到的是新课”的逻辑；后续再结合实际需求考虑是否给老用户查看新课id入口。
| case | 修改场景 | 修改元素 | 教研老师修改工具 | 学生端预期 | 教研预期 | 老师端预期 |
| --- | --- | --- | --- | --- | --- | --- |
| 0 | UI模版要替换 | UI图片、文字颜色 | 配课工具：模版工具 | 通过审核后c端直接更新 | 不能影响内容相关展示（板书、圈画、字幕） |  |
| 1 | xx题目有错，不需要重新批改 | 题目内容 | 题库直接修改这个id题目，经审核，一键发布至所有引用的课程 | 通过审核后c端直接更新 | 【修改方式解释】：纠错类-在原课修改，看过课和未看过课的学生均生效。升级类-复制出来一节新的课程，看过课的学生使用原课，未看过课的学生使用新课。【流程】：在题库修改，在课中刷新【提效操作】：通过题目id找到用到本题的所有课的id，一键刷新局部改错：修改题目内容/题目标签全局优化：修改题型 |  |
| 2 | xx题目有错，需要重新批改 | 题目内容、学生作答 | 题库修改 + 发起重新批改，完成后同步至老师端 & 学生端 | 修改通过审核后，内容直接更新，掌握度等需要计算的数据可以离线更新 |  |  |
| 3 | xx题目太难 | 题目id、题目数量减少 | 配课工具：模版工具 | 已经做过的学生内容不变，只对没有做过的学生更新展示 | 全局优化：更换题目 |  |
| 4 | MV视频有错 | 视频内容 | 配课工具：视频工具 | 通过审核后c端直接更新 | 局部改错：重新上传视频 |  |
| 5 | 人工圈画有错（画笔、插图） | 人工圈画视频 | 圈画工具 | 通过审核后c端内容直接更新，完成状态不变 | 局部改错：修改圈画/重新录制本part圈画。 |  |
| 6 | 板书有错字、系统圈画有错、图片有错 | 板书内容 | 逐字稿：板书工具+圈画工具提示重录 |  | 局部改错：修改板书内容/系统圈画/图片 |  |
| 7 | 字幕有错字 | 字幕 | 逐字稿：字幕工具 |  | 局部改错：修改字幕 |  |
| 8 | 朗读稿有错字、音频有错 | 音频 | 逐字稿：朗读稿工具 |  | 局部改错：修改音频 |  |
| 9 | Part内容修改、删减 | 调整标题、顺序、内容删减 | 逐字稿：part编辑 |  |  |  |

- 预览/新布置的课程使用最新
- 有学情数据的展示当时布置的数据
### 三、业务流程

**课程管理工具-核心功能**

![board_FsTCwC7kyhSoTbbDfeGcBW9hn2c](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844360004.png)

**配题工具-核心功能**

![board_Ie8UwUMqnhPXjebNnjfcv8nynVg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844360639.png)

### 四、需求概览

| 序号 | 需求名称 | 内容概述 | 优先级 |
| --- | --- | --- | --- |
| 1 | 课程管理工具 | 展示课程列表和筛选 | P0 |
| 2 |  | 课程的批量创建、单个创建 | P0 |
| 3 |  | 查看详情：修改课程地址和状态查看 | P0 |
| 4 |  | 课程上架/更新 | P0 |
| 4 | 配课工具 | 练习组件的添加、编辑、删除、选题 | P0 |
| 5 |  | 视频组件的添加、编辑、删除 | P0 |
| 6 |  | 组件位置修改 | P0 |
| 7 |  | 在新页面预览整课 | P0 |
| 8 |  | 完成配课 | P0 |
| 9 |  | UI模版配置 | P1 |

### 五、详细产品方案

#### 5.1课程管理工具

| 序号 | 模块 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- | --- |
| 0 | 手机号验证码登录 | 总部教研身份校验 |  |  | P0 |
| 1 | 课程管理 | 展示课程筛选 | 顶部：展示页面名称“AI课-课程管理”，登录人姓名（可点头像后退登）主体：展示课程筛选和列表信息 | ![in_table_image_FRIIbG28aoBuw7xfHchcf1Vfn5U](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844371313.png) | P0 |
| 2 |  | 展示课程列表 | 列表展示列表交互操作： | 同上 | P0 |
| 3 | 课程创建 | 课程的批量创建 | ![in_table_image_LZQqb0Z2UoR8jMxIxgUcZ3H2nDe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844372287.png) | ![in_table_image_MxBUbu7goohaldx8H7lcNdewnpe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844372740.png) | P0 |
| 4 |  | 课程的单个创建 | 创建课程 | ![in_table_image_SVHDbVnyPo27fHxll7xc6lBXnfc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844373155.png) | P0 |
| 5 | 课程编辑 | 课程的编辑 | 编辑课程 | ![in_table_image_HxsfbOoE3orTIox9KQOcgf66nwb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844374090.png) | P0 |
| 6 | 课程修改地址 | 查看详情 | 线上信息：修改地址： | ![in_table_image_JUC8bSYd8oK8fdxW5S2cxxTVncb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844375349.png) | P0 |
| 7 | 课程上架 | 课程上架/批量上架 | ![in_table_board_WOzawEpPChP6PebS9LFcsdOSnIe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844361127.png) | ![in_table_image_EhqBb5FZBorffexVFy5cAmMInRg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844375904.png) | P0 |

- 老师需要在内网环境下，输入手机号 & 验证码进行登录
- 手机号：仅后台配置的指定手机号可以访问课程管理页
![analyze_in_table_image_FRIIbG28aoBuw7xfHchcf1Vfn5U](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844371313.png)

- 课程id、课程名称、学段、学科、课程类型、业务树知识点（可能多行）、课程创建人、创建时间、上架态、上架时间
- 操作：编辑课程、上架课程、查看详情
- 按创建时间（正序/倒序）排序
- 按上架时间（正序/倒序）排序
- 下载列表（当前筛选条件下，全部列内容）
- 编辑课程、上架课程、查看详情
1. 下载模版，模版表头如下：
1. 批量上传课程：老师把课程名称和链接粘贴到模版中上传：
![analyze_in_table_image_CX9Lb5SFAoDhurxWFgxcgvc2n1b](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844371829.png)

1. 解析完毕后展示：成功数据数量：共xx条数据，成功xx条，失败xx条
失败条数不为0的情况下，+展示下载失败原因：点击后下载文件，内容如下：在用户上传表格数据基础上，加一列失败原因（仅展示失败数据）

![analyze_in_table_image_LZQqb0Z2UoR8jMxIxgUcZ3H2nDe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844372287.png)

1. 点击“创建”按当前结果批量创建课程id，否则不创建
![analyze_in_table_image_MxBUbu7goohaldx8H7lcNdewnpe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844372740.png)

- 必填：名称*（25字符以内）
- 边界case：点击“提交”时，check：是否存在：名称 & 地址同时存在的情况，若已有，弹出提示“该名称&地址已经创建过，确认创建新课程吗？确认，创建新id；取消，不创建”
- 必填：地址*（不做重复校验，作不存在提示--不可保存）
- 学段、学科、单课类型、数字人id：读取稿件url带过来的参数，不可修改；在未输入稿件地址 或 地址不存在时默认显示“输入稿件地址后自动填充”
- 非必填：业务树末级知识点：
- 根据名称作为关键词搜索后台业务树展示，若存在多个展示下拉菜单
- 若没有显示未找到关键字为“xxx”的业务树知识点，用户可清空后重新输入关键字再回车搜索
- 在不同业务树末级知识点名称可能不同，要支持用户搜索不同关键词后逐一添加
- 在课程上架时，该选项必填至少1个，否则阻塞上架
![analyze_in_table_image_SVHDbVnyPo27fHxll7xc6lBXnfc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844373155.png)

- 可改：名称*（25字符以内）
- 可改：地址*（不做重复校验，作不存在提示--不可保存）
- 学段、学科、单课类型、数字人id：读取稿件url带过来的参数，不可手动直接修改；在未输入稿件地址 或 地址不存在时默认显示“输入稿件地址后自动填充”
- 非必填：业务树末级知识点
- 根据名称作为关键词搜索后台业务树展示，若存在一个直接添加成功，若存在多个：展示下拉菜单、用户可点选多个，若没有显示未找到关键字为“xxx”的业务树知识点
- 搜索范围：仅业务树末级知识点
- 文案展示：业务树名称-末级知识点名称，例如：人教版-集合的基本性质
- 在课程上架时，该选项必填至少1个，否则阻塞上架
![analyze_in_table_image_HxsfbOoE3orTIox9KQOcgf66nwb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844374090.png)

![analyze_in_table_image_JUC8bSYd8oK8fdxW5S2cxxTVncb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844375349.png)

- 点击上架/更新/批量上架后：
- 展示二次弹窗，待老师确认“是否确认上架？上架后，学生端和老师端都将同步可见本课”确认：上架；否则：取消。
- 判定是否符合上架条件：比如：“试题为空”/“视频为空”
- 课程上架后：操作后的按钮改为“更新”，点击后操作同之前
![analyze_in_table_board_WOzawEpPChP6PebS9LFcsdOSnIe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844361127.png)

- 批量上架操作：
- 在第一列的勾选框，对当前筛选条件下的课程可进行勾选，完成后点击“批量上架/更新”
- 点击“第一列”表头的勾选或取消勾选，代表：批量选择 或 批量取消
![analyze_in_table_image_EhqBb5FZBorffexVFy5cAmMInRg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844375904.png)

#### 5.2配课工具

| 序号 | 模块 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- | --- |
| 1 | part展示 | 配课页面初始态内容 | 数据来源：课程创建完成后，可得到配课页id和url | ![in_table_image_QEX4bMpShoqXydxds5qc6juXnFY](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844376849.png) | P0 |
| 2 | 练习组件 | 练习组件的添加、删除 | 下面的修改都直接提交至服务端，即改即生效，可预览变化：「添加练习组件」的操作说明：「删除练习组件」的操作说明： | ![in_table_image_D7fSbuIQloty4gxYBvocJ2sMnhb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844377361.png) | P0 |
| 3 | 练习组件 | 练习组件的编辑题目 | 「编辑题目」的操作说明： | ![in_table_image_GR1oboxyIo57HGx214Dc8sw8nKd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844381347.png) |  |
| 4 | 视频组件 | 视频组件的添加、编辑、删除 | 下面的修改都直接提交至服务端，即改即生效，可预览变化：「添加视频组件」的操作说明：「删除视频组件」的操作说明：「编辑组件」的操作说明： | ![in_table_image_B2R5b45NIoPJMHxXq19cdWMSnhe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844382292.png) | P0 |
| 5 | 视频组件 | 视频组件的编辑视频 | 「编辑视频」的操作说明： | ![in_table_image_Vd42bNaBSoMSsfxdjuacu9p5n9f](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844383267.png)

完成视频上传，可提交保存 |  |
| 6 | 题目/视频组件位置 | 组件位置修改 | 下面的修改都直接提交至服务端，即改即生效，可预览变化： | ![in_table_image_V56AbLzT2otrizxhIr8cHC5Ingg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844383737.png) | P0 |
| 7 | 预览 | 在新页面预览整课 | 页面内容： | ![in_table_image_Dg1kbhzSVo1eBHxmDUwc3H8jnUf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844385618.png) | P0 |
| 8 | 提交完成 | 完成配课 | 点击“提交发布”，即完成配课 | ![in_table_image_DOQzbsu5Poev1hxBQwvcxAXCnQc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844386891.png) | P0 |
| 9 | UI模版 | 修改UI模版 | 当前所有AI课都随 学段-学科 存储了一套UI模版这套模版保存在服务端包含：若后续有可供修改的模版，这里可以在下拉菜单中点击选择：该学科下的其他模版，并提交后变更生成新样式。 | ![in_table_image_EijwbkyVloUUp3x0QrKcX2b0nfe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844388519.png) | P1 |

1. 顶部：展示课程名称、预览本课、提交发布
1. 中部：按顺序展示各part名称，并将各part作为卡片，下部展示+号，支持插入组件
1. 底部：可通过+/- 展示当前页面卡片显示的百分比
![analyze_in_table_image_P4lmbRwMnokbWTxyEUTcn6U7nTe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844376386.png)

![analyze_in_table_image_QEX4bMpShoqXydxds5qc6juXnFY](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844376849.png)

1. 选择插入组件的位置。
1. 在弹窗中：选择插入组件的类型为“练习组件”：获取前置part的名称并填入（可编辑）；点“创建组件”，完成添加（当前无题目，标记为“未完成状态”）。
1. 提交后，打开“添加题目”窗口，将“最近更新时间”更新到卡片上
1. 在卡片点击"...",点击“删除组件”。
1. 在二次弹窗中：是否确认删掉整个题组，删除后数据将无法回复；点“确定”，完成删除。
![analyze_in_table_image_D7fSbuIQloty4gxYBvocJ2sMnhb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844377361.png)

![image_QFzgbXg0boxvQBxaOOGcsstsnKg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844377807.png)

无题目

![image_PHzybzRqnokscDxQahqca2yfnOf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844378278.png)

有题目

1. 在卡片点击"...",点击“编辑题目”。
1. 若当前题目数为0，展示弹窗“添加题目”
1. 插入位置：题目1/...题目N（下拉菜单）
1. 当目前题目数为0时，仅显示题目1
1. 党目前题目为N时，按分组结构显示题目1、题目2-1、题目2-2～题目N，都可以被选
1. 输入框中：
1. 默认内容为空，提示文案：请按顺序输入多个题目id，用“、”分隔符隔开，【】方括号分组
1. 注：中文全角方括号
1. P1:自动添加题目版本：默认可自动根据前一个part内容添加题目
1. 点击提交：即刻服务端从后台批量获取题目id，按顺序返回当前页面，loading完成后，展示抽屉页“题组x的题目列表”
1. 若当前题目数不为0，直接展示抽屉页“练习x的题目列表”
1. 顶部：展示当前组件序号作为标题，比如：练习1
1. 添加题目：
1. 展示弹窗“添加题目”
1. 下拉菜单默认选择最后一题，用户可切换；输入框默认为空，完成后点：提交
1. 新添加的题目可按顺序补充到所选位置，并刷新展示抽屉页“题组x的题目列表”
1. 编辑全部题目：
1. 展示弹窗“编辑全部题目”
1. 输入框：默认包含当前全部题目id，及其对应的分组，用圆括号分隔开，用户修改完成后点：提交
1. 重新解析用户输入的题组内容（id、顺序、圆括号的位置、题号等）
1. 列表中：按顺序展示用户可以看到的题目
1. 题号：解析完成用户输入的内容：
1. （）括起来的部分作为同一个组展示，比如按：题目2-1、题目2-2展示题号
1. 其他按题目1、题目2、题目3...顺序展示
1. 题干、选项
1. 题目标签（题型、难易、来源）
1. 答案解析（默认收起，可点击展开）
1. 点击单题：可对题目进行编辑：
1. 上移/下移，点击后即刻移动本题位置
1. 若前后换位置的是单题 vs 整个题组，则互换位置时是整个题组互换
1. 若前后换位置的是同一题组，则互换位置是在当前题组内
1. 删除，点击后，展示二次弹窗：“是否确定删除题目？确定/取消”，点击“确定”即刻删除题目
1. 换题，点击后展示弹窗“替换题目”：
1. 输入框中：用户只可输入一个题目id替换当前题目，点击提交
1. 提交后即替换题目id，按指定位置返回当前页面，刷新展示抽屉页“题组x的题目列表”
1. 底部：可编辑全部题目、或清空
1. 清空：在二次弹窗中：是否确认清空整个题组，清空后数据将无法回复；点“确定”，完成清空。
1. 保存：
1. 当前页面的所有编辑都实时在服务端保存
1. 题目数计算口径：
1. 若没有相似题组，则题目数=题目总数；
1. 若有相似题组，同一个题组里的算1题，题目数=单题+题组总数。
![analyze_in_table_image_KacLbqQo0oQLUqxVH7ucdKeknNg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844378748.png)

![analyze_in_table_image_K7VhbbAkHoggDsxb8UtcXleQnab](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844380439.png)

![analyze_in_table_image_XXLhbr1WFoFoVSxbZi3cH1zFnuc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844380898.png)

![analyze_in_table_image_GR1oboxyIo57HGx214Dc8sw8nKd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844381347.png)

1. 选择插入组件的位置。
1. 在弹窗中：选择插入组件的类型为“视频组件”；点“创建组件”，完成添加（当前无视频，标记为“未完成状态”）。
1. 提交后，打开“添加视频”窗口，将“最近更新时间”更新到卡片上
1. 在卡片点击"...",点击“删除组件”。
1. 在二次弹窗中：是否确认删掉整个题组，删除后数据将无法回复；点“确定”，完成删除。
1. 在卡片点击"...",点击“编辑组件”。
1. 在弹窗中：获取题目组件名称（可编辑）；点“保存”，完成编辑。
1. 提交后，将“最近更新时间”&“视频时长”、“名称”更新到卡片上
![analyze_in_table_image_Y5Nqbgzwto2fWvxOQJac15nZnfb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844381781.png)

![analyze_in_table_image_B2R5b45NIoPJMHxXq19cdWMSnhe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844382292.png)

1. 在卡片点击"...",点击“编辑视频”
1. 获取前置part的名称并填入（可编辑）；
1. 选择本地视频文件后：
1. 未达100%时，显示上传进度条
1. 达到100%后，显示视频首祯供用户预览，并显示名称和时长
1. 此时用户可以点“x”删除视频，返回上一步重新上传
1. 提交后，将“最近更新时间”&“视频时长”、“名称”更新到卡片上
![analyze_in_table_image_IBOMb4s0Io6dyAxyUZHc4UVcnFb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844382777.png)

![analyze_in_table_image_Vd42bNaBSoMSsfxdjuacu9p5n9f](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844383267.png)

1. 点击“题组组件/视频组件”的"..."，可点击“更换位置”
1. 组件中间变为虚线，可以拖动到其他节点，松手后即完成位置修改
![analyze_in_table_image_V56AbLzT2otrizxhIr8cHC5Ingg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844383737.png)

1. 对于生产同学：点击“预览本课”，新打开一页展示“课程预览”
1. 对于审课同学：在固定url访问网址，即可查看最新的整课预览页面
1. 展示和学生端pad展示一样的内容：包含：进度栏、板书、圈画痕迹等
1. 在页面标题栏目，展示对应「业务树末级知识点名称」，此处若对应了多了不同的知识点，则展示下拉菜单，支持用户切换
![analyze_in_table_image_X7YHbHVBEoUF51xx7grcA5qqnX1](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844384162.png)

![analyze_in_table_image_OHqRb5ewIoqmQnxOXB1cGKBHnQc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844384931.png)

![analyze_in_table_image_Dg1kbhzSVo1eBHxmDUwc3H8jnUf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844385618.png)

![analyze_in_table_image_DOQzbsu5Poev1hxBQwvcxAXCnQc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844386891.png)

- 3个插图（头图--在part1之前、尾图--在最后一个part最后、插图--在最后一个part前面）
- 各part按顺序展示的icon
- 还可能包含：一些字体颜色的设置
![analyze_in_table_image_J3JGbdcs0o8B9qxBQygcyVQOncf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844388012.png)

![analyze_in_table_image_EijwbkyVloUUp3x0QrKcX2b0nfe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844388519.png)

### 六、数据需求

#### 6.1 效果追踪

描述计划监控数据效果的方式及所需数据字段

#### 6.2 数据埋点

| 页面 | 模块 | 动作 | 埋点名称 | 图示（如需） |
| --- | --- | --- | --- | --- |
|  |  |  |  |  |
|  |  |  |  |  |
|  |  |  |  |  |
|  |  |  |  |  |

### 七、a/b实验需求

若有，则写清楚实验方案

**实验目的**

XXX

**实验对象**

限制条件：端/版本/用户

实验组策略：

对照组策略：

**实验及分流**

启动时间、启动时用户量、预计持续时间

**评价指标**

结果指标（标明预期提升值）、过程指标

**暂无**



### 附：评审记录

记录存档历次需求评审中的会议纪要，方便追踪

举例：

**20250204 - 初评纪要**

1. 会后尽快确定巩固类型本期题目类型范围@杨宇航
已确认，并补充进文档

1. 数据下发方式待服务端和客户端最终确认@服务端rd @客户端rd
确定服务端下发，客户端仅做兜底逻辑

1. ...


**20250321 - 二次评审纪要**

1. XX
1. 
