# PRD - 巩固练习相关策略 - V1.0# 一、背景和目标

## 需求背景

在 AI 课程的学习流程中，“巩固练习”是承接课堂知识理解与能力内化的关键环节。学生是否能够在练习阶段接触到**适合其能力水平**的题目，直接决定了知识点掌握的效率和质量。
当前阶段，我们正处于系统冷启动初期，题目缺乏足够的答题数据，难以直接使用模型驱动的推荐算法，因此需要通过一定的“人工可控的策略”，尽早建立“能力匹配、难度适配”的练习体验，并为后续数据积累和算法推荐打下基础。

## 项目收益

1. **为不同能力水平的学生提供匹配的练习题**
   避免“题太难打击信心”或“题太简单浪费时间”，提升学习效率和满意度。
2. **让学生更有效地掌握课程重点知识点**
   通过对典型题的优先覆盖与逐步难度推进，提升知识掌握度和迁移能力。
3. **为后续算法推荐提供结构化数据积累**
   每一次基于策略推送的练习和作答，都是训练模型参数的有效样本，便于未来迭代上线 IRT、知识追踪等自动推荐机制。
4. **提升整体产品智能感与完成率**
   通过系统“主动推题、智能响应”，增强学生对产品智能化的感知。

## 覆盖用户

使用新 AI 课学习的全部用户

# 二、内容数据结构

## 内容结构

![BapIwZwaChzSmMbOmancRa01nWg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_BapIwZwaChzSmMbOmancRa01nWg.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】

此图片展示了一个“业务树知识点”下“巩固练习”模块的层级结构。

**关键元素层级结构与关联:**

1.  **业务树知识点**: 顶层概念，是学习内容的起点。
    *   **巩固练习**: “业务树知识点”下的一个主要环节，用于知识点的巩固。
        *   **题组1**: “巩固练习”的组成部分，包含一系列题目。
            *   题目1 (必做题 - 粉红色背景)
            *   题目2 (必做题 - 粉红色背景)
            *   题目3 (非必做题 - 黄色背景)
            *   题目4 (非必做题 - 黄色背景)
            *   ... (省略号，代表更多题目)
            *   题目N (非必做题 - 黄色背景)
        *   **题组2**: “巩固练习”的另一组成部分。
            *   题目1 (必做题 - 粉红色背景)
            *   题目2 (非必做题 - 黄色背景)
            *   题目3 (非必做题 - 黄色背景)
            *   ... (省略号，代表更多题目)
            *   题目N (非必做题 - 黄色背景)
        *   **题组N**: “巩固练习”的第N个组成部分。
            *   题目1 (非必做题 - 黄色背景)
            *   题目2 (非必做题 - 黄色背景)
            *   题目3 (非必做题 - 黄色背景)
            *   ... (省略号，代表更多题目)
            *   题目N (非必做题 - 黄色背景)

**图例说明:**
*   **必做题**: 以粉红色背景标示。
*   **非必做题**: 以黄色背景标示。

**Mermaid 流程图描述:**

```mermaid
graph TD
    A[业务树知识点] --> B[巩固练习]

    B --> G1[题组1]
    subgraph sg1[ ]
        direction LR
        Q1_1["题目1 (必做题)"]
        Q1_2["题目2 (必做题)"]
        Q1_3["题目3 (非必做题)"]
        Q1_4["题目4 (非必做题)"]
        Q1_etc["..."]
        Q1_N["题目N (非必做题)"]
    end
    G1 --> Q1_1

    B --> G2[题组2]
    subgraph sg2[ ]
        direction LR
        Q2_1["题目1 (必做题)"]
        Q2_2["题目2 (非必做题)"]
        Q2_3["题目3 (非必做题)"]
        Q2_etc["..."]
        Q2_N["题目N (非必做题)"]
    end
    G2 --> Q2_1

    B --> GN[题组N]
    subgraph sgn[ ]
        direction LR
        QN_1["题目1 (非必做题)"]
        QN_2["题目2 (非必做题)"]
        QN_3["题目3 (非必做题)"]
        QN_etc["..."]
        QN_N["题目N (非必做题)"]
    end
    GN --> QN_1

    subgraph Legend [图例]
        L1["必做题"]
        L2["非必做题"]
    end

    classDef mustDo fill:#FFDDEE,stroke:#D07070,color:black;
    classDef optionalDo fill:#FFFFDD,stroke:#D0D070,color:black;
    classDef defaultNode fill:#f9f9f9,stroke:#333,stroke-width:1px;
    classDef groupNode fill:#f0f8ff,stroke:#333,stroke-width:1px;
    classDef rootNode fill:#e6ffe6,stroke:#333,stroke-width:1px;

    class A rootNode;
    class B groupNode;
    class G1,G2,GN groupNode;

    class Q1_1,Q1_2,Q2_1,L1 mustDo;
    class Q1_3,Q1_4,Q1_N,Q2_2,Q2_3,Q2_N,QN_1,QN_2,QN_3,QN_N,L2 optionalDo;
    class Q1_etc,Q2_etc,QN_etc defaultNode;

    style sg1 stroke-dasharray:5 5,stroke-width:1px,stroke:#666,fill:none
    style sg2 stroke-dasharray:5 5,stroke-width:1px,stroke:#666,fill:none
    style sgn stroke-dasharray:5 5,stroke-width:1px,stroke:#666,fill:none
    style Legend fill:#f8f8f8,stroke:#bbb
```

【============== 图片解析 END ==============】



- 每节课的巩固练习由 1-5 个题组构成，题组对应为和这节课知识点相关的典型题型。
- 每个题组初期配置 10 道题左右
- 题组中的典型必做题，老师在选题时会打上推荐标签。每个题组中可以有“0-多”个必做题
- 每个题目都有知识点内的难度等级 L1 - L5，一个题组内的难度分布会尽量符合正态分布

## 题目难度系数

根据题目的难度等级，每道题目有对应的难度系数。

> 冷启动阶段的难度系数为人工设定，当有用户作答数据后，会重新离线计算更准确的难度系数。

<table>
<tr>
<td>难度等级<br/></td><td>L1<br/></td><td>L2<br/></td><td>L3<br/></td><td>L4<br/></td><td>L5<br/></td></tr>
<tr>
<td>题目难度系数<br/></td><td>0.2<br/></td><td>0.4<br/></td><td>0.6<br/></td><td>0.8<br/><br/></td><td>1.0<br/><br/></td></tr>
</table>

# 三、策略方案

## 题目推荐策略

### 前置计算

#### 预估题目数量

- **整体题量**

1. 根据这个知识点的难度和考试频率，计算基础答题量

> 基础题量和知识点难度、考试频率相关。考频高的难点应该多练习，而相对简单且低频的知识点，练习量可以减少。
> 基础答题量=10 *难度系数*考频系数   四舍五入取整   [8 , 14]

1. 根据用户当前掌握度，计算动态调整系数 A。默认值为 1。

$$
\text{A} = 1 + \alpha\times\frac{\max(0, \theta_{\text{tar}} - \theta_{\text{cur}})}{\theta_{\text{tar}}}$$- θ_tar为该知识点对应的目标掌握度
- θ_cur为学生对该知识点的当前掌握度
- α 是“经验调节系数”，用来控制差距对题量的影响强度，默认取0.6

1. 预估题目数量 = 基础答题量 * 动态调整系数 A
2. 全局上下限- 下限：题组数量 * 2（保证每个题组至少有2道题）
> 如：预估整体题量为8，共5个题组。则最终的预估整体题量为10
- 上限：15 （避免单次练习量过多）


示例：当前知识点难度系数 0.9，考频系数 1.2 ，学生当前掌握度 θ_cur=0.3，目标掌握度θ_tar=0.8。
1. 计算基础答题量  0.9 * 1.2 * 10 = 10.8 ≈ 11
2. 计算动态调整系数 A = 1 + 0.6 * [(0.8−0.3)/0.8] ≈ 1.375
3. 计算预估题目数量 11 * 1.375 ≈ 15

- **每个题组的题量**
> 巩固练习的定位是需要让学生把典型题都练到，因此题组在设置的时候也应该是典型的，每个题组都同等重要。
把预估的整体题量，平均到每个题组，如果不能整除，则有部分题组会多1题。
如：共10题，3个题组，则每个题组的预估题量为 3,3,4

- **计算时机**
1. 业务树完成难度系数和考频系数配置后，离线计算基础答题量
2. 离线更新用户初始掌握度&目标掌握度后，离线计算动态调整系数
3. 用户完成AI课后，更新动态调整系数。

#### 预期掌握度增量
预估每道题对当前用户带来的掌握度变化量作为推荐依据，掌握度增量计算方式详见：[PRD - 掌握度 - V1.0](https://wcng60ba718p.feishu.cn/wiki/J3d0wrEaDiWRy6kj2uVcQKLfnmh)
1. $$P(\text{答对}) = \text{sigmoid}(4 \times (\theta - \beta))
$$

> θ 为学生的掌握度，β 为题目难度系数
> Sigmoid 函数的数学表达式为$S(x)=\frac{1}{1 + e^{-x}}$

1. $$
   \text{预期掌握度增量} = P(\text{答对})\times\text{答对掌握度增量}+P(\text{答错})\times\text{答错掌握度增量}
   $$

- **示例**
  用户当前掌握度 0.3，题目难度系数 0.6（L3）- P(答对）≈ sigmoid(4 × (0.3 − 0.6)) ≈ 0.2315
- P(答错）≈  1 - 0.2315 = 0.7685
- 答对掌握度增量 = 0.2 * （0.6-0.3）= 0.06
- 答错掌握度增量 = -0.1 * （0.6-0.3）* 0.5 = -0.015
- 预期掌握度增量 = 0.2315 * 0.06 + 0.7685 * -0.015 ≈ 0.002
- **计算时机**
  用户每次答题后计算

### 巩固练习题目推荐

#### 整体流程

![M9QVwZngmh5UrqbB2lHcy5aZnpf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_M9QVwZngmh5UrqbB2lHcy5aZnpf.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】

此图片为“巩固练习推题”的业务流程图。其核心逻辑以层级化结构阐述如下：

*   **顶层流程：练习整体控制**
    *   **起始点**：“1. 巩固练习推题”。
    *   **核心判断 (是否有未作答题组?)**：“3. 本次练习是否有未作答的题组”。
        *   如果“否”，流程结束，进入“2. 结束”。
        *   如果“是”，进入下一层处理，执行“6. 按顺序进入题组”。

*   **第二层：题组内首题判断与初步选题**
    *   进入题组后，判断：“7. 当前题目是否为题组内首题”。
        *   如果“是”（首题）：
            *   进一步判断：“8. 题组内是否有必做题”。
                *   如果“是”（有必做题），则执行动作：“9. 在必做题中选择「预期掌握度增量」最大的题目”。
                *   如果“否”（无必做题），则执行动作：“10. 在题组中选择「预期掌握度增量」最大的题目”。
        *   如果“否”（非首题），则直接执行动作：“10. 在题组中选择「预期掌握度增量」最大的题目”。
    *   完成上述选题动作后，进入第三层判断。

*   **第三层：题组内继续推题或结束当前题组的判断逻辑**
    *   **判断1 (是否达到预估量?)**：“11. 当前题组是否已达预估答题量”。
        *   如果“是”，流程返回顶层核心判断“3. 本次练习是否有未作答的题组”，以处理下一个题组或结束练习。
        *   如果“否”，进入下一判断。
    *   **判断2 (剩余题目价值?)**：“12. 剩余可推荐题目的预期掌握度增量是否≥0.01”。
        *   如果“是”，流程返回第二层，执行“10. 在题组中选择「预期掌握度增量」最大的题目”，继续在本题组内选题。
        *   如果“否”，进入下一判断。
    *   **判断3 (是否已答2题?)**：“13. 本题组是否已作答2题”。
        *   如果“是”，流程返回顶层核心判断“3. 本次练习是否有未作答的题组”，以处理下一个题组或结束练习。
        *   如果“否”，流程返回第二层，执行“10. 在题组中选择「预期掌握度增量」最大的题目”，继续在本题组内选题。

以下是该流程的 Mermaid 图表表示：

```mermaid
flowchart TD
    S["1. 巩固练习推题"] --> D1{"3. 本次练习是否有未作答的题组"}

    D1 -- "否" --> E["2. 结束"]
    D1 -- "是" --> P1["6. 按顺序进入题组"]

    P1 --> D2{"7. 当前题目是否为题组内首题"}

    D2 -- "是" --> D3{"8. 题组内是否有必做题"}
    D2 -- "否" --> A2["10. 在题组中选择「预期掌握度增量」最大的题目"]

    D3 -- "是" --> A1["9. 在必做题中选择「预期掌握度增量」最大的题目"]
    D3 -- "否" --> A2

    A1 --> D4{"11. 当前题组是否已达预估答题量"}
    A2 --> D4

    D4 -- "是" --> D1
    D4 -- "否" --> D5{"12. 剩余可推荐题目的预期掌握度增量是否≥0.01"}

    D5 -- "是" --> A2
    D5 -- "否" --> D6{"13. 本题组是否已作答2题"}

    D6 -- "是" --> D1
    D6 -- "否" --> A2
```
【============== 图片解析 END ==============】



#### 题目推荐策略

1. 巩固练习中的多个题组，需要依次作答。
2. 每次推题默认在当前题组中选择「预期掌握度增量」最大的题目
3. 如果有必做题，优先推荐必做题。
4. 如果有多道题的「预期掌握度增量」相等，随机选择一道。
5. 最大答题数量（不包括错题）不超过预估答题数，最小答题数量为题组数量*2
   **限制条件**：
6. 连续两题之间难度最多提升一个等级（防止难度剧烈跳变）
7. 上题答错，不可提升难度等级
8. 上题答对，不可降低难度等级
   注：当推荐的题目难度上升/下降时，C 端展示对应动效
   **特殊情况：**
9. 当前题组作答未满 2 题已没有用户可推荐的题目，则直接进入下一题组。
10. 当推荐的题目中有大题（阅读理解、完形填空等一题包含多个小题）时，需要计算推出大题是否会超出预计答题数量（大题中的每个小题占用一个答题数量）。1. 优先在可推题目中推出题目数量不超过预估答题数的题目
11. 如果没有不超过预估答题数的题目，可推出超过预估答题数 5 题以内的大题
12. 否则直接进入下一题组或提前结束练习

#### 熔断策略

熔断策略优先级高于题目推荐优先级，当触发熔断时，直接结束巩固练习。会触发熔断的场景如下：

1. **用户连续做错 5 题 且 题池中没有难度更低的题目**：结束巩固练习
2. **用户专注度分值等于 3**：在用户提交当前题目后，结束巩固练习。

## 再练一次

### 报告页引导

用户完成巩固练习后，系统根据其对当前知识点的掌握情况和本次练习表现，判断是否需要引导用户再练一次
**判断条件：**

1. 用户当前掌握度低于目标掌握度的 70%
2. 巩固练习正确率低于 70%
   当同时满足上述 2 个条件时，报告页面需要引导用户再练一次。

### 判断是否满足再练一次

用户完成巩固练习后，需要根据题库中的剩余可练习题目数量来判断，是否满足再练一次。如果满足则展示入口，如果不满足则不展示入口（详见 C 端需求）
**判断步骤：**

1. 确定可练习的题组：只有在“巩固练习”环节中出现过错题的题组，才会进入再练一次环节。
2. 计算单个题组的可用题目：找出该题组内学生在“巩固练习”中未作答过，并且其“预期掌握度增量”不小于 0.01 的题目。将这些筛选出来的题目数量记为“可用题目数”。
3. 判断是否满足再练一次：所有可练习题组中的可用题目数量 ≥3 题时，满足再练一次条件

### 计算预估题目数量

1. 单个题组的预估题量 = Min(“可用题目数”, 该题组在巩固练习中的“预估答题数”)
2. 所有可练习的题组计算出的“预估题量”进行累加，得到整体的“预估题目数量”

### 再练一次推题策略

和正常的巩固练习推题策略相比

1. 再练一次增加了错题重练：每个题组的首题为巩固练习中「预期掌握度增量最大」的错题原题
2. 没有每个题组最少作答 2 题的限制
   ![QsgBw2hLlhAiL4bEX6acCDEmnTc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_QsgBw2hLlhAiL4bEX6acCDEmnTc.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】

此图片为“再练一次推题”功能的业务流程图，旨在为用户提供针对性的巩固练习。

**关键元素、组成部分及关联（层级化结构）：**

1.  **流程起点：**
    *   `再练一次推题`：启动推题流程。

2.  **准备阶段：题组筛选**
    *   `筛选巩固练习环节有错题的题组`：系统首先筛选出在先前“巩固练习”环节中包含用户错题的题组，作为本次“再练一次”的候选范围。

3.  **主循环：遍历题组**
    *   `本次练习是否有未作答的题组`（判断节点）：
        *   **是**：如果存在未作答的题组，则执行 `按顺序进入题组`，开始处理当前题组。
        *   **否**：如果不存在未作答的题组，则流程 `结束`。

4.  **题组内推题逻辑：**
    *   进入题组后，首先 `在错题中选择「预期掌握度增量」最大的题目`：优先从当前题组的错题中选择对用户掌握度提升最明显的题目。
    *   `当前题组是否已达预估答题量`（判断节点）：判断当前题组已推荐的题目数量是否达到预设的上限。
        *   **是**（已达预估量）：执行 `在题组中选择「预期掌握度增量」最大的题目`（此时可能是在所有剩余题目中选择，或作为一种优化选择），然后**返回**继续判断 `当前题组是否已达预估答题量`。这形成一个循环，确保在达到预估量时，依然推送最优题目（或完成当前题目后结束本题组）。
        *   **否**（未达预估量）：进入下一步判断。
    *   `剩余可推荐题目的预期掌握度增量是否≥0.01`（判断节点）：判断当前题组中，是否存在预期掌握度增量大于等于0.01的题目。
        *   **是**（存在符合条件的题目）：执行 `在题组中选择「预期掌握度增量」最大的题目`，然后**返回**判断 `当前题组是否已达预估答题量`。这确保了只要有价值的题目，就会继续推送，直到达到预估量。
        *   **否**（不存在符合条件的题目或无剩余题目）：**返回**至 `本次练习是否有未作答的题组` 的判断节点，尝试处理下一个题组或结束练习。

**核心逻辑概述：**
该流程通过两层循环实现。外层循环遍历符合条件的题组。内层循环在每个题组内，根据“预期掌握度增量”和“预估答题量”两个核心指标，智能推荐题目，优先推荐错题和掌握度增量高的题目，直至当前题组达到练习量或无合适题目可选，再进入下一个题组。

```mermaid
flowchart TD
    A["再练一次推题"] --> B["筛选巩固练习环节有错题的题组"];
    B --> C{"本次练习是否有未作答的<br>题组"};
    C --"否"--> D["结束"];
    C --"是"--> E["按顺序进入题组"];
    E --> F["在错题中选择「预期掌握度增量」<br>最大的题目"];
    F --> G{"当前题组是否已达预估答<br>题量"};
    G --"是"--> I["在题组中选择「预期掌握度增量」<br>最大的题目"];
    I --> G;
    G --"否"--> H{"剩余可推荐题目的预期掌<br>握度增量是否≥0.01"};
    H --"是"--> I;
    H --"否"--> C;
```

【============== 图片解析 END ==============】



## 不认真答题判断

> 用户答题专注度与作答时间、用户行为等多种特征相关。V1.0 阶段可构建极简版专注度评分系统，仅考虑作答时长及正确率。
> **判断方式：**<table>

<tr>
<td>类型<br/></td><td>事件<br/></td></tr>
<tr>
<td rowspan="2">作答时长<br/></td><td>进入题目1s内作答，且回答错误<br/></td></tr>
<tr>
<td>作答用时小于平均时长1/3，且回答错误（一期不支持）<br/></td></tr>
<tr>
<td rowspan="2">正确率<br/><br/></td><td>连续做错3题<br/></td></tr>
<tr>
<td>做错难度系数低于当前掌握度的题目<br/></td></tr>
</table>

引入专注度计分，每个巩固练习的初始分值为 10 分
每次答题后，如命中上述事件，则专注度分值 -1。同时命中多个事件不累计扣分。

**使用场景：**- 分数等于 7 时，触发第一次提醒

- 分数等于 5 时，触发第二次提醒
- 分数等于 3 时，触发熔断

# 历史存档（不用看）

![RJwfwhxEFhhb1TbZUB0cSdQNn8e](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_RJwfwhxEFhhb1TbZUB0cSdQNn8e.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】

该图片为“巩固练习推题”的业务流程图。

**1. 流程起点与主要分支判断：**
*   流程开始于 **巩固练习推题**。
*   首先判断 **本次练习是否有未作答的题组**：
    *   **是**：进入 **按顺序进入题组** 流程。
    *   **否**：判断 **是否有答错的必做题**：
        *   **是**：进入 **进入错题重练** 流程。
        *   **否**：流程 **结束**。

**2. 按顺序进入题组后的逻辑：**
*   判断 **题库内是否有未推荐的必做题**：
    *   **是**：执行动作 **在必做题中选择「预期掌握度增量」最大的题目** (选择标准: $\max(\text{预期掌握度增量})$)，然后返回判断 **本次练习是否有未作答的题组**。
    *   **否**：判断 **当前题目是否为巩固练习内首题**：
        *   **是**：执行动作 **在题组的全部题池中选择「题目难度系数-用户知识点掌握度」绝对值最小的题目** (选择标准: $\min(|\text{题目难度系数} - \text{用户知识点掌握度}|)$)，然后返回判断 **本次练习是否有未作答的题组**。
        *   **否**：判断 **当前题组是否已达预估答题量**：
            *   **是**：执行动作 **在题组的全部题池中选择「题目难度系数-用户知识点掌握度」绝对值最小的题目** (选择标准: $\min(|\text{题目难度系数} - \text{用户知识点掌握度}|)$)，然后返回判断 **本次练习是否有未作答的题组**。
            *   **否**：进入 **用户的答题情况** 判断。

**3. 用户答题情况判断与相应推题策略：**
*   基于 **用户的答题情况** 分为三种分支：
    *   **连续2题错误**：
        *   判断 **是否有比上一题难度更低的必做题**：
            *   **有**：执行动作 **在难度更低的必做题中选择「题目难度系数-用户知识点掌握度」绝对值最小的题目** (选择标准: $\min(|\text{题目难度系数} - \text{用户知识点掌握度}|)$)。
            *   **没有**：执行动作 **在剩余的必做题中选择「题目难度系数-用户知识点掌握度」绝对值最小的题目** (选择标准: $\min(|\text{题目难度系数} - \text{用户知识点掌握度}|)$)。
        *   完成后，返回判断 **本次练习是否有未作答的题组**。
    *   **连续2题正确**：
        *   判断 **是否有比上一题难度更高的必做题**：
            *   **有**：执行动作 **在难度更高的必做题中选择「题目难度系数-用户知识点掌握度」绝对值最小的题目** (选择标准: $\min(|\text{题目难度系数} - \text{用户知识点掌握度}|)$)。
            *   **没有**：执行动作 **在题组的全部题池中选择「题目难度系数-用户知识点掌握度」绝对值最小的题目** (选择标准: $\min(|\text{题目难度系数} - \text{用户知识点掌握度}|)$)。
        *   完成后，返回判断 **本次练习是否有未作答的题组**。
    *   **其他**：
        *   执行动作 **在题组的全部题池中选择「题目难度系数-用户知识点掌握度」绝对值最小的题目** (选择标准: $\min(|\text{题目难度系数} - \text{用户知识点掌握度}|)$)。
        *   完成后，返回判断 **本次练习是否有未作答的题组**。

**4. 进入错题重练后的逻辑：**
*   此路径与 **按顺序进入题组** 后的逻辑汇合，共同指向判断 **题库内是否有未推荐的必做题**。

**Mermaid代码如下：**
```mermaid
flowchart TD
    A[巩固练习推题] --> B{本次练习是否有未<br/>作答的题组};
    B -- 否 --> C{是否有答错的必做<br/>题};
    C -- 否 --> D[结束];
    C -- 是 --> E[进入错题重练];
    B -- 是 --> F[按顺序进入题组];
    E --> G{题库内是否有未推<br/>荐的必做题};
    F --> G;

    G -- 是 --> H["在必做题中选择「预期<br/>掌握度增量」最大<br/>的题目"];
    H --> B;

    G -- 否 --> I{当前题目是否为巩<br/>固练习内首题};
    I -- 是 --> J["在题组的全部题池中选择<br/>「题目难度系数-用户知识点掌握度」<br/>绝对值最小的题目"];
    J --> B;

    I -- 否 --> K{当前题组是否已达<br/>预估答题量};
    K -- 是 --> J; 

    K -- 否 --> L{用户的答题情况};

    L -- 连续2题错误 --> M{是否有比上一题难<br/>度更低的必做题};
    M -- 有 --> N["在难度更低的必做题中选择<br/>「题目难度系数-用户知识点掌握度」<br/>绝对值最小的题目"];
    M -- 没有 --> O["在剩余的必做题中选择<br/>「题目难度系数-用户知识点掌握度」<br/>绝对值最小的题目"];
    N --> B;
    O --> B;

    L -- 连续2题正确 --> P{是否有比上一题难<br/>度更高的必做题};
    P -- 有 --> Q["在难度更高的必做题中选择<br/>「题目难度系数-用户知识点掌握度」<br/>绝对值最小的题目"];
    P -- 没有 --> R["在题组的全部题池中选择<br/>「题目难度系数-用户知识点掌握度」<br/>绝对值最小的题目"];
    Q --> B;
    R --> B;

    L -- 其他 --> S["在题组的全部题池中选择<br/>「题目难度系数-用户知识点掌握度」<br/>绝对值最小的题目"];
    S --> B;
```

【============== 图片解析 END ==============】

