运营后台&用户中心-技术设计

### 版本管理

| 版本号 | 日期 | 变更人 | 变更类型 | 变更详情 |
| --- | --- | --- | --- | --- |
| V1.0.0 | 2025-02-24 |  | 新建 | 新建文档 |
|  |  |  |  |  |
|  |  |  |  |  |

### 运营后台产品需求文档：

[运营后台产品_1期_账号_需求PRD ](https://fqspoay1wny.feishu.cn/docx/YJoxdehtzoVnLbxclgQcymcBnog)

[运营后台产品_1期_框架&教务_需求PRD](https://fqspoay1wny.feishu.cn/docx/T8fpdKabSo4BE2xMYk6cCa8unOd)

### 总体架构：

![board_HZfHwzRzfheJ5rbCj8pcn2xAnQb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746001727995.png)

业务框架，前端使用vue, 后端使用kratos + gin 

功能模块: 参考一期需求

内部用户管理：超级管理员可以创建所有用户，包括普通管理员和驻校运营人员

学校管理：管理员可以新增合作校信息，并管理学校状态(合作，终止合作等)

权限管理：不同人员绑定不同角色分配不同权限，最终决定不同用户展示看板不同，可操作项不同

数据字典管理：元数据初始化和管理

数据存储：持久化和缓存上层生产数据



### 运营后台的基本结构

![image_CUUXbijQqoDoO4xMbbAc5WGTnTf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746001728615.png)



### 学校合作状态

![image_XRjbbBSdsopZ1YxYDsvcda9rn2g](https://static.test.xiaoluxue.cn/demo41/prd_images/1746001729186.png)



### 账号生命周期

![image_I282bs6YXooZdex1hYCcnkJ5nHc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746001729693.png)

### 运营后台访问控制

运营后台需要飞连才能使用



