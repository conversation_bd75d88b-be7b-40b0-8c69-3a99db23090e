# 鲁班Code-Agent

## 工具使用说明

建议安装

1. typora（可选），md格式文件查看工具
2. cursor，模型使用 IDE
3. dify: [https://dify.test.xiaoluxue.cn/apps?category=workflow](https://dify.test.xiaoluxue.cn/apps?category=workflow)

### 1. 飞书文档处理工具

### 2. 服务端需求分析整理工具

- 依赖：无，直接使用即可
- 工具目录：`./ai-templates/prompts/`
- 提示词文件：`be-s1-gen-ai-prd.md`
- 使用方法
  1. 打开模型对话框
  2. 选择模型（见推荐模型部分）
  3. 引入`prompt`文件（`s1-gen-ai-prd.md`）
  4. 引入`prd`文档（需为markdown格式）
  5. 发送请求

  示例：

  ```text
  请根据 @be-s1-gen-ai-prd.md 要求处理 @需求文档.md
  ```

- 推荐模型
  1. gemini-2.5-pro-exp-03-25(首选)
  2. claude3.7-sonnet

- 不推荐模型
  1. gpt-x
  2. deepseek-x

### 3. 数据实体定义和信息精炼

- 依赖：无，直接使用即可
- 工具目录：`./ai-templates/prompts/`
- 提示词文件：`be-s2-gen-ai-dd-s1.md`, `be-s3-gen-ai-dd-s2.md`
- 注意需要依次执行 2 个提示词文件，最终生成高度精炼的数据实体定义文件
- 使用方法
  1. 打开模型对话框
  2. 选择模型（见推荐模型部分）
  3. 引入`prompt`文件（`be-s2-gen-ai-dd-s1.md`）
  4. 引入`ai-prd`文档（需为markdown格式）
  5. 发送请求

  示例：

  ```text
  请根据 @be-s2-gen-ai-dd-s1.md 要求处理 @ai-prd.md

  完成后指定第2个指令：
  请根据 @be-s3-gen-ai-dd-s2.md 要求处理 @第1个指令的产出文件.md
  ```

- 推荐模型
  1. gemini-2.5-pro-exp-05-06(首选)
  2. claude3.7-sonnet

- 不推荐模型
  1. gpt-x
  2. deepseek-x

### 4. 服务端技术架构设计工具

- 依赖：无，直接使用即可
- 工具目录：`./ai-templates/prompts/`
- 提示词文件：`be-s4-gen-ai-td-s1.md`
- 使用方法
  1. 打开模型对话框
  2. 选择模型（见推荐模型部分）
  3. 引入 `prompt` 文件（`be-s4-gen-ai-td-s1.md`）
  4. 引入 `ai-prd` 需求文档（需为markdown格式）
  5. 发送请求
- 注意事项
  1. gemini 模型对 mermaid 视图的处理经常性的不准确，检查文档后，如果发现视图无法渲染成功，请输入如下命令要求模型进行修改：`请仔细分析产出文档中的 mermaid 视图，再严格对照规范中对 mermaid 的要求进行检查和处理`，或者直接将错误的 mermaid 视图位置和报错信息贴给模型处理，如：

     ```text
     4.1 场景：学生首次使用AI课，系统初始化学科能力和知识点掌握度 视图渲染失败
      Parse error on line 12:
      ...-->>U: 展示个性化学习起点/内容end
      ----------------------^
      Expecting 'SPACE', 'NEWLINE', 'create', 'box', 'autonumber', 'activate', 'deactivate', 'title', 'legacy_title', 'acc_title', 'acc_descr', 'acc_descr_multiline_value', 'loop', 'rect', 'opt', 'alt', 'par', 'par_over', 'critical', 'break', 'participant', 'participant_actor', 'destroy', 'note', 'links', 'link', 'properties', 'details', 'ACTOR', got 'end'

      4.2 场景：学生完成一次答题，系统更新知识点掌握度 视图渲染失败
      Parse error on line 11:
      ...继续学习];    G --> H;end
      ---------------------^
      Expecting 'SEMI', 'NEWLINE', 'SPACE', 'EOF', 'subgraph', 'acc_title', 'acc_descr', 'acc_descr_multiline_value', 'AMP', 'COLON', 'STYLE', 'LINKSTYLE', 'CLASSDEF', 'CLASS', 'CLICK', 'DOWN', 'DEFAULT', 'NUM', 'COMMA', 'NODE_STRING', 'BRKT', 'MINUS', 'MULT', 'UNICODE_TEXT', 'direction_tb', 'direction_bt', 'direction_rl', 'direction_lr', got 'end'

      4.3 场景：学生完成学习任务，系统更新外化掌握度（星星）视图渲染失败
      Parse error on line 16:
      ...: 外化掌握度暂不更新    endend
      ---------------------^
      Expecting 'SPACE', 'NEWLINE', 'create', 'box', 'autonumber', 'activate', 'deactivate', 'title', 'legacy_title', 'acc_title', 'acc_descr', 'acc_descr_multiline_value', 'loop', 'rect', 'opt', 'alt', 'par', 'par_over', 'critical', 'break', 'participant', 'participant_actor', 'destroy', 'note', 'links', 'link', 'properties', 'details', 'ACTOR', got 'end'

      4.4 场景：学生设置学习目标，系统计算目标掌握度 视图渲染失败
      Parse error on line 11:
      ...标掌握度];    H --> I;end
      ---------------------^
      Expecting 'SEMI', 'NEWLINE', 'SPACE', 'EOF', 'subgraph', 'acc_title', 'acc_descr', 'acc_descr_multiline_value', 'AMP', 'COLON', 'STYLE', 'LINKSTYLE', 'CLASSDEF', 'CLASS', 'CLICK', 'DOWN', 'DEFAULT', 'NUM', 'COMMA', 'NODE_STRING', 'BRKT', 'MINUS', 'MULT', 'UNICODE_TEXT', 'direction_tb', 'direction_bt', 'direction_rl', 'direction_lr', got 'end'
     ```

  2. 模型对最后的自查清单往往会自作主张修改内容和格式，请仔细检查自查清单，如果发现有问题，请输入如下命令要求模型进行修改：`请仔细分析产出文档中的自查清单，再严格对照规范中对自查清单的要求和模板中自查清单的格式进行检查和处理，规范要求的是表格格式，并用 ✅❌⚠️N/A 来标记检查项，作为一个顶级架构师，你不应该犯如此低级的错误`

- 示例：

    ```text
    请根据 @be-s4-gen-ai-td-s1.md 要求处理 @ai-prd.md

    注：模型会询问依赖的公共服务，默认会列出 3 个公共服务：ucenter、admin、question，和公共组件，请根据实际情况选择或补充。
    ```

- 错误处理:
  1. mermaid图渲染错误

      请将报错 mermaid 图的位置和报错信息贴给模型处理，如：

        ```plaint
        3.2.1 推荐的逻辑分层模型的图渲染失败了，请解决
        Parse error on line 7:
        ...    subgraph API接口层 (Interface Layer)  
        -----------------------^
        Expecting 'SEMI', 'NEWLINE', 'SPACE', 'EOF', 'GRAPH', 'DIR', 'subgraph', 'SQS', 'end', 'AMP', 'COLON', 'START_LINK', 'STYLE', 'LINKSTYLE', 'CLASSDEF', 'CLASS', 'CLICK', 'DOWN', 'UP', 'NUM', 'NODE_STRING', 'BRKT', 'MINUS', 'MULT', 'UNICODE_TEXT', got 'PS'
        ```

- 推荐模型
  1. claude3.7-sonnet(首选)
  2. gemini-2.5-pro-exp-03-25
- 不推荐模型
  1. gpt-x，除非你愿意花钱用 4o，4.5-preview，否则不要使用，效果很差
  2. deepseek-x

### 5. 架构设计方案摘要提取

- 依赖：无，直接使用即可
- 工具目录：`./ai-templates/prompts/`
- 提示词文件：`be-s5-gen-ai-td-s2.md`
- 使用方法
  1. 打开模型对话框
  2. 选择模型（见推荐模型部分）
  3. 引入`prompt`文件（`be-s5-gen-ai-td-s2.md`）
  4. 引入`ai-td`文档（需为markdown格式）
  5. 发送请求

  示例：

  ```text
  请根据 @be-s5-gen-ai-td-s2.md 要求处理 @be-s4-ai-td-s1-xxx.md
  ```

- 推荐模型
  1. gemini-2.5-pro-exp-05-06(首选)
  2. claude3.7-sonnet

- 不推荐模型
  1. gpt-x
  2. deepseek-x
