---
【Cursor开发中rules配置文件】后端版（适配PHP/Laravel场景）
---

### 通用规则
1. 默认情况下，所有回复都必须是中文，而且需要在开头称呼用户为"帅哥：" 
2. 复杂需求拆解成小任务，分步实现，每完成一个小任务后再继续  
3. 代码实现前后要仔细检查，确保没有遗漏  
4. 在已有功能基础上添加新功能时，必须确保：  
   - 不影响原有功能  
   - 不添加其他功能、代码、逻辑、文件、配置、依赖  
5. 遵循Laravel架构设计，保持代码风格一致，符合PSR-12标准  
6. 代码修改遵循单一职责原则，不混合多个变更  
7. 在进行代码设计规划的时候，请符合"第一性原理"
8. 在代码实现的时候，请符合"KISS原则"和"SOLID原则"
9. 尽量复用Laravel提供的功能和已有代码，避免重复代码  
10. 不引入不必要的composer依赖，避免增加维护成本  
11. 确保代码可读性与可维护性，使用PHP文档注释格式  
12. 代码变更范围最小化，避免大范围修改  
13. 实现后进行基本逻辑自检，确保无错误  
14. 如果有疑问，先询问再修改，不要擅自做决定  

### 自动化执行与安全策略
15. 自动执行无需严格确认的操作，减少人为干预，提高执行效率：  
   - 自动执行composer命令、artisan命令等必要流程  
   - 删除、移动、重命名文件等常规操作无需额外确认  
   - 命令行操作中，非关键性指令（如清理缓存、运行迁移）可直接执行  
   - 涉及影响较大的操作（如覆盖文件、修改数据库结构）仍需确认  
16. 重要操作（如文件删除、数据库修改）应自动备份，避免误操作  
17. 涉及数据库变更的操作，优先生成Laravel迁移文件，而非直接执行SQL  
18. 执行高风险操作前，AI代码编辑器应自动检测影响范围，必要时提供提示  

### 数据库操作规范
19. 所有数据库操作优先使用Eloquent ORM或Query Builder，避免原生SQL  
20. 创建模型时遵循Laravel命名约定，正确设置表名、主键和关联关系  
21. 数据库事务必须正确处理，确保数据一致性  
22. 大批量数据操作使用队列或分批处理，避免超时  
23. 敏感数据字段必须使用Laravel的加密或哈希功能处理  

### 代码质量优化
24. 代码生成后，自动进行基本优化（如去除未使用的use语句、格式化代码）  
25. 对于可能影响性能的代码（如Eloquent查询、循环嵌套），提供优化建议  
26. 关键功能应提供异常处理机制，使用Laravel的异常处理方式  
27. 编写单元测试和功能测试，确保代码质量  

### 架构感知
28. AI代码编辑器应优先分析现有代码库，避免重复实现已有功能  
29. 在添加新功能时，优先复用Laravel内置功能和已有模块，而非从零编写  
30. 如遇架构不清晰的情况，先整理依赖关系，再执行修改  
31. 遵循Laravel的MVC架构和目录结构，正确放置各类文件  
32. 视图层使用Blade模板，避免在模板中编写复杂逻辑  
33. 业务逻辑放在Service层，避免Controller过于臃肿  

### 代码变更的可追溯性
34. 所有代码变更应附带清晰的commit信息，描述修改点和原因  
35. 对于影响较大的改动（如架构调整），可自动生成变更日志  
36. 如涉及API变更，应提供新旧版本兼容策略，使用API资源和版本控制  
37. AI代码编辑器在执行任务前，必须先读取「业务架构文档」和「最新变更记录」，确保逻辑一致性  
38. 每次代码修改后，AI必须自动生成「任务总结」，描述修改逻辑并更新变更记录  
39. 我们手动维护这些文档，并在AI执行任务前提供，确保长期架构记忆  

### Laravel特定规则
40. 使用Laravel的依赖注入和服务容器管理对象  
41. 遵循Laravel路由命名规范，使用资源路由  
42. 使用中间件处理请求过滤和权限控制  
43. 使用Laravel的验证机制处理表单验证  
44. 使用事件和监听器处理解耦的业务逻辑  
45. 使用队列处理耗时任务，避免阻塞请求  
46. 使用缓存优化性能，特别是频繁访问的数据  
47. 所有环境配置通过.env文件管理，不硬编码  
48. 使用Laravel Mix或Vite管理前端资源  
49. 使用语言文件实现国际化，避免硬编码提示信息  
50. 数据库索引和查询优化，确保高效访问MySQL数据库
