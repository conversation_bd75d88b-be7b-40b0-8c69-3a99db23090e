# 产品需求文档：AI课中 - 练习组件 V1.0

## 1. 需求背景与目标

### 1.1 需求背景
为了优化 AI 课中整体学习体验，提升学生作答流畅度和情感反馈体验，本次练习组件在 Demo V1.0 与 V1.1 基础上进行进一步升级。

### 1.2 项目收益
- 优化课中作答体验，提升课程满意度与学习效果。
- 通过即时反馈与情感激励，增强学生参与感。

### 1.3 覆盖用户
- 使用新 AI 课版本的全量学生用户。

### 1.4 方案简述
本次方案主要聚焦在练习组件的交互体验升级，包括：
- 进入练习转场动效优化。
- 题目间即时反馈机制。
- 支持勾画与加入错题本功能复用。

### 1.5 未来规划
1.  引入互动讲题模块，基于题目分布讲解和互动，进一步提升课中练习体验与教学效果。

## 2. 功能范围

### 2.1 核心功能

#### 2.1.1 练习组件转场与反馈 (P0)
- **需求描述**：增加进入练习的转场动画和题目作答间的即时反馈，优化交互体验。
- **主要功能点**：
    1.  **进入练习提示与转场**：
        -   在练习组件最顶部增加"下拉进入练习"提示条，预告即将进入练习环节。
        -   **首次进入练习**（所有题目未作答时）：
            -   触发时机：用户完成课程（文档组件学习）后，首次进入练习组件时。
            -   转场效果：翻页动效，IP动效配合"开始练习 + {课程名称}"文案，展示2秒。
        -   **再次进入练习**：
            -   触发时机：用户退出课程后再次进入，且课程进度停留在练习组件时。
            -   转场效果：IP动效配合"继续练习 + {课程名称}"文案，展示2秒。
        -   *原始PRD中"嗯 我现在想着是 进入练习时 老师在但是内容 侧滑进来有个题的转场。然后老师说完 题和老师滑走，然后进入题目。"为产品经理思考过程，不纳入正式需求。*
    2.  **练习环节页面框架**：
        -   **顶部导航栏**：
            -   退出按钮：逻辑同课程框架退出逻辑。
            -   作答计时：复用题型组件的计时功能，每题独立正向计时（格式 MM:SS，上限 59:59）。学生未作答退出则暂停计时，下次进入从上次计时点继续。
            -   作答进度：展示为 X/Y 格式。分母Y为预估学生作答题目总数；分子X根据推题策略动态变化（答对涨，答错无相似题也涨，答错有相似题则不变）。
        -   **答题组件区域**：
            -   题型组件：各题型在练习中的具体展示和交互逻辑，详见《题型组件PRD》及《课中题目推荐策略PRD》。
            -   勾画组件：功能同巩固练习，详见相关PRD。
            -   错题本组件：功能同巩固练习，详见相关PRD。
        -   **答疑组件入口**：
            -   右上角常驻"问一问"入口。
            -   题目进入解析状态前，屏蔽"问一问"入口。
            -   交互：长按题目内容（文字/图片）可选中并唤起"问一问"；点击"问一问"图标直接进入答疑组件。
    3.  **题目间转场与即时反馈**：
        -   *原始PRD中提及"题目间转场"但未提供详细描述和原型图，此处保留需求点，具体方案待定。*
        -   连对触发效果：同巩固练习的连对激励机制，详见相关PRD。
        -   作答后反馈页：展示作答反馈（正确/错误/连胜类型）。
        -   难度变化提示页：展示难度调整提示（上升/下降类型）。
        -   不认真作答提示：作答过程中，如监测到作答过快等异常行为，触发不认真作答提示。

### 2.2 辅助功能
- **答疑组件**：基于大模型能力和学生进行实时对话，解答学生问题。在练习组件中，可通过右上角入口或长按题目内容唤起。

### 2.3 非本期功能
- 长按题目内容唤起评论功能。
- 引入互动讲题模块（已在1.5 未来规划中提及）。

## 3. 计算规则与公式

### 3.1 作答计时
- **每题作答时长**：
    - 计算方式：从题目加载完成开始计时，到用户提交答案或退出该题为止。
    - 单位：秒。
    - 存储：记录每道题的独立作答时长。
    - 规则：
        - 格式 MM:SS，上限 59:59。
        - 学生未作答退出，暂停计时，下次进入后在上次计时基础上继续计时。

### 3.2 作答进度
- **进度分子计算**：
    - 初始值：0
    - 答对题目：分子 +1。
    - 答错题目，且无相似题推荐：分子 +1。
    - 答错题目，且有相似题推荐：分子不变。
- **进度分母计算**：
    - 预估当前练习需要作答的题目总数（具体算法参考《课中题目推荐策略PRD》）。

## 4. 用户场景与故事

### 场景1: 学生首次进入练习环节
作为一个刚学完某个知识点的学生，我希望能够通过一个清晰且富有激励性的转场动画进入练习环节，让我对即将开始的练习充满期待。

**关键步骤：**
1.  学生完成当前课程所有文档组件的学习。
2.  系统自动触发进入练习组件的转场。
3.  界面展示翻页动效，随后出现IP形象和我学习的课程名称，提示"开始练习"。
4.  2秒后，转场动画结束，加载第一道练习题。

**Mermaid图示 (活动图):**
```mermaid
stateDiagram-v2
    [*] --> 文档学习完成
    文档学习完成 --> 进入练习转场: 自动触发
    进入练习转场 --> 显示IP与课程名: "开始练习"
    显示IP与课程名 --> 加载首题: 2秒后
    加载首题 --> 答题中
    答题中 --> [*]
```

### 场景2: 学生在练习中途退出后重新进入
作为一个学生，我可能因为临时有事需要中途退出正在进行的练习。当我再次回到课程时，我希望能从上次离开的题目继续作答，并且我之前的答题用时能够得到保留。

**关键步骤：**
1.  学生在练习组件中作答了部分题目，中途点击"退出"按钮离开课程。
2.  系统记录学生当前练习的进度（如正在作答的题目、已作答题目的用时和答案）。
3.  学生稍后重新进入该课程。
4.  系统检测到学生上次停留在练习组件。
5.  界面展示IP动效和课程名称，提示"继续练习"。
6.  2秒后，转场结束，加载学生上次未完成的题目，并恢复之前的作答计时。如果上次题目已作答完毕但未进入下一题，则加载下一题。

**Mermaid图示 (状态图):**
```mermaid
stateDiagram-v2
    state "练习中" as InProgress
    state "课程入口" as CourseEntry
    state "恢复练习" as ResumePractice

    [*] --> CourseEntry
    CourseEntry --> InProgress: 学生选择课程进入
    InProgress --> CourseEntry: 学生中途退出 (系统记录进度)

    CourseEntry --> ResumePractice: 学生再次进入该课程 (检测到上次在练习)
    ResumePractice --> InProgress: 显示"继续练习"转场后，加载上次题目和计时
```

### 场景3: 学生完成一道题目并查看即时反馈
作为一个正在努力刷题的学生，我希望每做完一道题都能立刻看到自己是否答对，如果答对了能获得一些积极的反馈，如果答错了也能及时知道，方便我回顾。

**关键步骤：**
1.  学生在练习页面选择答案后，点击"提交"按钮。
2.  系统判断答案正误，并展示即时反馈（如正确图标、错误提示）。
3.  如果连续答对，可能会触发连对激励效果。
4.  系统根据题目推荐策略，准备下一道题目或展示练习总结（如果题目已全部完成）。
5.  学生可以查看当前题目的解析（如果提供）。
6.  学生点击"下一题"或相应按钮进入下一题目/环节。

**Mermaid图示 (流程图):**
```mermaid
flowchart TD
    A[学生选择答案并点击提交] --> B{系统判断答案正误}
    B -- 正确 --> C[展示正确反馈]
    C --> D{是否触发连对?}
    D -- 是 --> E[展示连对激励]
    D -- 否 --> F[准备下一题/总结]
    B -- 错误 --> G[展示错误反馈]
    G --> F
    E --> F
    F --> H{查看解析?}
    H -- 是 --> I[展示题目解析]
    H -- 否 --> J[进入下一题/环节]
    I --> J
```

## 5. 业务流程图

### 5.1 单节课内组件基本流程 (包含练习组件)
*此流程图参考原始PRD中对整体课程结构的描述以及图片`in_table_board_IhpkwURCUh57uYbMqBhckYbNnld`的解析。*
```mermaid
graph TD
    A[课程开场页] --> B[课程引入 （文档组件）];
    B --> C[知识点1 （文档组件）];
    C --> D[知识点2 （文档组件）];
    D --> E[练习1 （练习组件）];
    E --> F[知识点3 （文档组件）];
    F --> G[知识点4 （文档组件）];
    G --> H[练习2 （练习组件）];
    H --> I[课程总结 （文档组件）];
    I --> J[学习报告/结算页];

    subgraph "通用支持"
        QA[答疑组件] -- 可用于 --> B;
        QA -- 可用于 --> C;
        QA -- 可用于 --> D;
        QA -- 可用于 --> E;
        QA -- 可用于 --> F;
        QA -- 可用于 --> G;
        QA -- 可用于 --> H;
        QA -- 可用于 --> I;
    end
```

### 5.2 练习组件内部核心交互流程
```mermaid
graph TD
    subgraph "进入练习"
        StartPractice[开始/继续练习转场] --> LoadQuestion[加载第N题]
    end

    subgraph "答题过程"
        LoadQuestion --> DisplayQuestion[展示题目内容及选项]
        DisplayQuestion --> UserAction{用户操作}
        UserAction -- "选择答案" --> SelectAnswer[记录选择]
        SelectAnswer --> UserAction
        UserAction -- "勾画" --> Drawing[勾画操作]
        Drawing --> UserAction
        UserAction -- "标记不确定" --> MarkUncertain[标记/取消不确定]
        MarkUncertain --> UserAction
        UserAction -- "长按题目/点击问一问" --> AskQuestion[进入答疑组件]
        AskQuestion --> UserAction
        UserAction -- "提交答案" --> SubmitAnswer[提交所选答案]
    end

    subgraph "题目反馈与流转"
        SubmitAnswer --> JudgeAnswer{判断正误}
        JudgeAnswer -- "正确" --> CorrectFeedback[展示正确反馈/激励]
        JudgeAnswer -- "错误" --> IncorrectFeedback[展示错误反馈]
        CorrectFeedback --> CheckNext[检查是否有下一题/推荐]
        IncorrectFeedback --> CheckNext
        CheckNext -- "有" --> LoadNextQuestion[加载下一题]
        LoadNextQuestion --> DisplayQuestion
        CheckNext -- "无/结束" --> PracticeSummary[练习总结/进入下一组件]
    end

    subgraph "通用操作"
        DisplayQuestion -- "退出练习" --> ExitPractice[退出练习流程]
        UserAction -- "退出练习" --> ExitPractice
    end
```

## 6. 性能与安全需求

### 6.1 性能需求
- **响应时间**：
    - 练习题目加载：平均响应时间不超过 1.5 秒。
    - 答案提交与反馈：平均响应时间不超过 1 秒。
    - 进入/退出练习转场动画：流畅播放，不应有明显卡顿。
- **并发用户**：初期版本需支持至少 500 个并发用户同时在线进行练习，核心功能性能不发生明显衰减。

### 6.2 安全需求
- **数据安全**：
    - 学生作答记录、练习成绩等敏感数据在存储和传输时应进行加密处理。
    - 防止恶意提交或篡改答案数据。
- **权限控制**：
    - 学生只能访问其有权访问的课程下的练习题。

## 7. 验收标准

### 7.1 功能验收

#### 7.1.1 进入练习转场与提示
- **首次进入练习**：
    - 使用场景：用户完成某课程的文档学习后，首次点击进入练习。
    - 期望结果：顶部出现"下拉进入练习"提示。成功进入后，展示翻页动效，随后IP动效和"开始练习 + {课程名称}"文案显示2秒，然后加载第一道题。
- **再次进入练习**：
    - 使用场景：用户中途退出练习，再次进入该课程并定位到练习组件。
    - 期望结果：展示IP动效和"继续练习 + {课程名称}"文案显示2秒，然后加载上次未完成的题目或进度。

#### 7.1.2 练习环节页面框架
- **顶部导航栏**：
    - 使用场景：学生在练习页面。
    - 期望结果：正确显示退出按钮、作答计时器（MM:SS格式，每题独立计时，退出暂停进入继续）、作答进度（X/Y格式，分子根据策略变化）。
- **答题组件区域**：
    - 使用场景：学生进行答题。
    - 期望结果：题目内容、选项、勾画工具、错题本功能按设计正常展示和使用。
- **答疑组件入口**：
    - 使用场景：学生在练习中遇到疑问。
    - 期望结果：右上角"问一问"入口可见（题目进入解析前屏蔽）。长按题目内容可选中文本并唤起"问一问"。点击入口或唤起操作均能正常进入答疑组件。

#### 7.1.3 题目间反馈
- **作答反馈**：
    - 使用场景：学生提交答案后。
    - 期望结果：系统能即时给出正确/错误的反馈。连对时有相应激励。
- **不认真作答提示**：
    - 使用场景：学生作答行为异常（如过快）。
    - 期望结果：系统能触发不认真作答提示。

### 7.2 性能验收
*  **IMPORTANT AI (Chief Product Manager) INSTRUCTION:** 此节应该是对 6.1 节中每个需求点的详细验收标准，包括具体的测试用例和期望结果。
- **响应时间**：
    - 给定网络良好，用户进入练习组件，首题题目内容及选项在1.5秒内加载完成。
    - 给定网络良好，用户提交单选题答案后，正误反馈在1秒内显示。
- **并发用户**：
    - 模拟500个并发用户同时进行练习答题（每秒约10-20次提交），持续运行15分钟，系统无崩溃，题目加载和答案提交的平均响应时间增加不超过20%。

### 7.3 安全验收
*  **IMPORTANT AI (Chief Product Manager) INSTRUCTION:** 此节应该是对 6.2 节中每个需求点的详细验收标准，包括具体的测试用例和期望结果。
- **数据安全**：
    - 通过抓包工具检查，确认学生答案提交、成绩返回等数据在传输过程中使用HTTPS加密。
    - 尝试通过工具构造非法答题数据提交，系统应能有效拦截或标记异常，后台数据不被污染。
- **权限控制**：
    - 学生A未购买/加入课程X，尝试通过URL直接访问课程X的练习题ID，系统应提示无权限。
    - 学生B已加入课程Y，可以正常访问和作答课程Y的练习题。

## 8. 其他需求

### 8.1 可用性需求
- **界面友好**：练习界面布局清晰，题目内容易于阅读，选项易于点选。核心操作（如提交答案、查看下一题）直观易懂。
- **容错处理**：
    - 网络突然断开再重连，系统应能尝试恢复学生当前的答题状态或给出明确提示。
    - 学生进行异常操作（如快速重复点击提交），前端应有适当限制，后端能正确处理。
- **兼容性**：支持主流浏览器（Chrome, Safari, Edge 最新版本）及主流移动端操作系统（iOS, Android 最新系统版本）的Web访问。

### 8.2 维护性需求
- **配置管理**：
    - 练习转场动画中的IP形象、固定文案等应支持后台替换或配置。
    - 题目推荐策略中的相关参数（如相似题判断阈值）应可由运营或教研人员在后台调整。
- **监控告警**：
    - 当练习题库加载失败、答案提交服务异常或错误率超过阈值时，系统应能自动告警。
- **日志管理**：详细记录学生练习行为日志（题目加载、答案提交、作答用时、勾画、错题本操作等），便于问题排查和数据分析。

## 9. 用户反馈和迭代计划

### 9.1 用户反馈机制
- 在练习结束后或特定界面提供用户反馈入口，收集学生对练习难度、题目质量、交互体验等的意见。
- 定期分析埋点数据中关于练习完成率、平均用时、各题正确率等，结合用户主动反馈，挖掘改进点。

### 9.2 迭代计划
- **V1.0上线后**：重点关注练习组件的稳定性和核心交互体验，收集用户对转场、即时反馈的接受度。
- **后续迭代**：
    - 根据反馈优化题目间转场的具体设计和实现。
    - 逐步引入1.5节规划的"互动讲题"模块。
    - 持续优化题目推荐策略和个性化反馈机制。

## 10. 需求检查清单
*  **IMPORTANT AI (Chief Product Manager) INSTRUCTION:** 本节必须保留，并且需要根据原始需求文档中的具体需求点进行填写。"原始需求"列，必须详细列出来自原始需求文档"详细产品方案"的所有具体功能点和需求描述。确保覆盖所有细化要求，而不是仅列出概览性条目，随后，将这些详细的原始需求点**严格映射**到新生成文档中对应的具体章节或功能点上，并进行比对评估。检查项必须全部完成，不得遗漏。

| 原始需求 (摘自PRD "AI 课中 - 练习组件 1.0" 第五节 "详细产品方案")                                                              | 对应新PRD需求点 (章节)                                  | 完整性 | 正确性 | 一致性 | 可验证性 | 可跟踪性 | 备注                                                                         |
| ------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------- | ------ | ------ | ------ | -------- | -------- | ---------------------------------------------------------------------------- |
| 进入练习提示：进入练习转场 (原型图 `in_table_image_E3xVbhOCBoczFFxa0Vic8qyFnLb` )                                              | 2.1.1 (练习组件转场与反馈 / 进入练习提示与转场)             | ✅      | ✅      | ✅      | ✅        | ✅        | 包含下拉提示，首次/再次进入的转场、文案、时长。                                          |
| 练习组件最顶部，增加"下拉进入练习" tip ，预告即将进入练习环节。                                                                 | 2.1.1 (练习组件转场与反馈 / 进入练习提示与转场)             | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                              |
| 首次进入练习（所有题目未作答时）：时机、转场动效（翻页）、IP动效+文案（开始练习+课程名称）、展示时间2s。                             | 2.1.1 (练习组件转场与反馈 / 进入练习提示与转场)             | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                              |
| 再次进入练习：时机、IP动效+文案（继续练习+课程名称）、展示时间2s。                                                              | 2.1.1 (练习组件转场与反馈 / 进入练习提示与转场)             | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                              |
| 练习环节页面框架 (原型图 `in_table_image_K4wFb9b0Qof0G2xH6FvcBEGFnjx` )                                                      | 2.1.1 (练习组件转场与反馈 / 练习环节页面框架)               | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                              |
| 顶部导航栏：退出按钮（逻辑同课程框架）。                                                                                        | 2.1.1 (练习组件转场与反馈 / 练习环节页面框架 / 顶部导航栏)    | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                              |
| 顶部导航栏：作答计时（复用题型组件，每题独立正计时 MM:SS, 上限59:59，未作答退出暂停，下次进入继续）。                                | 2.1.1 (练习组件转场与反馈 / 练习环节页面框架 / 顶部导航栏), 3.1 | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                              |
| 顶部导航栏：作答进度（分母：预估学生作答数量；分子：依据推题策略，答对涨，答错无相似题涨，答错有相似题不变）。                        | 2.1.1 (练习组件转场与反馈 / 练习环节页面框架 / 顶部导航栏), 3.2 | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                              |
| 连对触发效果：同巩固练习。                                                                                                      | 2.1.1 (练习组件转场与反馈 / 题目间转场与即时反馈)           | ✅      | ✅      | ✅      | ✅        | ✅        | 需参考巩固练习PRD                                                              |
| 答题组件：题型组件（各题型在练习中的展示和交互，见相关PRD）。                                                                      | 2.1.1 (练习组件转场与反馈 / 练习环节页面框架 / 答题组件区域)  | ✅      | ✅      | ✅      | ✅        | ✅        | 需参考题型组件PRD                                                              |
| 答题组件：题目推荐（见课中题目推荐策略PRD）。                                                                                      | 2.1.1 (练习组件转场与反馈 / 练习环节页面框架 / 答题组件区域)  | ✅      | ✅      | ✅      | ✅        | ✅        | 需参考题目推荐策略PRD                                                            |
| 答题组件：勾画组件（同巩固练习）。                                                                                                | 2.1.1 (练习组件转场与反馈 / 练习环节页面框架 / 答题组件区域)  | ✅      | ✅      | ✅      | ✅        | ✅        | 需参考巩固练习PRD                                                              |
| 答题组件：错题本组件（同巩固练习）。                                                                                              | 2.1.1 (练习组件转场与反馈 / 练习环节页面框架 / 答题组件区域)  | ✅      | ✅      | ✅      | ✅        | ✅        | 需参考巩固练习PRD                                                              |
| 答疑组件：入口（右上角常驻"问一问"，题目进入解析状态前屏蔽）。交互（长按题目内容选中唤起问一问，点击图标进入）。                       | 2.1.1 (练习组件转场与反馈 / 练习环节页面框架 / 答疑组件入口), 2.2 | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                              |
| 题目间转场 (原型图 `in_table_image_BosmbvrKeo8580x9RqycMEZ7nYb`)                                                               | 2.1.1 (练习组件转场与反馈 / 题目间转场与即时反馈)           | ⚠️      | ✅      | ✅      | ⚠️        | ✅        | 原始PRD未提供详细描述，新PRD中已注明具体方案待定。原型图主要展示答题后反馈，非题目间转场本身。 |

- 完整性：原始需求是否都有对应的优化后需求点，无遗漏
- 正确性：优化后需求描述是否准确表达了原始需求的意图
- 一致性：优化后需求之间是否有冲突或重复
- 可验证性：优化后需求是否有明确的验收标准
- 可跟踪性：优化后需求是否有明确的优先级和依赖关系

✅表示通过；❌表示未通过；⚠️表示描述正确，但细节不完整（如计算规则、阈值等，需要和 PM 进一步沟通）；N/A表示不适用。每个需求点都需要填写。

## 11. 附录

### 11.1 原型图
原型图及UI截图请参考原始PRD文档 `documents/raw-docs/PRD - AI 课中 - 练习组件 1.0_analyzed.md` 中对应的图片链接和嵌入的图片分析。主要涉及的图片有：
- `in_table_board_IhpkwURCUh57uYbMqBhckYbNnld` (单节课程结构图)
- `in_table_image_E3xVbhOCBoczFFxa0Vic8qyFnLb` (进入练习转场示意图 - 与文档组件学习内容页类似)
- `in_table_image_K4wFb9b0Qof0G2xH6FvcBEGFnjx` (练习环节页面框架示意图 - 单题作答后反馈页)
- `in_table_image_BosmbvrKeo8580x9RqycMEZ7nYb` (题目间转场示意图 - 题目解析及激励页)
- `in_table_image_GFWobx4zHo13YlxhwhlccN3GnRe` (练习反馈总结页示意图)

*注意：上述原型图在原始文档中部分用于示意，部分为其他组件的截图被引用说明类似场景，具体练习组件的精确UI需以最终设计稿为准。*

### 11.2 术语表
- **IP动效**：指课程或品牌相关的卡通形象的动画效果。
- **推题策略**：指根据学生历史作答情况、知识点掌握程度等因素动态推荐练习题目的算法或规则。
- **勾画组件**：允许用户在题目上进行圈点、划线等标记的工具。
- **错题本组件**：允许用户收藏错题或重要题目，以便后续复习的功能。
- **答疑组件**：提供AI或人工在线问答服务的模块。

### 11.3 参考资料
- 原始需求文档: `documents/raw-docs/PRD - AI 课中 - 练习组件 1.0_analyzed.md`
- 《AI 课中 - V1.0》 (关联需求，具体文档待补充)
- 《题型支持》 (关联需求，具体文档待补充)
- 《课程框架PRD》 (本文档中多处提及"逻辑同课程框架"，需参考)
- 《题型组件PRD》 (待补充)
- 《课中题目推荐策略PRD》 (待补充)
- 《巩固练习PRD》 (本文档中多处提及"同巩固练习"，需参考)

--- 等待您的命令，指挥官 