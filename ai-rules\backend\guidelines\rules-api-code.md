# API 设计规范 (v1.0)
### 1.1. 目的
本文档旨在为项目定义一套统一的 API 设计规范，确保 API 的一致性、易用性、可维护性和健壮性。所有对外暴露的 HTTP/gRPC API（通过网关转换为HTTP）均需遵循此规范。

### 1.2. 核心原则
1. **严谨高效 (Rigorous and Efficient)**：严谨评估和审查接口设计方案，确保其在功能、性能、安全等方面的**正确性**和**有效性**。高效完成工作，避免不必要的重复劳动，并**及时**地提供反馈和更新。
2. **用户中心 (Consumer-Centric)**：始终从接口调用者（前端应用、其他微服务等）的角度思考，设计出易于理解、易于使用、易于集成的接口。优先考虑调用方的体验。
3. **简洁明了 (Clear and Concise)**：接口的命名、参数、请求/响应体结构都应力求简洁、直观、无歧义。避免不必要的复杂性和冗余信息。
4. **一致性 (Consistent)**：在整个服务或产品线中，接口的设计风格、命名约定、错误处理机制、数据格式等应保持高度一致。
5. **职责单一 (Single Responsibility for Endpoints)**：每个接口端点 (endpoint) 应有明确且单一的职责。避免设计“万能接口”。
6. **面向资源 (Resource-Oriented - 尤其RESTful)**：如果设计RESTful API，应以资源为核心进行设计，使用标准的HTTP方法表达对资源的操作。
7. **可扩展性与兼容性 (Extensible & Compatible)**：接口设计应考虑到未来的需求变化，支持向后兼容的扩展。避免破坏性变更。
8. **安全性 (Secure by Design)**：从设计之初就考虑接口的安全性，包括认证、授权、输入验证、防注入、防滥用等（虽然具体实现可能由开发负责，但设计时需有此意识并提供必要的支持字段或机制）。

## 2. 通用设计原则

### 2.1. RESTful 风格
* 尽可能遵循 REST (Representational State Transfer) 架构风格。
* 使用标准的 HTTP 方法操作资源。
* 资源通过 URL (Uniform Resource Locator) 进行唯一标识。

### 2.2. 资源导向
* API 应围绕"资源 (Resource)"进行组织，资源是 API 可操作的主要实体。
* URL 路径应表示资源，而不是操作。操作通过 HTTP 方法体现。

### 2.3. HTTP 方法使用
* **GET**: 用于读取资源。GET 请求应该是安全的（不产生副作用）和幂等的。
* **POST**: 用于创建新资源，或执行非幂等的控制器类操作。
* **PUT**: 用于完整替换或更新已存在的资源。PUT 请求应该是幂等的。
* **PATCH**: 用于部分更新已存在的资源。PATCH 请求应该是幂等的。
* **DELETE**: 用于删除资源。DELETE 请求应该是幂等的。

### 2.4. Idempotency (幂等性)
* 对于 `GET`, `PUT`, `PATCH`, `DELETE` 方法，应确保其操作的幂等性。即多次执行相同的请求，对资源状态产生的影响与执行一次相同。
* `POST` 方法通常不是幂等的，除非服务端做了特殊处理。

## 3. URL 设计

### 3.1. 路径结构
* **路径中不包含任何参数(即不完全遵循 restful API 规则)**: 所有请求参数都放在 query param 或 request body 中。
* **名词复数**: 资源名称使用名词复数形式。例如：`/users`, `/orders`。
* **层级关系**: 使用 `/` 表示资源的层级关系。例如：`/users/addresses`。
* **小写字母**: URL 路径全部使用小写字母，单词间使用连字符 `-` (kebab-case) 或下划线 `_` (snake_case)。**本项目统一使用下划线 `_` (snake_case)**，与数据库字段命名保持一致。
  * 示例: `/api/v1/user_profiles/activity_logs`
* 避免在 URL 中使用动词，除非是表示非资源类的操作（控制器动作）。

### 3.2. 参数设计
* **查询参数**: 查询参数用于过滤、排序、分页等非资源定位的操作。
* **路径参数**: 路径参数用于唯一标识资源或资源的子资源。

### 3.2. 版本控制
* API 版本控制通过 **URL 路径**实现。
* 版本号置于基础路径之后，例如：`/api/v1/users`, `/api/v2/products`。
* 版本号使用 `v` 前缀加整数，如 `v1`, `v2`。
* 内部使用接口，还需要增加`internal`，即：`/api/v1/internal/...`。

### 3.3. 查询参数
* 用于过滤、排序、分页等非资源定位的操作。
* 参数名使用小写驼峰 (camelCase) 或下划线 (snake_case)。**本项目统一使用小写驼峰 (camelCase)** 以便与请求/响应体中的 JSON 字段保持一致性。
  * 示例: `/users?isActive=true&sortBy=createdAt&page=1&pageSize=20`

## 4. 请求格式

### 4.1. Content-Type
* 对于包含请求体的请求 (POST, PUT, PATCH)，客户端必须指定 `Content-Type` 请求头。
* 项目统一使用 `application/json` 作为请求体的主要格式。

### 4.2. 请求体 (Request Body)
* **JSON 格式**: 请求体使用 JSON 格式。
* **字段命名**: JSON 对象的字段名统一使用**小写驼峰 (camelCase)** 命名法。
  * 示例: `{"userName": "john_doe", "emailAddress": "<EMAIL>"}`
* **时间格式**: 时间戳统一使用 **UTC 秒数 (Unix Timestamp in seconds)**，类型为**整数 (int64)**。
* **整数类型**: 项目中所有 `int` 类型数据，在 API 交互时统一视为 `int64`。
* **避免 null 字段**: 对于可选字段，如果客户端未提供值，建议在请求体中不包含该字段，而不是发送 `null` 值，除非 `null` 具有特定业务含义。

### 4.3. 请求头 (Headers)
* **Authorization**: 用于传递认证凭证 (如 Bearer Token)。
* **Accept**: 客户端可使用 `Accept` 头指定期望的响应格式，服务端应尽量满足，默认为 `application/json`。
* **X-Request-ID (或 Trace-ID)**: 用于链路追踪，客户端可生成或由网关注入。
* 自定义请求头应使用 `X-` 前缀。

## 5. 响应格式

### 5.1. Content-Type
* 服务端响应默认使用 `application/json` 格式，并设置 `Content-Type: application/json; charset=utf-8` 响应头。

### 5.2. HTTP 状态码
* 我们只使用以下状态码表达操作结果和请求状态。
* **200 (成功)**:
  * `200 OK`: 请求成功，用于 GET, PUT, PATCH, DELETE 操作成功，不再细分`2xx`状态码。
* **4xx (客户端错误)**:
  * `400 Bad Request`: 请求无效，如参数错误、格式错误、语义错误。响应体中应包含详细错误信息。
  * `401 Unauthorized`: 未认证。客户端需要提供有效的认证凭证。
  * `403 Forbidden`: 已认证，但无权限访问该资源。
  * `404 Not Found`: 请求的资源不存在。
  * `429 Too Many Requests`: 请求过于频繁，触发速率限制。
* **5xx (服务端错误)**:
  * `500 Internal Server Error`: 服务端发生未知错误。响应体中不应暴露敏感的错误细节。
  * `502 Bad Gateway`: 作为网关或代理，从上游服务器收到无效响应。
  * `503 Service Unavailable`: 服务暂时不可用 (如过载或维护)。
  * `504 Gateway Timeout`: 作为网关或代理，未及时从上游服务器获得响应。

### 5.3. 统一响应结构
#### 5.3.1. 成功响应
* 对于返回数据的成功请求 (如 GET, 或 POST/PUT/PATCH 返回更新后资源)，响应体应包含实际数据。
* 统一的响应包装结构，包括 GET/POST/PUT/PATCH 全部请求类型，使用如下结构：
  ```json
  {
    "code": 0, // 0 表示成功，对应HTTP状态码200，其他情况下，code为7位长度，前3位为http状态码，后4位为业务自定义错误码
    "message": "Success",
    "data": { ... }, // 实际业务数据对象或数组
    "pageInfo": { ... } // 分页信息，仅在分页查询时返回
  }
  ```
  **注意**: 项目当前倾向于直接返回数据对象/数组，除非有特定元数据需要传递。`code` 和 `message` 的信息已由HTTP状态码和标准文本承载。

#### 5.3.2. 错误响应
* 所有客户端错误 (4xx) 和服务端错误 (5xx) 均应返回统一结构的 JSON 错误响应体。
* 错误响应体应包含：
  * `code`: 业务错误码 (int)，用于程序识别错误类型。0 通常表示成功，非0表示具体错误。
  * `message`: 人类可读的错误描述信息。
  * `details`: (可选) 更详细的错误信息或调试信息 (如字段校验失败详情)，仅在开发或特定场景下返回。
* **示例错误响应**: (HTTP Status: 400 Bad Request)
  ```json
  {
    "code": 40001, // 业务自定义错误码，例如参数无效
    "message": "Invalid input parameters.",
    "details": [
      {
        "field": "userName",
        "issue": "must not be empty"
      },
      {
        "field": "emailAddress",
        "issue": "must be a valid email format"
      }
    ]
  }
  ```

### 5.4. 数据类型与格式
* **JSON 字段命名**: 统一使用**小写驼峰 (camelCase)**。
* **时间格式**: 时间戳统一使用 **UTC 秒数 (Unix Timestamp in seconds)**，类型为**整数 (int64)**。
* **整数类型**: `int` 在API层面视为 `int64`。
* **枚举 (Enums)**: 枚举值建议使用字符串形式，便于阅读和扩展。如果使用数字，需在文档中明确其含义。
* **状态**: 状态字段，如 `status` 或 `state`，使用`int64`类型。
* **Null 值**: 如果一个字段确实没有值，可以返回 `null`。对于集合类型，如果没有元素，应返回空数组 `[]` 而不是 `null`。

## 6. 分页 (Pagination)

* 对于返回资源列表的 API，必须支持分页。
* **分页与排序规范**:
  * **请求字段**：
    * 使用 `page`/`pageSize` 进行分页。
    * 使用 `sortBy=field&orderBy=asc|desc` 进行排序。
* **响应体**: 列表数据包含在 `data` 字段 (如果采用包装结构)，或直接作为响应体。
* **响应元数据**: 分页信息包含在结构体`pageInfo`中，如：
  * `pageInfo.total`: 总条目数。
  * `pageInfo.pageSize`: 每页条目数。
  * `pageInfo.page`: 当前页码。
* **示例分页响应 (包装结构)**:
  ```json
  {
    "code": 0,
    "message": "Success",
    "data": [ ... ], // 当前页的资源列表
    "pageInfo": {
      "total": 120,
      "pageSize": 10,
      "page": 1,
    }
  }
  ```

## 7. 过滤、排序与搜索

### 7.1. 过滤 (Filtering)
* 通过查询参数实现，参数名应清晰表达过滤字段。
* 示例: `/products?categoryId=123&status=1`

### 7.2. 排序 (Sorting)
* 通过 `sortBy` (或 `sort`) 查询参数指定排序字段，支持多个字段排序。
* 通过 `sortOrder` (或前缀 `+`/`-`) 指定排序方向 (如 `asc`, `desc`)。
* 示例: `/users?sortBy=lastName&sortOrder=asc` 或 `/users?sort=-createdAt,firstName`

### 7.3. 搜索 (Searching)
* 通常使用 `q` 或 `search` 查询参数进行全文或关键词搜索。

## 8. 认证与授权 (Authentication & Authorization)

* API 应通过 HTTPS 提供服务。
* 认证机制通常基于 Token (如 JWT, OAuth2 Bearer Token)，通过 `Authorization` 请求头传递。
* 未认证的请求应返回 `401 Unauthorized`。
* 已认证但无权访问特定资源的请求应返回 `403 Forbidden`。
* API 设计应考虑操作所需的权限范围 (Scopes)。

## 9. 速率限制 (Rate Limiting)

* 对 API 调用频率进行限制，防止滥用。
* 当超出限制时，返回 `429 Too Many Requests` 状态码。
* 响应头中可包含速率限制信息：
  * `X-RateLimit-Limit`: 当前时间窗口内允许的最大请求数。
  * `X-RateLimit-Remaining`: 当前时间窗口内剩余的请求数。
  * `X-RateLimit-Reset`: 距离限制重置的剩余秒数 (Unix Timestamp)。

## 10. 缓存 (Caching)

* 合理利用 HTTP 缓存机制以提高性能和减少服务器负载。
* 使用 `ETag` 响应头进行条件请求。
* 使用 `Cache-Control` 响应头控制缓存行为 (如 `max-age`, `no-cache`, `no-store`)。
* `Last-Modified` 响应头。

## 11. API 文档

* 所有 API 必须有清晰、准确、最新的文档。
* 推荐使用 **OpenAPI (Swagger)** 规范编写和生成 API 文档。
* 文档应包含：
  * 每个端点的详细描述、HTTP 方法、URL 路径。
  * 请求参数 (路径参数、查询参数、请求体) 的名称、类型、是否必需、描述和示例值。
  * 响应格式、可能的 HTTP 状态码、成功和错误响应体的结构与示例。
  * 认证要求。
* API 文档应与代码实现同步更新。

## 12. 向后兼容性与演进

* API 发布后，应尽量保持向后兼容。
* **破坏性变更 (Breaking Changes)** 应通过增加 API 版本号 (URL路径版本控制) 来管理。
  * 例如：删除字段、修改字段类型、修改资源路径、删除端点。
* **非破坏性变更 (Non-Breaking Changes)** 通常不需要改变 API 版本。
  * 例如：添加新端点、添加新的可选请求参数、在响应中添加新字段。
* 详细记录 API 变更日志。

## 13. 安全考量

* **HTTPS**: 所有 API 通信必须使用 HTTPS 加密。
* **输入验证**: 对所有来自客户端的输入进行严格验证 (格式、类型、长度、范围)。在 Controller 层进行参数绑定和基础校验，Service 层进行业务逻辑校验。
* **输出编码**: 对响应数据进行适当编码，防止 XSS 攻击。
* **SQL 注入防护**: 使用参数化查询或 ORM，禁止拼接 SQL。
* **权限控制**: 确保用户只能访问其被授权的资源和操作。
* **敏感数据**: 不在 URL 中传递敏感数据；响应中对敏感数据进行脱敏或不返回。
* 定期进行安全审计和漏洞扫描。

---
*本规范将根据项目实践持续更新。*
