PRD - AI 课中 - 练习组件 1.0 

**版本管理**

| 版本号 | 日期 | 变更人 | 变更类型 | 变更详情 |
| --- | --- | --- | --- | --- |
| V1.0 | 2025.04.25 | 张博 | 新建 |  |

- 新建文档
**关联需求**

| 关联需求名称 | 所属 PM | 需求进度 | 文档链接 |
| --- | --- | --- | --- |
| AI 课中 - V1.0 |  |  |  |
| 题型支持 |  |  |  |

**设计稿**

视觉稿：待补充



# 一、背景和目标

## 需求背景

为了优化 AI 课中整体学习体验，提升学生作答流畅度和情感反馈体验，本次练习组件在 Demo V1.0 与 V1.1 基础上进行进一步升级。

目标是通过引入更友好的转场动画、作答即时反馈机制，提升课程满意度与学习效果。

## 项目收益

- 优化课中作答体验。
- 通过即时反馈与情感激励，增强学生参与感。
## 覆盖用户

- 使用新 AI 课版本的全量学生用户
## 方案简述

本次方案主要聚焦在练习组件的交互体验升级，包括：

- 进入练习转场动效优化
- 题目间即时反馈机制
- 支持勾画与加入错题本功能复用


## 未来会做什么

1. 引入互动讲题模块，基于题目分布讲解和互动，进一步提升课中练习体验与教学效果。


# 二、名词说明

暂无



# 三、业务流程

整体课程结构和demo阶段类似，V1.0只支持 「文档组件」、「练习组件」、「答疑组件」

一节课由不同的可配置组件构成。组件之间不同的组合方式构成了不同的课程类型。

答疑组件在课中不能单独配置，需要挂载在「文档组件」/「练习组件」下

| 单节课程结构 | 组件类型 | 说明 |
| --- | --- | --- |
| ![in_table_board_IhpkwURCUh57uYbMqBhckYbNnld](https://static.test.xiaoluxue.cn/demo41/prd_images/1746845253032.png) | 文档组件 | 课程内容为JS文档形式，支持学生在上课过程中自由浏览，支持点击/长按进行提问 |
|  | 练习组件 | 支持各类题型（一期仅支持单选），可按策略推送题目 |
|  | 答疑组件 | 基于大模型能力和学生进行实时对话，解答学生问题 |

![in_table_board_IhpkwURCUh57uYbMqBhckYbNnld]

###### 图片分析
【============== 图片解析 BEGIN ==============】

基于提供的图片及其OCR文本内容，现进行如下解析与总结：

1.  **图片类型、关键元素、层级结构及核心作用**

    *   **图片类型**: 该图片可被视为一种 **模块功能流程图** 或 **组件化结构示意图**。它清晰地展示了“单节课内容”的顺序流程以及在每个流程节点所使用的功能组件。
    *   **关键元素**:
        *   **流程阶段**: 课程开场页、课程引入、知识点1、知识点2、练习1、知识点3、知识点4、练习2、课程总结、学习报告。
        *   **功能组件**: 功能页面、文档组件、练习组件、答疑组件。
        *   ** overarching 主题**: 单节课内容。
    *   **层级化结构与关联**:
        1.  **顶层**: “单节课内容”作为整个流程的起点和容器。
        2.  **中层 - 顺序流程**: 从“课程开场页”开始，依次经历“课程引入”、“知识点”、“练习”，直至“课程总结”和“学习报告”，构成一个完整的单节课学习路径。
        3.  **中层 - 组件映射**:
            *   “课程开场页”和“学习报告” 关联 “功能页面”。
            *   “课程引入”、“知识点1-4”、“课程总结” 关联 “文档组件”。
            *   “练习1”和“练习2” 关联 “练习组件” (与PRD主题“练习组件 1.0”直接相关)。
        4.  **辅助层 - 通用支持**: “答疑组件”在图中横向排列，暗示其作为一个通用支持功能，在课程的各个阶段均可被访问或集成。
    *   **核心作用与价值**:
        *   **明确课程结构**: 清晰定义了一节AI课程的标准流程和组成部分。
        *   **组件化设计**: 体现了功能模块化的设计思想，不同类型的内容（知识传递、练习互动、总结报告）由专门的组件承载，便于开发、维护和复用。
        *   **“练习组件”定位**: 明确了“练习组件 1.0”在课程流程中的具体应用节点（练习1、练习2），是实现互动练习功能的关键。
        *   **用户体验考量**: 通过不同组件的组合，旨在为用户提供结构化、多样化的学习体验，并通过“答疑组件”提供即时帮助。

2.  **各组成部分功能模块拆解及概述**

    *   **单节课内容**:
        *   **功能概述**: 代表一节完整课程的总体结构和流程。
    *   **课程开场页**:
        *   **功能概述**: 课程的起始引导页面，用于展示课程基本信息或欢迎语。
    *   **课程引入**:
        *   **功能概述**: 课程的导入部分，用于介绍课程背景、目标或引起学生兴趣。
    *   **知识点1, 知识点2, 知识点3, 知识点4**:
        *   **功能概述**: 承载具体教学内容的模块，用于讲解理论知识、概念等。
    *   **练习1, 练习2**:
        *   **功能概述**: 课程中的互动练习环节，用于巩固所学知识点，检验学习效果。
    *   **课程总结**:
        *   **功能概述**: 对本节课内容进行回顾和总结。
    *   **学习报告**:
        *   **功能概述**: 课程结束后生成的学习成果报告，展示学习数据和表现。
    *   **功能页面 (组件类型)**:
        *   **功能概述**: 一种通用型页面组件，用于承载如开场页、学习报告等特定功能的展示。
    *   **文档组件 (组件类型)**:
        *   **功能概述**: 用于显示静态或富文本内容的组件，如图文、音视频等，主要用于知识点讲授和信息呈现。
    *   **练习组件 (组件类型)**:
        *   **功能概述**: 提供交互式练习题目的组件，支持用户作答、提交并可能获得反馈。
    *   **答疑组件 (组件类型)**:
        *   **功能概述**: 提供问答功能的组件，学生可以提问，并获取解答，贯穿学习过程。

3.  **服务端需提供的功能和返回的数据内容**

    服务端需要为每个阶段和组件提供相应的数据和支持：

    *   **课程开场页 (功能页面)**: 服务端需提供课程的标题、描述、封面信息或开场引导所需的多媒体素材信息。
    *   **课程引入 (文档组件)**: 服务端需提供课程引入部分的详细内容，可能包括文本、图片、音视频链接等。
    *   **知识点 (文档组件)**: 对每个知识点，服务端需提供其对应的教学材料，如文本讲解、图示、代码示例、相关音视频资源等。
    *   **练习 (练习组件)**:
        *   服务端需提供练习题目的具体内容，包括题干描述、选项（如果是选择题）、题目类型标识、作答区域定义。
        *   服务端需提供该练习题目的正确答案、评分标准或解题思路，用于后续的批改和反馈。
        *   服务端需接收用户提交的答案数据。
        *   服务端需根据用户答案和预设标准进行批改判断，并返回批改结果、得分、正确答案以及可能的解析或反馈信息。
    *   **课程总结 (文档组件)**: 服务端需提供课程总结部分的文本内容、要点回顾或相关补充材料。
    *   **学习报告 (功能页面)**: 服务端需整合用户在本节课的学习数据，包括各练习的作答情况、得分、整体学习进度、知识点掌握程度评估等，并返回这些数据用于生成报告。
    *   **答疑组件**:
        *   服务端需提供历史问答数据查询功能，允许用户查看与当前课程或知识点相关的已有问答。
        *   服务端需接收用户提交的新问题。
        *   服务端需存储用户问题，并能将问题流转至解答方（AI或人工），之后返回解答内容给用户或更新问题状态。

4.  **Mermaid 流程图描述**

    ```mermaid
    graph TD
        subgraph 单节课内容流程与组件分配
            A[课程开场页] -- 使用 --> C_FP1(功能页面)
            A --> B[课程引入]
            B -- 使用 --> C_DC1(文档组件)
            B --> C[知识点1]
            C -- 使用 --> C_DC2(文档组件)
            C --> D[知识点2]
            D -- 使用 --> C_DC3(文档组件)
            D --> E[练习1]
            E -- 使用 --> C_EC1(练习组件)
            E --> F[知识点3]
            F -- 使用 --> C_DC4(文档组件)
            F --> G[知识点4]
            G -- 使用 --> C_DC5(文档组件)
            G --> H[练习2]
            H -- 使用 --> C_EC2(练习组件)
            H --> I[课程总结]
            I -- 使用 --> C_DC6(文档组件)
            I --> J[学习报告]
            J -- 使用 --> C_FP2(功能页面)
        end

        subgraph 辅助支持功能
            QA[答疑组件] -- 全程可用 --> A
            QA -- 全程可用 --> B
            QA -- 全程可用 --> C
            QA -- 全程可用 --> D
            QA -- 全程可用 --> E
            QA -- 全程可用 --> F
            QA -- 全程可用 --> G
            QA -- 全程可用 --> H
            QA -- 全程可用 --> I
            QA -- 全程可用 --> J
        end
    ```

    *(注: Mermaid 对于“全程可用”的视觉表达不如原图直接，此处通过多条连接示意其广泛适用性，或在文字说明中强调其普适性。实际图片中“答疑组件”的横向排列更好地表达了此意图。)*

【============== 图片解析 END ==============】



# 四、需求概览

| 模块 | 需求描述 | 优先级 |
| --- | --- | --- |
| 练习组件 | 增加进入转场和题目间转场和反馈。 | P0 |



# 五、详细产品方案

## 练习组件

嗯 我现在想着是 进入练习时 老师在但是内容 侧滑进来有个题的转场。

然后老师说完 题和老师滑走，然后进入题目。

| 模块/功能 | 需求描述 | 原型图 |
| --- | --- | --- |
| 进入练习 | 进入练习提示：进入练习转场： | ![in_table_image_E3xVbhOCBoczFFxa0Vic8qyFnLb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746845254353.png) |
| 练习环节 | 页面框架 | ![in_table_image_K4wFb9b0Qof0G2xH6FvcBEGFnjx](https://static.test.xiaoluxue.cn/demo41/prd_images/1746845254833.png) |
| 题目间转场 |  | ![in_table_image_BosmbvrKeo8580x9RqycMEZ7nYb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746845255737.png) |

- 练习组件最顶部，增加“下拉进入练习” tip ，预告即将进入练习环节。
- 首次进入练习（所有题目未作答时）：
- 时机：用户完成课程后，进入练习组件时
- 转场：
- 转场动效，翻页。
- IP动效 + 文案。
- 文案：开始练习 + 课程名称。
- 展示时间： 2 s。
- 再次进入练习
- 时机：用户退出课程后再次进入课程时进度在练习组件时，
- 转场：
- IP动效 + 文案。
- 文案：继续练习 + 课程名称。
- 展示时间： 2 s。
![in_table_image_ZBTHbAACio3xtIxZgtQc7EpVnGh]

###### 图片分析
【============== 图片解析 BEGIN ==============】

本文档中的图片是一张 **用户界面（UI）截图**，展示了 AI 课程中练习组件的一个学习内容页面。该页面旨在向学习者呈现关于“等差数列的表示方法”的教学内容，并提供相关的导航和练习入口。

1.  **关键元素、组成部分及层级化结构阐述**

    该界面作为一个在线学习内容的载体，其核心价值在于结构化地呈现教学信息，并引导用户进行学习和练习。

    *   **顶层：整体学习界面**
        *   **核心作用与价值：** 提供一个集视频、图文、导航和交互于一体的学习环境。
        *   **组成部分：**
            *   **A. 视频播放区 (Video Playback Area):**
                *   **位置：** 界面主体内容区域的顶部。
                *   **元素：** 播放/暂停控制（根据上下文推断，图中未直接显示按钮但有进度条）、视频进度条、当前播放时间（00:32）、视频总时长（02:44）、全屏按钮（根据上下文推断，标准视频播放器均有此功能）。
                *   **核心作用与价值：** 以视频形式动态讲授核心知识点，适应不同学习者的偏好，提高学习效率和趣味性。
            *   **B. 内容导航区 (Content Navigation Area):**
                *   **位置：** 界面左侧。
                *   **元素：** 列表形式的课程主题/章节链接，包括“课程引入”、“复数的分类”、“复数相等的条件”。当前内容“等差数列的表示方法”是主标题，未在导航区作为可选链接出现，表明它是当前正在学习的主题。
                *   **核心作用与价值：** 提供课程结构的概览，允许学习者在不同知识点或章节间快速跳转和回顾，增强学习的灵活性和自主性。
            *   **C. 主要内容展示区 (Main Content Display Area):**
                *   **位置：** 界面主体，视频播放区下方。
                *   **元素：**
                    *   当前主题标题：“等差数列的表示方法”。
                    *   文本说明区域：包含对“表示方法选择”（如“数列有多种表示方法...递推公式或通项公式表示”）和“递推公式”（如“所以递推公式为 an = an-1 + d (n≥2) 也可写成 an - an-1 = d (n≥2)”——实际图中为Qn，根据OCR和上下文推断应为an）的详细文字和公式解释。
                    *   UI提示：“标题字数多折行 展示效果如图” - 这是对前端展示的说明，非学习内容本身。
                *   **核心作用与价值：** 详细呈现当前知识点的图文内容，包括定义、解释、公式等，是学习者获取信息的主要来源。
            *   **D. 交互操作区 (Interaction Area):**
                *   **位置：** 主要内容展示区下方。
                *   **元素：** “随堂练习2”按钮、“完成”按钮。
                *   **核心作用与价值：** “随堂练习”按钮用于引导学习者进行即时练习，检验学习效果；“完成”按钮用于标记当前学习单元的结束，可能触发学习进度更新。

    **元素间关联：**
    *   用户通过**内容导航区(B)**选择或进入某个主题（如当前主题“等差数列的表示方法”）。
    *   该主题的核心教学内容通过**视频播放区(A)**和**主要内容展示区(C)**联合呈现。
    *   学习完当前内容后，用户可以通过**交互操作区(D)**的“随堂练习2”按钮进行练习，或通过“完成”按钮结束当前单元的学习。

2.  **功能模块拆解及简要功能概述**

    *   **视频播放模块:**
        *   概述：负责加载和播放教学视频，提供播放控制（如播放/暂停、进度调整、全屏）。
    *   **内容导航模块:**
        *   概述：展示课程的章节或主题列表，支持用户点击跳转到不同内容的学习页面。
    *   **图文内容展示模块:**
        *   概述：负责渲染和展示当前学习主题的文本、数学公式、图片（若有）等静态内容。支持长标题自动换行。
    *   **随堂练习模块 (入口):**
        *   概述：提供一个入口（按钮），用户点击后可以进入与当前学习内容相关的练习环节。
    *   **学习完成状态模块:**
        *   概述：提供一个操作（按钮），用户点击后标记当前学习单元已完成，系统可据此记录学习进度。

3.  **服务端需提供的功能和返回的数据内容描述**

    服务端需要为该练习组件的当前页面提供以下数据和服务：
    *   **视频数据：** 提供与当前学习主题“等差数列的表示方法”相关的视频资源地址或流媒体信息，以及视频的总时长。
    *   **内容导航数据：** 提供左侧导航栏的条目列表，每个条目应包含其显示的文本（如“课程引入”、“复数的分类”、“复数相等的条件”）以及对应的导航目标标识。
    *   **主题内容数据：**
        *   当前页面的主标题信息（如“等差数列的表示方法”）。
        *   详细的图文内容，包括所有段落文本（例如，“数列有多种表示方法,但用图像或表格表示等差数列较复杂,所以考虑用递推公式或通项公式表示。”）和子标题（例如，“表示方法选择”，“递推公式”）。
        *   精确的数学公式及其相关条件（例如，递推公式的具体表达式及其适用条件 "n≥2"）。
    *   **练习入口数据：** 提供“随堂练习”按钮的显示文本（如“随堂练习2”）以及该练习的唯一标识或跳转链接。
    *   **学习状态处理：** 具备接收和处理用户点击“完成”按钮后发送的学习单元完成状态更新请求的能力。

4.  **Mermaid 图表描述**

    该图片为用户界面（UI）截图，更侧重于展示页面布局和元素，而非一个标准流程或结构图。若要描述用户在该界面上的可能交互路径，可以使用 Mermaid Flowchart 如下：

    ```mermaid
    graph TD
        A[用户进入“等差数列的表示方法”学习页面] --> B{浏览页面内容};
        B --> B1[观看教学视频];
        B --> B2[阅读文字和公式解释];
        B --> C{左侧导航栏};
        C -- 点击 --> C1[跳转至“课程引入”];
        C -- 点击 --> C2[跳转至“复数的分类”];
        C -- 点击 --> C3[跳转至“复数相等的条件”];
        B --> D[点击“随堂练习2”按钮进入练习];
        B --> E[点击“完成”按钮标记学习结束];
    ```

【============== 图片解析 END ==============】



![in_table_image_E3xVbhOCBoczFFxa0Vic8qyFnLb]

###### 图片分析
【============== 图片解析 BEGIN ==============】

基于提供的PRD文档中的图片及其OCR解析文本，现进行如下分析：

1.  **图片类型、关键元素、结构与核心价值解析**

    *   **图片类型**：该图片为 **用户界面（UI）截图**，展示了在线练习或答题场景的一个具体页面。
    *   **关键元素与组成部分**：
        *   **导航/状态区**：位于界面顶部。
            *   `退出学习`：操作按钮，允许用户中止当前练习。
            *   `用时 00:00`：状态显示，实时展示用户在当前练习中所花费的时间。
        *   **题目信息区**：位于导航区下方。
            *   `单选`：题目类型指示，明确告知用户此题为单项选择题。
            *   `空间几何~`：题目内容/题干，展示具体的问题。
        *   **选项区**：题目内容下方，列出可供选择的答案。
            *   `A 打点计时器`：选项A及其内容。
            *   `打点计时器`：根据上下文和单选题特性，此为选项B及其内容。
            *   `C 打点计时器`：选项C及其内容。
        *   **操作功能区**：位于界面底部。
            *   `不确定`：功能按钮，允许用户标记当前题目为不确定，可能用于后续回顾。
            *   `提交`：核心操作按钮，用户选择答案后点击提交。
            *   `勾画`：辅助功能按钮，可能用于在题目或选项上进行标记或高亮（具体交互方式未在此静态图中展示）。
    *   **层级化结构与元素间关联**：
        1.  **顶层**：整体练习界面。
        2.  **第一层（导航与状态）**：包含全局控制（退出）和状态监控（用时）。“退出学习”影响整个练习会话，“用时”则动态反映会话进程。
        3.  **第二层（题目呈现）**：包含题目类型和题干。“单选”规定了用户在选项区的交互方式（选择唯一答案），“空间几何~”是用户需要解答的核心内容。
        4.  **第三层（答案选择）**：包含具体的选项A、B、C。用户需根据题干从这些选项中选择一个。
        5.  **第四层（操作与提交）**：包含对当前题目或整体练习的操作。“不确定”和“勾画”是针对当前题目的辅助操作，“提交”则是完成当前题目并可能流转到下一题或结果页的核心动作。
    *   **核心作用与价值（结合教育领域上下文）**：
        此界面是“AI课中 - 练习组件1.0”的核心交互环节。其核心作用是为学习者提供一个清晰、便捷的在线答题环境，用于巩固知识、检验学习效果。
        *   **对于用户**：能够直观地看到题目、进行选择、管理答题状态（标记不确定）、提交答案并了解用时情况。
        *   **对于产品**：是实现练习功能、收集用户作答数据、驱动后续智能反馈或学习路径调整（AI课的潜在价值）的关键触点。通过提供“勾画”等辅助工具，可以提升用户练习体验。

2.  **功能模块拆解与概述**

    *   **导航与会话管理模块**：
        *   `退出学习`：提供中断当前练习会话并退出的功能。
        *   `用时显示`：实时展示当前练习已进行的时长。
    *   **题目展示模块**：
        *   `题型展示`：明确告知用户当前题目的类型（如：单选）。
        *   `题干内容呈现`：完整显示题目问题。
    *   **答案选项模块**：
        *   `选项罗列`：展示所有可选答案项。
        *   `答案选择交互`：允许用户点选其中一个答案（根据“单选”类型）。
    *   **答题辅助与提交模块**：
        *   `标记不确定`：允许用户对当前题目进行“不确定”标记，方便后续查找或重点复习。
        *   `提交答案`：用户确认选择后，提交当前题目的答案进行处理（如判卷、计分等）。
        *   `勾画工具`：提供在题目区域进行勾画、高亮等辅助理解或标记的功能。

3.  **服务端需提供的功能与数据内容**

    服务端需要提供的支持和数据，以确保此练习组件功能的实现，具体描述如下：

    *   **服务端需提供的功能**：
        *   题目数据管理与下发功能：能够根据用户当前的练习进度或请求，准确提供下一道或指定题目的完整信息。
        *   用户答题会话管理功能：能够开始、暂停、继续和结束用户的练习会话，并记录会话状态，如开始时间，以便计算用时。
        *   用户答案接收与处理功能：能够接收用户针对特定题目提交的所选答案。
        *   用户行为标记记录功能：能够接收并记录用户对题目进行的“不确定”标记。
        *   （若“勾画”内容需持久化或多端同步）勾画数据存储与读取功能：能够接收、存储用户在题目上的勾画信息，并在需要时提供给客户端展示。
        *   （后续流程）答案校验与反馈功能：在用户提交答案后，能够判断答案的正确性，并准备相应的反馈信息（如正确答案、解析等，虽然未在此图显示，但提交功能暗示了后续处理）。

    *   **服务端需返回的数据内容（为渲染此界面及支撑其功能）**：
        *   当前题目的唯一标识。
        *   当前题目的类型信息（例如，文本指明为“单选”）。
        *   当前题目的题干文本内容。
        *   当前题目的一组选项，每个选项包含其自身的标识（如A, B, C）和选项的文本内容。
        *   （若会话已开始）当前练习会话的已用时长信息，或会话开始的时间戳供客户端计算。
        *   （若题目曾被标记）当前题目是否已被用户标记为“不确定”的状态。
        *   （若“勾画”内容需持久化）与当前题目关联的任何已保存的勾画数据。

4.  **Mermaid 图表描述**

    该图片展示的是一个静态的用户界面（UI Mockup），并非流程图、时序图、类图、ER图、甘特图或饼图。它描绘了一个特定时刻的界面状态和可交互元素，而不是一个动态过程或数据结构关系。因此，使用 Mermaid 的 flowchart, sequenceDiagram, classDiagram, erDiagram, gantt, 或 pieChart 语法来直接描述这张静态图片本身是不适用的。

【============== 图片解析 END ==============】



- 顶部导航栏：
- 退出按钮：逻辑同课程框架。
- 作答计时：复用题型组件。
- 每题独立正计时（格式 00:00，上限 59:59）。
- 学生未作答退出，暂停计时，下次进入后在上次计时基础上继续计时（例一道题先看了 5 分钟没作答，退出后再进来，从 5 分钟开始计时）。
- 作答进度：
- 进度展示：
- 分母：预估学生作答数量。
- 分子：依据推题策略，
- 如果学生答对，进度涨。
- 如果学生答错，但没有相似题，进度涨。
- 如果学生答错，但有相似题需要再练一道，进度不变。
- 连对触发效果：
- 同巩固练习，。
- 答题组件：
- 题型组件：各题型在练习中的展示和交互，见。
- 题目推荐：见课中题目推荐策略，见。
- 勾画组件：同巩固练习，。
- 错题本组件：同巩固练习，。
- 答疑组件：
- 入口：右上角常驻展示「问一问」，题目进入解析状态前屏蔽问一问入口。
- 交互：
- 长按题目内容：选中文字/图片内容，唤起问一问。
- 用户可拖拽高亮条，选择文字。
- 点击问一问，进入答疑组件。
- 点击评论，唤起键盘，用户可输入内容进行发表。
![in_table_image_K4wFb9b0Qof0G2xH6FvcBEGFnjx]

###### 图片分析
【============== 图片解析 BEGIN ==============】

好的，我们来解析一下这张关于“AI课中 - 练习组件 1.0”PRD文档中的图片。

1.  **图片类型、关键元素、层级结构及核心作用价值**

    *   **图片类型**:
        该图片是一张**用户界面（UI）截图**。它展示了用户在完成AI课中练习后，针对单个题目反馈的界面样式和信息。

    *   **关键元素与层级结构**:
        该界面从上到下，可以划分为以下几个主要层级和关键元素：

        1.  **顶部状态栏（非核心业务元素）**: 显示设备时间、日期、电量等信息（按要求，此部分不作核心分析）。
        2.  **练习表现摘要区**:
            *   **连对题数**: "连对5题" - 表明用户当前连续答对的题目数量。
            *   **用时**: "用时01:24" - 表明用户完成此次练习或当前题目所花费的时间。
        3.  **题目详情区**:
            *   **用户选择与正确性标识**: "A (0, π/6 ]" (OCR "pail6" 结合数学上下文推断为 π/6，下同) 被高亮选中，并带有绿色勾号，表示用户选择了A选项且正确。旁边有数值"20.900"。
            *   **题型**: "单选" - 明确指出题目是单项选择题。
            *   **题目来源/上下文**: "(2024春 浙江期中)" - 提供题目的出处或相关背景。
            *   **题干**: "在四面体ABCD中, BCD为等边三角形ADB=π/2 (OCR "pail2" 推断为 π/2), 面角B-A0-C的大小为a,则a的取值范围是" - 题目具体内容。
            *   **选项列表**:
                *   选项 A: "(0, π/6 ]" (用户选择，正确)，关联数据 "20.900"
                *   选项 B (推测，未直接标出B，但为第二个选项): "(0, π/6 ]"，关联数据 "10.89"
                *   选项 C (推测): "(0, π/6 ]"，关联数据 "50.200"
                *   选项 D (推测): "(0, π/6 ]"，关联数据 "18.100"
            *   **正确答案提示**: "正确答案" 文字，指向已选中的A选项。
        4.  **操作功能区**:
            *   **题目解析**: 按钮/链接，用于查看该题的详细解答过程。
            *   **加入错题本**: 按钮/链接，用于将该题目收藏到错题本。
            *   **继续**: 按钮/链接，用于进行下一题或后续练习环节。

    *   **元素间关联**:
        *   "练习表现摘要区"提供了对用户当前练习状态的宏观反馈。
        *   "题目详情区"是核心，它展示了具体的题目信息、用户的作答情况以及正确答案。用户的选择（选项A）与题目内容直接关联，并由"正确答案"标签确认其正确性。各选项后附带的数值（如20.900）是与选项相关的特定数据。
        *   "操作功能区"的各项功能（题目解析、加入错题本、继续）均是基于当前展示的题目，为用户提供学习辅助和流程推进的操作。

    *   **核心作用与价值**:
        这张UI截图的核心作用在于：
        1.  **即时反馈**: 用户答题后能立刻看到自己的表现（连对数、用时）、所选答案是否正确。
        2.  **学习指引**: 清晰展示题目内容、正确答案，并提供"题目解析"入口，帮助用户理解题目和知识点。
        3.  **个性化学习辅助**: "加入错题本"功能支持用户记录疑难点，便于后续复习。
        4.  **流程连续性**: "继续"按钮确保用户可以顺畅地进行下一阶段的学习或练习。
        对于互联网教育产品，该界面是练习环节中至关重要的一环，它直接关系到用户的学习体验和效果评估，通过有效的反馈和辅助工具强化学习闭环。

2.  **功能模块拆解与概述**

    *   **练习表现统计模块**:
        *   概述：展示用户在当前练习会话中的阶段性表现，如连续正确题数和所用时间。
    *   **题目信息展示模块**:
        *   概述：完整呈现当前题目的所有必要信息，包括题型、来源、题干内容以及所有选项的内容。
    *   **用户作答反馈模块**:
        *   概述：明确标示用户的选择，并通过视觉提示（如高亮、勾选）反馈其作答的正确性。
    *   **正确答案显示模块**:
        *   概述：清晰展示当前题目的标准正确答案。
    *   **选项关联数据显示模块**:
        *   概述：显示与每个选项关联的特定数值（如图片中的20.900, 10.89等）。
    *   **题目解析查阅模块**:
        *   概述：提供入口，允许用户查看当前题目的详细解题步骤和分析。
    *   **错题收集模块**:
        *   概述：允许用户将当前题目添加入个人的错题记录中，方便后续针对性复习。
    *   **练习导航模块**:
        *   概述：提供“继续”功能，引导用户进入练习的下一个环节或题目。

3.  **服务端需提供的功能与数据内容**

    服务端需要为该界面提供以下数据内容和支持：

    *   **用户练习表现数据**: 服务端需计算并返回用户当前的连续答对题目数量以及完成本次练习（或特定题目集）所用的总时长。
    *   **题目详细数据**:
        *   题目ID。
        *   题型信息（例如：单选题）。
        *   题目的来源或上下文信息（例如："2024春 浙江期中"）。
        *   题干的完整文本内容。
        *   一个包含所有选项的列表，每个选项对象应包含：
            *   选项的标识符（如A, B, C, D）。
            *   选项的文本内容（例如："(0, π/6 ]"）。
            *   一个布尔值或标记，指明该选项是否为正确答案。
            *   该选项关联的特定数值（例如图片中的 "20.900", "10.89" 等）。
    *   **用户作答数据**: 服务端需记录并返回用户针对当前题目所选择的选项。
    *   **题目解析数据**: 服务端需提供当前题目的详细文字、图片或视频解析内容，当用户请求时返回。
    *   **错题本状态数据**: 服务端需记录并返回当前题目是否已被该用户加入错题本的状态，以便前端正确显示“加入错题本”或“已加入/移出错题本”的按钮状态。
    *   **练习流程控制数据**: 服务端需提供用户点击“继续”后应跳转的下一个题目ID或下一个练习环节的信息。

4.  **Mermaid图表描述**

    该图片为用户界面（UI）截图，展示了练习结果反馈的特定状态，并非Mermaid可直接描述的标准流程图、时序图、类图、ER图、甘特图或饼图。因此，无法直接使用Mermaid语法对此图片本身进行结构化描述。

【============== 图片解析 END ==============】



- 逻辑和展示同巩固练习，。
![in_table_image_GFWobx4zHo13YlxhwhlccN3GnRe]

###### 图片分析
【============== 图片解析 BEGIN ==============】

根据您提供的PRD文档中的图片及其OCR解析文本，我将进行如下分析：

1.  **图片类型、关键元素、层级结构及核心作用与价值**

    *   **图片类型**：该图片为**用户界面（UI）截图**，展示的是在线教育产品中，学生完成练习后的结果反馈与总结页面。

    *   **关键元素与组成部分**：
        *   **练习基本信息**：
            *   练习耗时：“用时 01:24”。
            *   练习标签/来源：“单选 (2024春 浙江期中)”。
        *   **作答表现可视化**：
            *   一个包含四个数据柱的图表，可能表示与特定标准（标签为 `(0, pail6 ]`）相关的某种分布或比较，Y轴数据点分别为 20.906、10.890、50.200、18.100。
        *   **AI评价/反馈**：
            *   文字评价：“同学巩固得很牢固”。
        *   **后续操作功能**：
            *   按钮：“题目解析”。
            *   按钮：“加入错题本”。
            *   按钮：“继续”。

    *   **层级化结构阐述元素间关联**：
        1.  **顶层：练习结果反馈页面**
            *   **区域一：练习概要**
                *   耗时信息
                *   练习元数据（类型、来源）
            *   **区域二：表现详情与评估**
                *   数据可视化图表（展示具体的表现数据）
                *   AI生成的定性评价（基于表现数据）
            *   **区域三：操作引导**
                *   功能按钮组（题目解析、加入错题本、继续）

    *   **核心作用与价值**：
        *   **即时反馈**：向学生清晰展示其在本次练习中的表现，包括耗时和具体的数据化表现（通过图表）。
        *   **学习诊断与引导**：通过AI评价（“同学巩固得很牢固”）给出学习状态的判断，并通过“题目解析”和“加入错题本”功能，引导学生进行针对性复习和巩固薄弱环节。
        *   **学习流转**：“继续”按钮确保学习流程的顺畅进行，学生可以据此进入下一学习环节。
        *   **个性化学习支持**：图表数据和AI评价为后续可能的个性化学习路径推荐或AI辅导提供数据基础。

2.  **各组成部分包含的功能模块及简要功能概述**

    *   **练习信息展示模块**：
        *   **功能概述**：显示本次练习的基本信息，包括学生作答所用时长和练习的属性（如题型、所属考试/章节等）。
    *   **作答表现可视化模块**：
        *   **功能概述**：以图表形式（此处为柱状图）直观展示学生在某些维度上的表现数值。
    *   **AI智能评价模块**：
        *   **功能概述**：基于学生的作答数据，系统自动生成一句总结性的评价，反馈学生对知识点的掌握程度。
    *   **题目解析模块**：
        *   **功能概述**：允许用户查看当前练习中题目的详细解答过程和知识点说明。
    *   **错题管理模块**：
        *   **功能概述**：允许用户将练习中的错题或有价值的题目手动添加到个人错题本中，方便后续复习。
    *   **流程导航模块**：
        *   **功能概述**：提供“继续”操作，引导用户进入下一个学习步骤或返回到主流程。

3.  **服务端需要提供的功能和返回的数据内容**

    服务端需要提供以下数据内容以支持该页面的展示和功能：
    *   用户本次练习所花费的总时长。
    *   本次练习的元数据，包括练习的类型（如“单选”）和练习的来源或名称（如“2024春 浙江期中”）。
    *   用于生成表现可视化图表的数据，这应包括每个数据柱的标签或分类名称（如图中所示的 `(0, pail6 ]`，如果每条柱代表不同分类，则需提供各自分类名）以及对应的数值（如图中的20.906, 10.890, 50.200, 18.100等）。
    *   基于用户作答情况生成的AI评价文本内容（如“同学巩固得很牢固”）。
    *   与“题目解析”功能相关的标识或链接，以便前端能够引导用户查看解析内容。
    *   与“加入错题本”功能相关的状态信息（例如，题目是否已在错题本中，或是否有题目符合加入错题本的条件）及执行操作所需的题目ID等。
    *   “继续”按钮的目标导航信息，指示用户下一步应跳转至何处。

4.  **Mermaid 图表描述**

    该图片本身为用户界面截图，并非流程图、时序图等标准图表类型。但其内部包含一个柱状图，如果尝试用 Mermaid 的 `pieChart` 来示意性地表示这组数据的分布（假设它们可以被视为整体的几个部分），可以如下描述（注意：柱状图和饼图表达含义不同，此处仅为按要求使用 Mermaid 格式示意数据）：

    ```mermaid
    pie title Performance Data Distribution (Illustrative)
        "Category A (Value: 20.906)" : 20.906
        "Category B (Value: 10.890)" : 10.890
        "Category C (Value: 50.200)" : 50.200
        "Category D (Value: 18.100)" : 18.100
    ```
    *   **注意**：OCR文本显示所有数据柱的横轴标签均为 `(0, pail6 ]`。在饼图中，为区分数据，使用了Category A-D作为示例标签。实际应用中，这些数据的确切含义和分类需要根据业务逻辑确定。如果 `(0, pail6 ]` 是指一个统一的类别下的不同指标，则饼图可能不适用，柱状图更合适。由于Mermaid原生不支持复杂柱状图，此处按要求选用`pieChart`做数据量的示意。

【============== 图片解析 END ==============】



![in_table_image_BosmbvrKeo8580x9RqycMEZ7nYb]

###### 图片分析
【============== 图片解析 BEGIN ==============】

好的，我们来一起分析这张来自“AI 课中 - 练习组件 1.0”需求文档中的图片。

1.  **图片类型、关键元素、组成部分及核心作用与价值**

    *   **图片类型**:
        这张图片是一张**用户界面（UI）截图**，展示了学生在完成一道练习题后的结果反馈与解析界面。

    *   **关键元素与组成部分 (层级化结构)**:
        1.  **顶部状态栏信息** (非核心业务，但为UI标准组成)
            *   日期显示（Mon Jun 3）
        2.  **练习表现概要区**
            *   连对题数（连对5题）
            *   用时（用时01:24）
            *   得分/指标（20.900）
        3.  **题目信息区**
            *   题型（单选）
            *   题目来源/标签（(2024春 浙江期中)）
            *   题目内容（(0,pails 面角B-A0-C的大小为a。则a的取值范围是）
        4.  **题目解析区**
            *   标题（题目解析）
            *   解析内容占位（解析题目解析题目解析题目解析题目解析）
        5.  **交互操作区**
            *   继续按钮（继续）
        6.  **激励/成就展示区**
            *   成就徽章/称号（实力派666）
            *   成就描述（连对5题!）

    *   **元素间关联**:
        *   **练习表现概要区**的数据（连对、用时、得分）是用户完成**题目信息区**所示题目（或一系列题目）后的综合表现。
        *   **题目解析区**提供了针对**题目信息区**中题目的详细解答。
        *   用户查看完题目、表现和解析后，可以通过**交互操作区**的“继续”按钮进行下一步操作（如继续下一题或返回列表）。
        *   **激励/成就展示区**是基于用户在练习中的表现（如**练习表现概要区**的“连对5题”）给予的积极反馈。

    *   **核心作用与价值**:
        *   **即时反馈**: 及时向学生展示作答后的表现，包括正确率（通过连对题数间接体现）、答题效率（用时）和综合评分。
        *   **知识巩固与学习**: 提供题目来源和详细解析，帮助学生理解错题原因或巩固已掌握的知识点。
        *   **学习激励**: 通过称号和成就描述等方式，对学生的良好表现给予肯定，提升学习积极性和持续参与度。
        *   **流程引导**: “继续”按钮引导用户进行后续学习环节，保证学习流程的顺畅性。
        对于互联网教育产品，此界面是练习反馈闭环中的关键一环，承载了评估、解析、激励和导航的功能，直接影响用户对练习效果的感知和学习体验。

2.  **功能模块拆解及简要概述**

    *   **练习表现统计模块**:
        *   概述：展示用户本次或近期练习的关键表现数据。包括当前会话日期、连续答对题目数量、答题所用时长、以及一个综合性得分或指标。
    *   **题目内容展示模块**:
        *   概述：清晰展示当前题目的核心信息。包括题目的类型（如单选）、题目的来源或分类标签、以及题目的具体文本内容。
    *   **答案解析模块**:
        *   概述：提供当前题目的详细解答和分析过程。用于帮助学生理解解题思路和相关知识点。
    *   **用户交互模块**:
        *   概述：提供用户进行下一步操作的入口。当前可见的是“继续”按钮，用于进入下一个练习或环节。
    *   **学习激励模块**:
        *   概述：根据用户的练习表现给予正向反馈。包括特定成就的称号或徽章，以及对该成就的文字描述。

3.  **服务端需提供的功能和返回的数据内容**

    服务端需要提供以下数据内容，以支持该界面的完整展示：
    *   练习会话的日期信息。
    *   用户连续答对题目的数量。
    *   用户完成当前题目或一组题目所花费的时间。
    *   一个表示用户表现的数值，可能是得分或其他形式的度量指标。
    *   当前题目的类型信息。
    *   当前题目的来源信息或相关标签信息。
    *   当前题目的完整内容文本。
    *   针对当前题目的详细文字解析内容。
    *   根据用户表现触发的激励性文字或标识，例如特定的称号。
    *   与激励称号相对应的成就描述文字，例如连续答对题目的数量的再次强调。

4.  **Mermaid 图表**
    该图片为UI界面截图，不适用流程图、时序图、类图、ER图、甘特图或饼图等Mermaid语法进行描述。

【============== 图片解析 END ==============】





# 七、数据需求

## 埋点需求

| 模块 | 页面 | 动作 | 埋点 | 备注 |
| --- | --- | --- | --- | --- |
| 练习组件 | 练习页面 | 加载题目 | exercise_question_load | 每道题加载完成 |
| 练习组件 | 练习页面 | 提交答案 | exercise_answer_submit | 记录作答时间、答案内容、是否正确 |
| 练习组件 | 练习页面 | 点击退出按钮 | exercise_exit_confirm_click | 退出时是否保存进度 |
| 练习组件 | 练习页面 | 勾画操作 | drawing_action | 包括勾画/橡皮擦/清除/撤销/恢复 |
| 练习组件 | 练习页面 | 错题收藏/取消 | mistakebook_toggle_click | 收藏或取消收藏错题动作 |
| 练习组件 | 作答后反馈页 | 展示作答反馈 | answer_feedback_show | 正确/错误/连胜类型 |
| 练习组件 | 难度变化提示页 | 展示难度调整提示 | difficulty_change_feedback_show | 上升/下降类型 |
| 练习组件 | 作答过程中 | 展示不认真作答提示 | inattentive_feedback_show | 作答过快/异常行为触发 |



## 数据存储需求

| 模块 | 数据项 | 说明 |
| --- | --- | --- |
| 练习组件 | 学生选择的答题选项 | 包括选择内容、是否确定提交 |
| 练习组件 | 每题作答时长 | 单题独立正计时，精确到秒 |
| 练习组件 | 每题作答正确/错误标识 | 用于后续练习效果统计 |
| 练习组件 | 连续正确次数 | 记录连胜链条 |
| 练习组件 | 是否触发难度上升/下降 | 标记作答后难度变化情况 |



