# ClickHouse 数据库设计规范与约束

本文件详细定义了在使用 ClickHouse 数据库时必须遵循的设计规范和技术约束。

## 1. 通用数据库设计原则 (同样适用于指导 ClickHouse 数据建模)
  * **单一职责原则 (SRP - Single Responsibility Principle)**：每个表（或 ClickHouse 中的数据实体）应尽可能表示一个明确的业务实体或分析主题。虽然 ClickHouse 常用于宽表，但逻辑上的清晰划分依然重要。
  * **开闭原则 (OCP - Open/Closed Principle)**：表结构应能灵活扩展。在 ClickHouse 中，这可能意味着方便地添加新列，或者通过物化视图等方式支持新的聚合维度，而尽量不频繁改动核心表的结构。
  * **里氏替换原则 (LSP - Liskov Substitution Principle)**：在 ClickHouse 的上下文中，此原则应用较少，但如果存在某种形式的“继承”或表间数据转换，应确保转换后数据的兼容性。
  * **数据隔离原则 (DSP - Data Segregation Principle)**：严格按照业务逻辑或分析需求对数据进行组织。不同的数据源或分析主题可能适合存储在不同的表中，或通过不同的物化视图进行处理。
  * **迪米特法则 (LoD - Law of Demeter) / 最少知识原则 (Least Knowledge Principle)**：在 ClickHouse 中，通常表间关联较少（倾向于反规范化），但如果涉及通过 `JOIN` 操作（尤其是在字典表或小表关联时），应确保关联的必要性和简洁性。
  * **高内聚低耦合 (High Cohesion and Low Coupling)**：ClickHouse 的表设计应使其内部数据高度相关于其分析主题。与其他数据实体的依赖（耦合）应尽可能通过清晰的关联键或在查询层面处理。
  * **KISS (Keep It Simple, Stupid)**：数据库表设计（包括列定义、排序键、分区键等）应保持简单明了，易于理解和维护。
  * **YAGNI (You Ain't Gonna Need It)**：避免过度设计，不预先设计当前不需要的复杂数据结构、物化视图或索引。仅在需求明确时进行添加和优化。
  * **关注点分离 (SoC - Separation of Concerns)**：将原始数据层、中间聚合层（可能通过物化视图实现）、最终报表层的数据在逻辑上或物理上进行分离。
  * **DRY (Don't Repeat Yourself)**：在 ClickHouse 中，为了查询性能有时会进行适度的数据冗余（反规范化）。但应避免无控制的、不一致的数据重复。物化视图可以帮助管理和同步这种冗余。

## 2. 应避免的数据库设计反模式 (ClickHouse 视角下的考量)
  * **数据孤岛 (Data Silos)**：虽然 ClickHouse 可能作为数据仓库或数据集市，但其内部数据也应避免不必要的割裂，确保数据可以有效整合分析。
  * **巨石表 (God Table) 的极端情况**：ClickHouse 适合宽表，但如果一个表包含了过多完全不相关的分析维度和指标，可能导致管理困难和部分查询效率低下。应在“宽”和“职责清晰”之间找到平衡。
  * **过度规范化 (Over Normalization)**：ClickHouse 的核心优势在于对大数据量的快速扫描和聚合，过度规范化导致大量 JOIN 操作通常会严重损害性能。设计时应倾向于反规范化。
  * **重复数据 (Data Duplication) 的失控**：如前述，适度冗余可接受，但无规划的重复会导致存储浪费和数据不一致风险。
  * **过度依赖复杂数据类型而无相应优化**: ClickHouse 支持 `Array`, `Tuple`, `Nested`, `JSON` (`JSON`对象通常以 `String` 存储，需应用层解析或使用特定函数) 等复杂类型。使用这些类型时，需考虑其对查询性能、索引（如果适用）、聚合操作的影响。
  * **冗余或无效的跳数索引 (Redundant or Ineffective Skip Indexes)**：过多的跳数索引会增加写入开销，而设计不当的跳数索引（如粒度不合适、类型选择不当）可能对查询性能提升有限。
  * **不合理的排序键 (Order Key)**：排序键是 `MergeTree` 家族引擎性能的核心。选择不当（如基数过高、与主要查询模式不匹配）会严重影响性能。
  * **不合理的分区策略 (Partitioning Strategy)**：分区过细或过粗，或者分区键与查询过滤条件不匹配，都会导致分区裁剪失效，影响查询和维护效率。
  * **滥用轻量级删除 (Lightweight Deletes) 而不理解其影响**: 轻量级删除（`ALTER TABLE ... DELETE WHERE ...`）在 `MergeTree` 表上是非阻塞的，但会产生额外的 `_row_exists` 列和墓碑标记，可能影响后续查询性能和存储，直到 parts 被合并。

## 3. ClickHouse 特定设计约束

### 3.1 命名规范
  * **数据库 (Database)**: 以`db_`开头，使用小写字母、数字和下划线 (`_`) 组成。推荐使用项目或业务模块的名称(例如： `db_order_logs`, `db_user_behavior_logs`)。
  * **表 (Table)**: 以`tbl_`开头，使用小写字母、数字和下划线 (`_`) 组成，清晰表达表的业务含义。推荐使用业务名词的复数形式 (例如 `tbl_user_actions`, `tbl_order_details`)。
  * **字段 (Column)**: 使用小写字母、数字和下划线 (`_`)。
  * **视图 (View / Materialized View)**: 使用小写字母、数字和下划线 (`_`)。普通视图建议加 `v_` 前缀 (例如 `v_user_summary`)，物化视图建议加 `mv_` 前缀 (例如 `mv_daily_sales`)。
  * **避免使用 ClickHouse 的关键字**作为名称。

### 3.2 数据类型选择
  * **优先选择 ClickHouse 原生且高效的数据类型。**
  * **整数**: 根据数值范围选择 `Int8` 至 `Int256` 或对应的无符号类型 `UInt8` 至 `UInt256`。**推荐：除非有明确的小数值范围需求，否则优先考虑使用 `Int64` 或 `UInt64`**，以便于后端代码 (如 Go) 的类型处理。
  * **浮点数**: `Float32`, `Float64`。推荐使用 `Float64` 以保证足够的精度。
  * **定点数**: `Decimal(P, S)`，用于需要精确小数计算的场景 (例如金融数据)。`P` 是总位数 (1-76)，`S` 是小数位数 (0-P)。
  * **字符串**: `String` (动态长度字符串，**常用且推荐**)。`FixedString(N)` (固定长度字符串，仅在长度绝对固定且对性能有极致要求时考虑)。
  * **日期与时间**:
      * `Date`: 存储日期，不含时间，精度为天。
      * `Date32`: 存储日期，范围更广（1900-2299）。
      * `DateTime`: 存储日期和时间，精度为秒 (**常用且推荐用于存储事件发生时间**)。
      * `DateTime64(P[, timezone])`: 存储日期和时间，带有亚秒级精度 (P为小数位数，通常0-9)，可选时区。当需要高精度时间戳时使用。
  * **枚举**: `Enum8('key1' = 1, 'key2' = 2)`, `Enum16(...)`。用于存储预定义集合中的值，可节省存储空间，并提高查询效率（内部存储为整数）。
  * **UUID**: `UUID` 标准类型，推荐用于全局唯一标识符。
  * **布尔型**: ClickHouse 没有独立的布尔类型。**统一使用 `UInt8` 类型，约定 `1` 代表 `true`，`0` 代表 `false`。**
  * **数组**: `Array(T)`，用于存储同一类型 `T` 的数组。
  * **Nullable**: `Nullable(T)`，允许字段存储 `NULL` 值。按需使用，非 `Nullable` 字段通常具有更好的性能和更低的存储开销。
  * **LowCardinality**: `LowCardinality(T)`。对低基数（不同值的数量远小于总行数）的列进行字典编码优化。**强烈推荐用于基数较低的字符串或枚举字段 (例如国家、城市、状态等)，可显著提升查询性能和数据压缩率。**

### 3.3 表引擎选择 (至关重要)
  * **选择合适的表引擎是 ClickHouse 性能的关键。**
  * **首选 `MergeTree` 家族引擎**：这是 ClickHouse 发挥高性能的基础。
      * `MergeTree`: **最常用且推荐的基础引擎**。支持主键索引、数据分区、数据副本和数据采样。
      * `ReplacingMergeTree[(ver)]`: `MergeTree` 的变种，在合并时根据排序键去除重复行。可选 `ver` 参数指定版本列，保留最新（或指定）版本。适用于需要去重或更新语义的场景。
      * `SummingMergeTree([columns])`: `MergeTree` 的变种，在合并时对排序键相同的行的数值类型列进行汇总（求和）。可选参数指定需要汇总的列，未指定的数值列行为未定义。适用于预聚合计数或求和场景。
      * `AggregatingMergeTree`: `MergeTree` 的变种，存储预聚合函数的中间状态 (`AggregateFunction` 类型)，在查询时使用 `-Merge` 组合器得到最终结果。适用于更灵活的预聚合，如计算平均值、分位数等。
      * `CollapsingMergeTree(sign)`: `MergeTree` 的变种，基于 `sign` 列（通常 `Int8` 类型，1 表示插入，-1 表示删除/取消）在合并时异步“折叠”删除具有相同排序键的行对。适用于需要快速插入和后台逻辑删除的场景。
      * `VersionedCollapsingMergeTree(sign, version)`: 结合了 `ReplacingMergeTree` 和 `CollapsingMergeTree` 的特性，根据版本列进行折叠。
      * `GraphiteMergeTree`: 用于存储 Graphite 监控数据的专用引擎，会自动进行数据汇总和保留。
  * **日志引擎 (Log Family)**:
      * `TinyLog`, `StripeLog`, `Log`: 结构简单，不支持索引，并发写入可能阻塞或导致数据不一致（`TinyLog`不支持并发）。适用于一次性写入的小批量数据、临时表或原型验证。**生产环境核心业务表应避免使用。**
  * **集成引擎 (Integration Engines)**:
      * `Kafka`: 直接从 Kafka topic 消费数据。
      * `JDBC`, `ODBC`: 连接外部 JDBC/ODBC 数据源。
      * `HDFS`, `S3`: 直接读写 HDFS 或 S3 上的文件。
      * `ExternalData`: 允许在查询时从用户提供的可执行程序加载数据。
      * 设计这类表时需关注连接参数、数据格式、安全性（如S3的ak/sk）以及可能的性能瓶颈。
  * **特殊引擎 (Special Engines)**:
      * `Distributed`: **核心引擎之一，用于实现分布式查询和写入。** 本身不存储数据，而是将查询路由到集群中各分片上的本地表（通常是 `MergeTree` 家族）。设计时需指定集群名称、底层数据库和表名，以及分片键 (`sharding_key`)。
      * `MaterializedView`: 用于创建物化视图，将数据从源表实时或异步地转换并存储到目标表。
      * `Memory`: 数据仅存于内存，服务重启后丢失。用于测试或需要极高速度的临时计算且数据量不大的场景。
      * `Buffer`: 为目标表提供写入缓冲区，将写入数据缓存于内存中，达到一定条件后批量刷入目标表，以提高写入吞吐量，但可能因服务异常重启丢失缓冲区内的数据。
      * `Dictionary`: 用于创建外部字典，可以将外部数据源（如文本文件、HTTP服务、其他数据库表）加载到内存中作为键值对使用，常用于数据补全或JOIN优化。
      * `Null`: 写入数据时直接丢弃，读取时返回空。可用于测试写入性能或作为 `Distributed` 表的底层表进行数据路由。

### 3.4 索引与排序键 (性能核心)
  * **排序键 (`ORDER BY tuple_of_columns`) 是 `MergeTree` 家族引擎最重要的性能优化手段，必须在 `CREATE TABLE` 语句中显式指定。**
      * 选择查询中最常用于 `WHERE` 条件过滤（尤其是范围查询）、`GROUP BY` 或 `ORDER BY` 的列作为排序键，通常选择 2-4 个列。
      * 排序键的基数不宜过高，但也不能过低以至于无法有效筛选数据。排序键定义了数据在磁盘上数据块(parts)内部的物理存储顺序。
      * 排序键的列顺序非常重要，应将过滤选择性最高（即能排除最多数据）的列放在前面。
  * **主键 (`PRIMARY KEY tuple_of_columns`)** 默认情况下与 `ORDER BY` 相同。可以显式指定 `PRIMARY KEY`，但它必须是 `ORDER BY` 定义的列的前缀。主键的主要作用是加速基于主键的查询（利用稀疏索引），并影响数据在数据块中的组织和合并。ClickHouse的主键是稀疏索引，每个granule（默认8192行）的第一个主键值被记录。
  * **跳数索引 (`INDEX index_name expression TYPE index_type GRANULARITY N`)**: 可选的数据跳过索引，用于加速非主键列的查询，通过跳过不包含目标数据的数据块(granules)来工作。
      * `expression` 是索引作用的列或表达式。
      * `index_type` 包括：
          * `minmax`: 记录数据块中表达式的最大最小值。适用于数值、日期等可以比较大小的类型。
          * `set(max_rows)`: 记录数据块中表达式的不重复值（不超过 `max_rows` 个）。适合不同值数量较少的低基数列。
          * `ngrambf_v1(n, size_of_bloom_filter_in_bytes, number_of_hash_functions, seed)`: 基于n-gram的布隆过滤器，适合文本的模糊匹配（如 `LIKE '%substring%'`）。
          * `tokenbf_v1(size_of_bloom_filter_in_bytes, number_of_hash_functions, seed)`: 基于token的布隆过滤器，适合文本中特定词语的匹配（如 `hasToken()`, `multiSearchAny()`）。
          * `bloom_filter([false_positive_rate])`: 标准布隆过滤器，适合等值查询 (`=`, `IN`, `has()`)。
      * `GRANULARITY N` 控制索引的粒度，表示每 N 个主键标记（granule的主键标记）创建一个跳数索引标记。
      * **仅在排序键无法有效覆盖某些重要查询场景时，才谨慎添加跳数索引。** 过多索引会影响写入性能和存储。

### 3.5 分区 (Partitioning)
  * **分区 (`PARTITION BY expression`) 是 `MergeTree` 家族引擎用于数据管理和查询优化的重要机制，必须在 `CREATE TABLE` 语句中显式指定分区表达式。**
  * **最常用的分区键是时间相关的列**，通常按月 (例如 `PARTITION BY toYYYYMM(event_date)`) 或按天 (例如 `PARTITION BY toYYYYMMDD(event_date)` 或 `PARTITION BY event_date` 如果 `event_date` 是 `Date` 类型) 进行分区。也可以按其他业务维度分区，如地区ID等。
  * 分区键的选择应综合考虑：
      * **查询过滤**: 查询中是否经常按该键过滤？分区裁剪可以跳过扫描不相关的分区，极大提升查询性能。
      * **数据保留策略（TTL）**: 是否基于该键按分区删除旧数据？例如，按月分区，可以方便地删除几个月前的数据分区。
      * **分区数量**: 分区数量不宜过多（经验上，活跃分区保持在几百到几千个以内较好，总分区数过多会导致元数据管理开销增大和查询计划复杂化），避免使用基数非常高的列作为分区键导致分区过多。
      * **数据写入模式**: 确保数据能均匀写入各分区，避免某些分区过热或过大。
  * 分区键表达式的结果必须是整数或日期/日期时间类型。

### 3.6 数据压缩 (Compression)
  * `MergeTree` 引擎默认对每个列独立压缩，默认整体使用 `LZ4` 压缩算法。
  * ClickHouse 支持多种压缩算法，可以在 `CREATE TABLE` 语句的 `SETTINGS` 中通过 `min_compress_block_size`, `max_compress_block_size` 和列级 `CODEC` 指定更细致的压缩策略。
  * 常用的 `CODEC` 包括：
      * `NONE`: 不压缩。
      * `LZ4` (默认): 压缩速度快，压缩率一般。
      * `LZ4HC[(level)]`: 高压缩率的LZ4，压缩速度较慢。
      * `ZSTD[(level)]`: 压缩率高，压缩和解压速度均衡，通常是推荐的LZ4替代方案，level越高压缩率越高但越慢。
      * `Delta(delta_bytes)`: 对数据进行差分编码后再按基础CODEC压缩，适用于单调或缓慢变化的数据（如时间序列）。
      * `DoubleDelta`: 二阶差分，适用于变化率也比较平稳的数据。
      * `Gorilla`: 专为浮点数时间序列设计的高效压缩算法。
      * `T64`: 专为64位整数设计，配合 `Delta` 或 `DoubleDelta` 效果更佳。
  * 可以为不同的列根据其数据特性（如数据类型、基数、变化规律）指定不同的压缩编码组合，以达到最佳的压缩效果和查询性能平衡。例如，对变化缓慢的整数列使用 `Delta, ZSTD`，对高基数文本列使用 `ZSTD`。

### 3.7 数据保留策略 (TTL - Time To Live)
  * `MergeTree` 家族引擎支持通过 `TTL` 子句在 `CREATE TABLE` 时定义数据的自动过期策略，可以是删除数据或将数据移动到其他磁盘/卷。
  * **行级TTL**: `TTL event_date + INTERVAL 1 MONTH [DELETE|TO DISK 'disk_name'|TO VOLUME 'volume_name']`。
  * **条件删除TTL**: `TTL event_date + INTERVAL 1 WEEK DELETE WHERE status = 'temp'`。
  * **列级TTL**: 可以设置列的TTL，当列过期时，其值会被替换为该列类型的默认值。`ALTER TABLE ... MODIFY COLUMN col_name TTL date_col + INTERVAL 1 DAY;`
  * **表级TTL**: `TTL date_col + INTERVAL 1 YEAR RECOMPRESS CODEC('ZSTD(17)')` 可以在数据过期后重新压缩。
  * **合理设置 TTL 是管理存储成本和维护查询性能的关键。** TTL规则在数据合并时被应用。

### 3.8 标准字段适配
  * **创建时间 (`created_at`)**: 推荐使用 `DateTime` 或 `DateTime64` 类型。可在 `CREATE TABLE` 中设置默认值 `DEFAULT now()` 或 `DEFAULT toDateTime(0)` 后由应用层写入。
  * **更新时间 (`updated_at`)**: ClickHouse 主要面向分析场景，数据通常是不可变的或通过 `ReplacingMergeTree` 等引擎实现更新语义，因此传统的行级 `updated_at` 自动更新不常见。如果业务确实需要记录最后修改时间，应在应用层面处理，或结合版本列使用。
  * **软删除 (`deleted_at` 或类似机制)**: **推荐使用 `CollapsingMergeTree(sign)` 引擎**。为其添加一个 `sign Int8` 类型的列，插入真实数据时 `sign = 1`，当需要“删除”数据时，插入一条排序键相同但 `sign = -1` 的记录。查询时需要对 `sign` 列进行聚合 (例如 `GROUP BY ... HAVING sum(sign) > 0`)。对于非 `CollapsingMergeTree` 表，可以添加 `is_deleted UInt8 DEFAULT 0` 标记位，查询时过滤 `WHERE is_deleted = 0`，但这种方式无法自动清理数据。
  * **主键 ID**: 如果需要全局唯一 ID，推荐使用 `UUID` 类型。如果使用自增 ID，ClickHouse 没有内置的全局自增序列，需要应用层生成或使用外部序列服务（或利用 `rowNumberInAllBlocks()` 等函数在特定场景下生成，但非全局唯一）。通常在分析场景中，自然主键（如 `user_id`, `event_timestamp` 组合）或 `UUID` 更常见。

### 3.9 视图与物化视图 (Views & Materialized Views)
  * **普通视图 (`CREATE VIEW view_name AS SELECT ...`)**:
      * 只是查询的别名，不存储数据。每次查询都会重新计算。适用于简化复杂查询或进行权限控制。
  * **物化视图 (`CREATE MATERIALIZED VIEW [IF NOT EXISTS] [db.]view_name [TO [db.]target_table_name] [ENGINE = engine] [POPULATE] AS SELECT ... FROM source_table`)**:
      * **核心特性之一，用于实现高效的预聚合和数据转换**。
      * 当源表 (`source_table`) 插入数据时，将触发物化视图的 `SELECT` 查询，并将结果插入到目标表 (`target_table_name`，如果省略则自动创建同名内部表)。
      * 目标表通常选用 `SummingMergeTree`, `AggregatingMergeTree`, 或 `ReplacingMergeTree` 等聚合引擎，以支持预聚合的逻辑。
      * `POPULATE` 关键字表示在创建物化视图时，会立即将源表现有数据导入物化视图。
      * 能够显著加速基于预聚合结果的查询。设计时需仔细考虑数据一致性（特别是源表有更新/删除操作时，物化视图可能不会自动同步这些变更，除非目标表和物化视图查询设计能处理）、维护成本以及对源表写入性能的潜在影响。

### 3.10 分布式表设计 (`Distributed` 引擎)
  * 当使用 `Distributed` 表引擎时，需要仔细设计：
      * **集群名称**: 在 ClickHouse 配置文件中定义的集群名称。
      * **底层本地表**: `Distributed` 表本身不存储数据，它指向集群中各个节点上的本地表（通常是 `MergeTree` 家族）。确保所有分片上的本地表结构一致。
      * **分片键 (`sharding_key`)**: 用于决定数据写入时如何分布到集群的不同分片上。
          * **选择标准**:
              * **高基数 (High Cardinality) 且均匀分布**: 选择基数较高且其值能均匀分布的列或表达式，以确保数据能均匀分布到所有分片，避免数据倾斜。例如 `rand()` (实现随机分片，适用于写入负载均衡，但不利于特定分片查询)、`user_id` (如果用户量大且分布均匀), `cityHash64(user_id)` (对可能分布不均的ID进行哈希以提高分散度)。
              * **查询模式**: 如果查询经常按某个高基数列过滤，且希望查询能尽可能只命中部分分片（如果数据按该键分布），或者希望按该键的聚合能有效下推，则该列可作为分片键的候选。
              * **数据局部性**: 有些场景下，为了保证特定关联数据（如同一用户的所有事件）落在同一分片以优化后续在该分片上的JOIN或聚合，可能会选择该关联ID作为分片键。但这可能与数据均匀分布的目标冲突，需权衡。
              * **分片键表达式**: 可以是单个列，也可以是表达式，如 `intHash64(user_id)`。
          * **避免**: 基数过低的列，或数据分布严重不均的列，它们可能导致数据写入集中在少数分片，形成热点。
  * 分布式查询会自动将查询分发到集群中的所有相关分片（或根据`WHERE`条件和分片键优化后的部分分片），并在发起节点上汇总结果。

## 4. ClickHouse 设计示例：电商用户行为日志分析

本示例将演示如何根据上述规范设计一个用于存储和分析电商平台用户行为日志的 ClickHouse 表 (`tbl_user_action_logs`) 及相关的物化视图。

### 4.1 需求分析与核心实体

* **核心实体**: 用户行为日志 (`tbl_user_action_logs`)
* **主要字段**:
    * `event_time`: DateTime64(3) - 事件发生时间（毫秒精度）
    * `user_id`: UInt64 - 用户ID
    * `action_type`: LowCardinality(String) - 行为类型 (e.g., 'view', 'click', 'add_to_cart', 'order', 'pay')
    * `product_id`: UInt64 - 商品ID
    * `session_id`: String - 会话ID (非必须，但常用于分析)
    * `device_type`: LowCardinality(String) - 设备类型 (e.g., 'PC', 'Mobile', 'Tablet')
    * `ip_address`: IPv6 - 用户IP地址
    * `event_properties`: Map(String, String) - 其他事件属性 (灵活扩展)
* **主要查询模式**:
    * 按时间范围统计各种行为的总量、独立用户数。
    * 查询特定用户的行为序列。
    * 分析特定商品的转化漏斗。
    * 按天聚合用户行为数据。

### 4.2 基础表设计 (`tbl_user_action_logs`)

```sql
CREATE TABLE default.tbl_user_action_logs
(
    -- 业务字段
    `event_time` DateTime64(3) CODEC(Delta(4), ZSTD(1)),  -- 事件时间，毫秒精度，使用Delta+ZSTD压缩
    `user_id` UInt64 CODEC(T64, ZSTD(1)),                 -- 用户ID，使用T64+ZSTD压缩
    `action_type` LowCardinality(String),                 -- 行为类型，低基数字符串
    `product_id` UInt64 CODEC(T64, ZSTD(1)),              -- 商品ID
    `session_id` String,                                  -- 会话ID
    `device_type` LowCardinality(String),                 -- 设备类型
    `ip_address` IPv6,                                    -- IP地址
    `event_properties` Map(String, String),               -- 事件属性

    -- 为了CollapsingMergeTree或VersionedCollapsingMergeTree可能需要的sign和version列（本示例暂不使用，但可按需添加）
    -- `sign` Int8 DEFAULT 1,
    -- `version` UInt64 DEFAULT 0,

    -- 标准审计字段（在OLAP场景中，created_at通常等同于event_time，updated_at不适用）
    `log_date` Date MATERIALIZED toDate(event_time) -- 用于分区的辅助列

)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(log_date)     -- 按月分区 (基于 log_date)
ORDER BY (log_date, user_id, event_time, action_type) -- 排序键：日期、用户ID、事件时间、行为类型
TTL log_date + INTERVAL 365 DAY DELETE -- 数据保留1年
SETTINGS index_granularity = 8192, merge_with_ttl_timeout = 86400; -- 标准的granularity，允许TTL在合并时执行

-- 添加表注释
COMMENT ON TABLE default.tbl_user_action_logs IS '电商平台用户行为日志表，记录用户的各种交互行为。';

-- 添加字段注释
COMMENT ON COLUMN default.tbl_user_action_logs.event_time IS '事件发生的精确时间（毫秒级）';
COMMENT ON COLUMN default.tbl_user_action_logs.user_id IS '执行操作的用户ID';
COMMENT ON COLUMN default.tbl_user_action_logs.action_type IS '用户行为类型 (例如: view, click, add_to_cart, order, pay)';
COMMENT ON COLUMN default.tbl_user_action_logs.product_id IS '行为关联的商品ID (如果适用)';
COMMENT ON COLUMN default.tbl_user_action_logs.session_id IS '用户会话ID';
COMMENT ON COLUMN default.tbl_user_action_logs.device_type IS '用户设备类型 (例如: PC, Mobile, Tablet)';
COMMENT ON COLUMN default.tbl_user_action_logs.ip_address IS '用户的IP地址';
COMMENT ON COLUMN default.tbl_user_action_logs.event_properties IS '事件相关的其他动态属性 (键值对)';
COMMENT ON COLUMN default.tbl_user_action_logs.log_date IS '事件日期 (从event_time派生，主要用于分区)';

-- 跳数索引示例 (根据实际查询需求添加)
-- 如果经常需要快速筛选特定商品的行为:
ALTER TABLE default.tbl_user_action_logs ADD INDEX idx_product_id product_id TYPE bloom_filter GRANULARITY 1;
-- 如果经常需要快速定位特定IP的行为:
ALTER TABLE default.tbl_user_action_logs ADD INDEX idx_ip_address ip_address TYPE set(0) GRANULARITY 1; -- set(0) 表示自动选择max_rows
