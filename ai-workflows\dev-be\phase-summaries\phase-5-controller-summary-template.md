# Phase 5: Controller层完成汇总

**项目名称**: {project_name}
**业务模块**: {biz_module}
**完成时间**: {completion_time}
**执行人**: {developer_name}

## 1. 已生成文件清单

### 1.1 Controller文件
- `internal/controller/{biz_module}/{entity1}_controller.go` - {entity1}控制器
- `internal/controller/{biz_module}/{entity2}_controller.go` - {entity2}控制器
- `internal/controller/{biz_module}/{entity3}_controller.go` - {entity3}控制器

### 1.2 中间件文件
- `internal/middleware/auth.go` - 认证中间件
- `internal/middleware/cors.go` - 跨域中间件
- `internal/middleware/rate_limit.go` - 限流中间件
- `internal/middleware/request_id.go` - 请求ID中间件
- `internal/middleware/logger.go` - 日志中间件

### 1.3 响应处理文件
- `internal/controller/common/response.go` - 统一响应处理
- `internal/controller/common/error_handler.go` - 错误处理器
- `internal/controller/common/validator.go` - 参数验证器

### 1.4 依赖注入文件
- `internal/controller/{biz_module}/wire.go` - Wire依赖注入配置
- `internal/controller/{biz_module}/provider.go` - 控制器提供者配置

### 1.5 单元测试文件
- `internal/controller/{biz_module}/{entity1}_controller_test.go` - {entity1}控制器测试
- `internal/controller/{biz_module}/{entity2}_controller_test.go` - {entity2}控制器测试
- `internal/controller/common/response_test.go` - 响应处理测试

### 1.6 API文档文件
- `api/swagger/{biz_module}/{entity1}.yaml` - {entity1} API文档
- `api/swagger/{biz_module}/{entity2}.yaml` - {entity2} API文档
- `docs/api/{biz_module}_api.md` - API使用说明文档

## 2. Controller实现汇总

### 2.1 {Entity1}Controller
- **文件位置**: `internal/controller/{biz_module}/{entity1}_controller.go`
- **依赖注入**:
```go
type {Entity1}Controller struct {
    {entity1}Service service.{Entity1}Service
    logger           *logger.Logger
    validator        *validator.Validate
}

func New{Entity1}Controller(
    {entity1}Service service.{Entity1}Service,
    logger *logger.Logger,
    validator *validator.Validate,
) *{Entity1}Controller {
    return &{Entity1}Controller{
        {entity1}Service: {entity1}Service,
        logger:           logger,
        validator:        validator,
    }
}
```

#### 2.1.1 CRUD接口实现
```go
// Create 创建{entity1}
// @Summary 创建{entity1}
// @Description 创建新的{entity1}记录
// @Tags {entity1}
// @Accept json
// @Produce json
// @Param request body dto.{Entity1}CreateReq true "创建请求"
// @Success 200 {object} response.Response{data=dto.{Entity1}Resp} "创建成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 422 {object} response.Response "业务错误"
// @Failure 500 {object} response.Response "系统错误"
// @Router /{biz_module}/{entity1} [post]
func (c *{Entity1}Controller) Create(ctx *gin.Context) {
    var req dto.{Entity1}CreateReq
    
    // 1. 参数绑定
    if err := ctx.ShouldBindJSON(&req); err != nil {
        c.logger.Debug(ctx, "参数绑定失败", "error", err)
        response.Error(ctx, response.CodeInvalidParam, "参数格式错误", err)
        return
    }
    
    // 2. 参数验证
    if err := c.validator.Struct(&req); err != nil {
        c.logger.Debug(ctx, "参数验证失败", "error", err)
        response.Error(ctx, response.CodeInvalidParam, "参数验证失败", err)
        return
    }
    
    // 3. 调用Service
    resp, err := c.{entity1}Service.Create(ctx, &req)
    if err != nil {
        c.handleServiceError(ctx, err, "创建{entity1}失败")
        return
    }
    
    // 4. 返回结果
    response.Success(ctx, resp, "创建成功")
}

// GetByID 根据ID获取{entity1}
// @Summary 获取{entity1}详情
// @Description 根据ID获取{entity1}详细信息
// @Tags {entity1}
// @Accept json
// @Produce json
// @Param id path int64 true "{entity1} ID"
// @Success 200 {object} response.Response{data=dto.{Entity1}Resp} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 404 {object} response.Response "资源不存在"
// @Failure 500 {object} response.Response "系统错误"
// @Router /{biz_module}/{entity1}/{id} [get]
func (c *{Entity1}Controller) GetByID(ctx *gin.Context) {
    // 1. 获取路径参数
    idStr := ctx.Param("id")
    id, err := strconv.ParseInt(idStr, 10, 64)
    if err != nil || id <= 0 {
        response.Error(ctx, response.CodeInvalidParam, "ID参数无效", nil)
        return
    }
    
    // 2. 调用Service
    resp, err := c.{entity1}Service.GetByID(ctx, id)
    if err != nil {
        c.handleServiceError(ctx, err, "获取{entity1}失败")
        return
    }
    
    // 3. 返回结果
    response.Success(ctx, resp, "获取成功")
}

// Update 更新{entity1}
// @Summary 更新{entity1}
// @Description 更新{entity1}信息
// @Tags {entity1}
// @Accept json
// @Produce json
// @Param id path int64 true "{entity1} ID"
// @Param request body dto.{Entity1}UpdateReq true "更新请求"
// @Success 200 {object} response.Response{data=dto.{Entity1}Resp} "更新成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 404 {object} response.Response "资源不存在"
// @Failure 422 {object} response.Response "业务错误"
// @Failure 500 {object} response.Response "系统错误"
// @Router /{biz_module}/{entity1}/{id} [put]
func (c *{Entity1}Controller) Update(ctx *gin.Context) {
    // 1. 获取路径参数
    idStr := ctx.Param("id")
    id, err := strconv.ParseInt(idStr, 10, 64)
    if err != nil || id <= 0 {
        response.Error(ctx, response.CodeInvalidParam, "ID参数无效", nil)
        return
    }
    
    // 2. 参数绑定
    var req dto.{Entity1}UpdateReq
    if err := ctx.ShouldBindJSON(&req); err != nil {
        response.Error(ctx, response.CodeInvalidParam, "参数格式错误", err)
        return
    }
    
    // 3. 设置ID
    req.ID = id
    
    // 4. 参数验证
    if err := c.validator.Struct(&req); err != nil {
        response.Error(ctx, response.CodeInvalidParam, "参数验证失败", err)
        return
    }
    
    // 5. 调用Service
    resp, err := c.{entity1}Service.Update(ctx, &req)
    if err != nil {
        c.handleServiceError(ctx, err, "更新{entity1}失败")
        return
    }
    
    // 6. 返回结果
    response.Success(ctx, resp, "更新成功")
}

// Delete 删除{entity1}
// @Summary 删除{entity1}
// @Description 根据ID删除{entity1}
// @Tags {entity1}
// @Accept json
// @Produce json
// @Param id path int64 true "{entity1} ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 404 {object} response.Response "资源不存在"
// @Failure 422 {object} response.Response "业务错误"
// @Failure 500 {object} response.Response "系统错误"
// @Router /{biz_module}/{entity1}/{id} [delete]
func (c *{Entity1}Controller) Delete(ctx *gin.Context) {
    // 1. 获取路径参数
    idStr := ctx.Param("id")
    id, err := strconv.ParseInt(idStr, 10, 64)
    if err != nil || id <= 0 {
        response.Error(ctx, response.CodeInvalidParam, "ID参数无效", nil)
        return
    }
    
    // 2. 调用Service
    err = c.{entity1}Service.Delete(ctx, id)
    if err != nil {
        c.handleServiceError(ctx, err, "删除{entity1}失败")
        return
    }
    
    // 3. 返回结果
    response.Success(ctx, nil, "删除成功")
}

// List 获取{entity1}列表
// @Summary 获取{entity1}列表
// @Description 分页获取{entity1}列表
// @Tags {entity1}
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(10)
// @Param keyword query string false "关键词搜索"
// @Param status query int false "状态筛选"
// @Param startTime query int64 false "开始时间"
// @Param endTime query int64 false "结束时间"
// @Success 200 {object} response.Response{data=dto.{Entity1}ListResp} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "系统错误"
// @Router /{biz_module}/{entity1} [get]
func (c *{Entity1}Controller) List(ctx *gin.Context) {
    // 1. 参数绑定
    var req dto.{Entity1}ListReq
    if err := ctx.ShouldBindQuery(&req); err != nil {
        response.Error(ctx, response.CodeInvalidParam, "参数格式错误", err)
        return
    }
    
    // 2. 设置默认值
    if req.Page <= 0 {
        req.Page = 1
    }
    if req.PageSize <= 0 {
        req.PageSize = 10
    }
    if req.PageSize > 100 {
        req.PageSize = 100
    }
    
    // 3. 参数验证
    if err := c.validator.Struct(&req); err != nil {
        response.Error(ctx, response.CodeInvalidParam, "参数验证失败", err)
        return
    }
    
    // 4. 调用Service
    resp, err := c.{entity1}Service.List(ctx, &req)
    if err != nil {
        c.handleServiceError(ctx, err, "获取{entity1}列表失败")
        return
    }
    
    // 5. 返回结果
    response.Success(ctx, resp, "获取成功")
}
```

#### 2.1.2 业务接口实现
```go
// {BusinessMethod1} {business_method1_description}
// @Summary {business_method1_summary}
// @Description {business_method1_description}
// @Tags {entity1}
// @Accept json
// @Produce json
// @Param request body dto.{BusinessReq1} true "业务请求"
// @Success 200 {object} response.Response{data=dto.{BusinessResp1}} "操作成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 422 {object} response.Response "业务错误"
// @Failure 500 {object} response.Response "系统错误"
// @Router /{biz_module}/{entity1}/{business_action1} [post]
func (c *{Entity1}Controller) {BusinessMethod1}(ctx *gin.Context) {
    var req dto.{BusinessReq1}
    
    // 1. 参数绑定和验证
    if err := c.bindAndValidate(ctx, &req); err != nil {
        return
    }
    
    // 2. 调用Service
    resp, err := c.{entity1}Service.{BusinessMethod1}(ctx, &req)
    if err != nil {
        c.handleServiceError(ctx, err, "{business_method1}失败")
        return
    }
    
    // 3. 返回结果
    response.Success(ctx, resp, "操作成功")
}

// {ChangeStatus} 修改{entity1}状态
// @Summary 修改{entity1}状态
// @Description 修改{entity1}的状态
// @Tags {entity1}
// @Accept json
// @Produce json
// @Param id path int64 true "{entity1} ID"
// @Param status body object{status=int32} true "状态信息"
// @Success 200 {object} response.Response "修改成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 404 {object} response.Response "资源不存在"
// @Failure 422 {object} response.Response "业务错误"
// @Failure 500 {object} response.Response "系统错误"
// @Router /{biz_module}/{entity1}/{id}/status [patch]
func (c *{Entity1}Controller) {ChangeStatus}(ctx *gin.Context) {
    // 1. 获取路径参数
    id, err := c.getIDFromPath(ctx)
    if err != nil {
        return
    }
    
    // 2. 获取状态参数
    var req struct {
        Status int32 `json:"status" binding:"required" validate:"oneof=1 2 3"`
    }
    if err := c.bindAndValidate(ctx, &req); err != nil {
        return
    }
    
    // 3. 调用Service
    err = c.{entity1}Service.{ChangeStatus}(ctx, id, req.Status)
    if err != nil {
        c.handleServiceError(ctx, err, "修改状态失败")
        return
    }
    
    // 4. 返回结果
    response.Success(ctx, nil, "修改成功")
}
```

#### 2.1.3 辅助方法实现
```go
// bindAndValidate 参数绑定和验证
func (c *{Entity1}Controller) bindAndValidate(ctx *gin.Context, req interface{}) error {
    // 参数绑定
    if err := ctx.ShouldBindJSON(req); err != nil {
        c.logger.Debug(ctx, "参数绑定失败", "error", err)
        response.Error(ctx, response.CodeInvalidParam, "参数格式错误", err)
        return err
    }
    
    // 参数验证
    if err := c.validator.Struct(req); err != nil {
        c.logger.Debug(ctx, "参数验证失败", "error", err)
        response.Error(ctx, response.CodeInvalidParam, "参数验证失败", err)
        return err
    }
    
    return nil
}

// getIDFromPath 从路径获取ID参数
func (c *{Entity1}Controller) getIDFromPath(ctx *gin.Context) (int64, error) {
    idStr := ctx.Param("id")
    id, err := strconv.ParseInt(idStr, 10, 64)
    if err != nil || id <= 0 {
        response.Error(ctx, response.CodeInvalidParam, "ID参数无效", nil)
        return 0, err
    }
    return id, nil
}

// handleServiceError 处理Service层错误
func (c *{Entity1}Controller) handleServiceError(ctx *gin.Context, err error, message string) {
    c.logger.Debug(ctx, message, "error", err)
    
    // 根据错误类型返回不同的HTTP状态码
    switch {
    case errors.IsNotFoundError(err):
        response.Error(ctx, response.CodeNotFound, "资源不存在", err)
    case errors.IsConflictError(err):
        response.Error(ctx, response.CodeConflict, "资源冲突", err)
    case errors.IsValidationError(err):
        response.Error(ctx, response.CodeValidationFailed, "业务规则验证失败", err)
    case errors.IsPermissionError(err):
        response.Error(ctx, response.CodePermissionDenied, "权限不足", err)
    default:
        response.Error(ctx, response.CodeInternalError, "系统内部错误", nil)
    }
}
```

### 2.2 {Entity2}Controller
- **文件位置**: `internal/controller/{biz_module}/{entity2}_controller.go`
- **实现结构**: [类似上述结构]

### 2.3 {Entity3}Controller
- **文件位置**: `internal/controller/{biz_module}/{entity3}_controller.go`
- **实现结构**: [类似上述结构]

## 3. 中间件实现汇总

### 3.1 认证中间件
- **文件位置**: `internal/middleware/auth.go`
```go
// AuthMiddleware JWT认证中间件
func AuthMiddleware() gin.HandlerFunc {
    return func(ctx *gin.Context) {
        // 1. 获取Token
        token := ctx.GetHeader("Authorization")
        if token == "" {
            response.Error(ctx, response.CodeUnauthorized, "缺少认证信息", nil)
            ctx.Abort()
            return
        }
        
        // 2. 验证Token
        claims, err := jwt.ParseToken(token)
        if err != nil {
            response.Error(ctx, response.CodeUnauthorized, "认证信息无效", err)
            ctx.Abort()
            return
        }
        
        // 3. 设置用户信息
        ctx.Set("userID", claims.UserID)
        ctx.Set("username", claims.Username)
        ctx.Set("roles", claims.Roles)
        
        ctx.Next()
    }
}
```

### 3.2 跨域中间件
- **文件位置**: `internal/middleware/cors.go`
```go
// CORSMiddleware 跨域中间件
func CORSMiddleware() gin.HandlerFunc {
    return cors.New(cors.Config{
        AllowOrigins:     []string{"*"},
        AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"},
        AllowHeaders:     []string{"Origin", "Content-Length", "Content-Type", "Authorization"},
        ExposeHeaders:    []string{"Content-Length"},
        AllowCredentials: true,
        MaxAge:           12 * time.Hour,
    })
}
```

### 3.3 限流中间件
- **文件位置**: `internal/middleware/rate_limit.go`
```go
// RateLimitMiddleware 限流中间件
func RateLimitMiddleware(rate int, burst int) gin.HandlerFunc {
    limiter := rate.NewLimiter(rate.Limit(rate), burst)
    
    return func(ctx *gin.Context) {
        if !limiter.Allow() {
            response.Error(ctx, response.CodeTooManyRequests, "请求过于频繁", nil)
            ctx.Abort()
            return
        }
        ctx.Next()
    }
}
```

### 3.4 请求ID中间件
- **文件位置**: `internal/middleware/request_id.go`
```go
// RequestIDMiddleware 请求ID中间件
func RequestIDMiddleware() gin.HandlerFunc {
    return func(ctx *gin.Context) {
        requestID := ctx.GetHeader("X-Request-ID")
        if requestID == "" {
            requestID = uuid.New().String()
        }
        
        ctx.Set("requestID", requestID)
        ctx.Header("X-Request-ID", requestID)
        
        ctx.Next()
    }
}
```

### 3.5 日志中间件
- **文件位置**: `internal/middleware/logger.go`
```go
// LoggerMiddleware 日志中间件
func LoggerMiddleware(logger *logger.Logger) gin.HandlerFunc {
    return func(ctx *gin.Context) {
        start := time.Now()
        path := ctx.Request.URL.Path
        raw := ctx.Request.URL.RawQuery
        
        ctx.Next()
        
        latency := time.Since(start)
        clientIP := ctx.ClientIP()
        method := ctx.Request.Method
        statusCode := ctx.Writer.Status()
        
        if raw != "" {
            path = path + "?" + raw
        }
        
        logger.Debug(ctx, "HTTP请求",
            "method", method,
            "path", path,
            "status", statusCode,
            "latency", latency,
            "ip", clientIP,
        )
    }
}
```

## 4. 响应处理汇总

### 4.1 统一响应结构
- **文件位置**: `internal/controller/common/response.go`
```go
// Response 统一响应结构
type Response struct {
    Code    int         `json:"code"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
    TraceID string      `json:"traceId,omitempty"`
}

// Success 成功响应
func Success(ctx *gin.Context, data interface{}, message string) {
    ctx.JSON(http.StatusOK, Response{
        Code:    CodeSuccess,
        Message: message,
        Data:    data,
        TraceID: getTraceID(ctx),
    })
}

// Error 错误响应
func Error(ctx *gin.Context, code int, message string, err error) {
    statusCode := getHTTPStatusCode(code)
    
    resp := Response{
        Code:    code,
        Message: message,
        TraceID: getTraceID(ctx),
    }
    
    // 开发环境返回详细错误信息
    if gin.Mode() == gin.DebugMode && err != nil {
        resp.Data = map[string]interface{}{
            "error": err.Error(),
        }
    }
    
    ctx.JSON(statusCode, resp)
}
```

### 4.2 错误码定义
```go
// 错误码常量
const (
    CodeSuccess           = 0     // 成功
    CodeInvalidParam      = 40001 // 参数错误
    CodeUnauthorized      = 40101 // 未认证
    CodePermissionDenied  = 40301 // 权限不足
    CodeNotFound          = 40401 // 资源不存在
    CodeConflict          = 40901 // 资源冲突
    CodeValidationFailed  = 42201 // 业务验证失败
    CodeTooManyRequests   = 42901 // 请求过于频繁
    CodeInternalError     = 50001 // 系统内部错误
)

// getHTTPStatusCode 获取HTTP状态码
func getHTTPStatusCode(code int) int {
    switch {
    case code >= 40000 && code < 41000:
        return http.StatusBadRequest
    case code >= 41000 && code < 42000:
        return http.StatusUnauthorized
    case code >= 43000 && code < 44000:
        return http.StatusForbidden
    case code >= 44000 && code < 45000:
        return http.StatusNotFound
    case code >= 49000 && code < 50000:
        return http.StatusConflict
    case code >= 42000 && code < 43000:
        return http.StatusUnprocessableEntity
    case code >= 50000:
        return http.StatusInternalServerError
    default:
        return http.StatusOK
    }
}
```

### 4.3 参数验证器
- **文件位置**: `internal/controller/common/validator.go`
```go
// CustomValidator 自定义验证器
type CustomValidator struct {
    validator *validator.Validate
}

// NewCustomValidator 创建验证器
func NewCustomValidator() *CustomValidator {
    v := validator.New()
    
    // 注册自定义验证规则
    v.RegisterValidation("mobile", validateMobile)
    v.RegisterValidation("idcard", validateIDCard)
    
    return &CustomValidator{validator: v}
}

// Struct 验证结构体
func (cv *CustomValidator) Struct(s interface{}) error {
    return cv.validator.Struct(s)
}

// validateMobile 手机号验证
func validateMobile(fl validator.FieldLevel) bool {
    mobile := fl.Field().String()
    matched, _ := regexp.MatchString(`^1[3-9]\d{9}$`, mobile)
    return matched
}

// validateIDCard 身份证号验证
func validateIDCard(fl validator.FieldLevel) bool {
    idcard := fl.Field().String()
    matched, _ := regexp.MatchString(`^\d{17}[\dXx]$`, idcard)
    return matched
}
```

## 5. 依赖注入配置汇总

### 5.1 Wire配置
- **文件位置**: `internal/controller/{biz_module}/wire.go`
```go
//go:build wireinject
// +build wireinject

package {biz_module}

import (
    "github.com/google/wire"
    "your-project/internal/controller/{biz_module}"
)

// ProviderSet 控制器提供者集合
var ProviderSet = wire.NewSet(
    // Controllers
    New{Entity1}Controller,
    New{Entity2}Controller,
    New{Entity3}Controller,
    
    // Common
    common.NewCustomValidator,
)

// InitControllers 初始化所有控制器
func InitControllers(
    {entity1}Service service.{Entity1}Service,
    {entity2}Service service.{Entity2}Service,
    {entity3}Service service.{Entity3}Service,
    logger *logger.Logger,
) (
    *{Entity1}Controller,
    *{Entity2}Controller,
    *{Entity3}Controller,
    error,
) {
    wire.Build(ProviderSet)
    return nil, nil, nil, nil
}
```

### 5.2 Provider配置
- **文件位置**: `internal/controller/{biz_module}/provider.go`
```go
// New{Entity1}Controller 创建{Entity1}控制器
func New{Entity1}Controller(
    {entity1}Service service.{Entity1}Service,
    logger *logger.Logger,
    validator *validator.Validate,
) *{Entity1}Controller {
    return &{Entity1}Controller{
        {entity1}Service: {entity1}Service,
        logger:           logger,
        validator:        validator,
    }
}
```

## 6. API文档汇总

### 6.1 Swagger注解规范
- ✅ 所有接口包含完整的Swagger注解
- ✅ 请求参数详细说明
- ✅ 响应结构明确定义
- ✅ 错误码完整覆盖
- ✅ 示例数据提供

### 6.2 API文档结构
```yaml
# api/swagger/{biz_module}/{entity1}.yaml
paths:
  /{biz_module}/{entity1}:
    post:
      summary: 创建{entity1}
      description: 创建新的{entity1}记录
      tags:
        - {entity1}
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/{Entity1}CreateReq'
      responses:
        '200':
          description: 创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Response'
```

### 6.3 API使用文档
- **文件位置**: `docs/api/{biz_module}_api.md`
- **内容包含**:
  - 接口概览
  - 认证方式
  - 请求格式
  - 响应格式
  - 错误码说明
  - 使用示例

## 7. 安全特性汇总

### 7.1 输入验证
- ✅ 参数类型验证
- ✅ 参数长度限制
- ✅ 参数格式验证
- ✅ 业务规则验证
- ✅ SQL注入防护
- ✅ XSS攻击防护

### 7.2 认证授权
- ✅ JWT Token认证
- ✅ 角色权限控制
- ✅ 接口访问控制
- ✅ 资源权限验证

### 7.3 安全头设置
```go
// SecurityHeadersMiddleware 安全头中间件
func SecurityHeadersMiddleware() gin.HandlerFunc {
    return func(ctx *gin.Context) {
        ctx.Header("X-Content-Type-Options", "nosniff")
        ctx.Header("X-Frame-Options", "DENY")
        ctx.Header("X-XSS-Protection", "1; mode=block")
        ctx.Header("Strict-Transport-Security", "max-age=31536000")
        ctx.Next()
    }
}
```

## 8. 性能优化汇总

### 8.1 请求处理优化
- ✅ 参数绑定优化
- ✅ 响应序列化优化
- ✅ 中间件执行顺序优化
- ✅ 静态资源缓存

### 8.2 并发控制
- ✅ 请求限流保护
- ✅ 连接池管理
- ✅ 超时控制
- ✅ 优雅关闭

### 8.3 缓存策略
```go
// CacheMiddleware 响应缓存中间件
func CacheMiddleware(duration time.Duration) gin.HandlerFunc {
    return func(ctx *gin.Context) {
        // 只缓存GET请求
        if ctx.Request.Method != "GET" {
            ctx.Next()
            return
        }
        
        // 生成缓存键
        cacheKey := generateCacheKey(ctx.Request.URL.Path, ctx.Request.URL.RawQuery)
        
        // 检查缓存
        if cached, exists := cache.Get(cacheKey); exists {
            ctx.Data(http.StatusOK, "application/json", cached.([]byte))
            return
        }
        
        // 执行请求
        ctx.Next()
        
        // 缓存响应
        if ctx.Writer.Status() == http.StatusOK {
            cache.Set(cacheKey, ctx.Writer.Body(), duration)
        }
    }
}
```

## 9. 错误处理汇总

### 9.1 全局错误处理
```go
// GlobalErrorHandler 全局错误处理中间件
func GlobalErrorHandler() gin.HandlerFunc {
    return func(ctx *gin.Context) {
        defer func() {
            if err := recover(); err != nil {
                logger.Error(ctx, "系统panic", "error", err, "stack", string(debug.Stack()))
                response.Error(ctx, response.CodeInternalError, "系统内部错误", nil)
            }
        }()
        
        ctx.Next()
        
        // 处理未捕获的错误
        if len(ctx.Errors) > 0 {
            err := ctx.Errors.Last()
            logger.Error(ctx, "未处理的错误", "error", err)
            response.Error(ctx, response.CodeInternalError, "系统内部错误", nil)
        }
    }
}
```

### 9.2 业务错误映射
```go
// mapServiceError 映射Service层错误到HTTP响应
func mapServiceError(err error) (int, string) {
    switch {
    case errors.IsNotFoundError(err):
        return response.CodeNotFound, "资源不存在"
    case errors.IsConflictError(err):
        return response.CodeConflict, "资源冲突"
    case errors.IsValidationError(err):
        return response.CodeValidationFailed, "业务规则验证失败"
    case errors.IsPermissionError(err):
        return response.CodePermissionDenied, "权限不足"
    case errors.IsRateLimitError(err):
        return response.CodeTooManyRequests, "请求过于频繁"
    default:
        return response.CodeInternalError, "系统内部错误"
    }
}
```

## 10. 测试覆盖情况

### 10.1 单元测试统计
- **{Entity1}Controller测试覆盖率**: {controller1_coverage}%
- **{Entity2}Controller测试覆盖率**: {controller2_coverage}%
- **{Entity3}Controller测试覆盖率**: {controller3_coverage}%
- **中间件测试覆盖率**: {middleware_coverage}%

### 10.2 测试场景覆盖
- ✅ 正常请求处理测试
- ✅ 参数验证测试
- ✅ 错误处理测试
- ✅ 中间件功能测试
- ✅ 认证授权测试
- ✅ 限流功能测试

### 10.3 集成测试
```go
// TestCreateEntity1 测试创建实体
func TestCreateEntity1(t *testing.T) {
    // 设置测试环境
    router := setupTestRouter()
    
    // 准备测试数据
    req := dto.{Entity1}CreateReq{
        {Field1}: "test_value",
        {Field2}: "test_value",
    }
    
    // 发送请求
    w := httptest.NewRecorder()
    body, _ := json.Marshal(req)
    request, _ := http.NewRequest("POST", "/{biz_module}/{entity1}", bytes.NewBuffer(body))
    request.Header.Set("Content-Type", "application/json")
    router.ServeHTTP(w, request)
    
    // 验证响应
    assert.Equal(t, http.StatusOK, w.Code)
    
    var resp response.Response
    err := json.Unmarshal(w.Body.Bytes(), &resp)
    assert.NoError(t, err)
    assert.Equal(t, response.CodeSuccess, resp.Code)
}
```

## 11. 监控指标汇总

### 11.1 性能指标
- **请求响应时间**: P50, P95, P99
- **请求吞吐量**: QPS统计
- **错误率**: 4xx, 5xx错误统计
- **并发连接数**: 活跃连接监控

### 11.2 业务指标
- **接口调用统计**: 各接口调用次数
- **用户行为统计**: 用户操作行为
- **业务成功率**: 业务操作成功率
- **资源使用情况**: CPU、内存使用率

### 11.3 监控实现
```go
// MetricsMiddleware 指标收集中间件
func MetricsMiddleware() gin.HandlerFunc {
    return func(ctx *gin.Context) {
        start := time.Now()
        
        ctx.Next()
        
        duration := time.Since(start)
        path := ctx.FullPath()
        method := ctx.Request.Method
        status := ctx.Writer.Status()
        
        // 记录指标
        metrics.RequestDuration.WithLabelValues(method, path).Observe(duration.Seconds())
        metrics.RequestTotal.WithLabelValues(method, path, strconv.Itoa(status)).Inc()
        
        if status >= 400 {
            metrics.ErrorTotal.WithLabelValues(method, path, strconv.Itoa(status)).Inc()
        }
    }
}
```

## 12. 下一Phase输入资源

### 12.1 可用Controller
- `{Entity1}Controller` - 完整CRUD和业务接口
- `{Entity2}Controller` - 完整CRUD和业务接口
- `{Entity3}Controller` - 完整CRUD和业务接口

### 12.2 中间件组件
- 认证中间件 - JWT Token验证
- 跨域中间件 - CORS配置
- 限流中间件 - 请求频率控制
- 日志中间件 - 请求日志记录
- 错误处理中间件 - 全局错误捕获

### 12.3 响应处理
- 统一响应格式
- 错误码体系
- 参数验证器
- 安全头设置

### 12.4 API文档
- Swagger文档完整
- 接口使用说明
- 错误码说明
- 示例代码提供

## 13. Router层开发指导

### 13.1 路由注册建议
- 按业务模块组织路由
- 使用路由组管理相关接口
- 合理配置中间件顺序
- 实现版本化路由管理

### 13.2 中间件配置建议
- 全局中间件在最外层
- 认证中间件按需配置
- 业务中间件精确配置
- 避免中间件冲突

### 13.3 路由设计建议
- 遵循RESTful设计原则
- 使用语义化的URL路径
- 合理设计路径参数
- 支持查询参数扩展

## 14. 注意事项

### 14.1 安全考虑
- 所有输入参数必须验证
- 敏感信息不在日志中记录
- 实现适当的访问控制
- 防止常见Web攻击

### 14.2 性能考虑
- 避免在Controller中进行复杂计算
- 合理使用缓存减少重复计算
- 控制响应数据大小
- 实现适当的限流保护

### 14.3 可维护性
- 保持Controller方法简洁
- 统一错误处理方式
- 完善的日志记录
- 清晰的代码注释

## 15. 验收确认

- ✅ 所有Controller实现完成
- ✅ 中间件配置完成
- ✅ 响应处理统一完成
- ✅ 参数验证完善
- ✅ 错误处理完整
- ✅ 安全特性实现
- ✅ API文档完整
- ✅ 单元测试全部通过
- ✅ 集成测试通过
- ✅ 性能测试通过
- ✅ 安全测试通过
- ✅ 代码评审通过
- ✅ 文档更新完成

**Phase 5 Controller层开发完成，可以进入Phase 6 Router层开发。**