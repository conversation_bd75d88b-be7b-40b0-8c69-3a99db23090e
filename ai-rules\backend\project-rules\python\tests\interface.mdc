---
description: 
globs: backend/tests/interface/**/*.py
alwaysApply: false
---
# Interface 层测试代码生成规则

**核心原则：简洁优先**

## 边界
- 视系统为黑盒：仅通过 HTTP 交互验证  
- 不验证业务细节；只校验路由、状态码、响应结构、错误映射  
- 依赖注入使用 FastAPI `dependency_overrides` 或在 TestClient 层替换

## 约定
- 优先使用 `fastapi.TestClient`，仅在必要时才使用 `patch`  
- 方法命名 `test_{endpoint}_{场景}`  
- HTTP响应 `status_code == 200` 且 JSON 包含 `code`, `data`, `error`  
- 成功断言 `code == 0` 且 `error == ""`  
- 在 `client.headers["Authorization"]` 注入 `Bearer {token}` 进行认证
- 使用 SQLite 内存库或依赖覆盖，测试间不共享状态  
- 覆盖率目标 ≥ 90 %


## 示例代码
```python
class TestAuthController:
    """认证控制器测试类"""
    
    def assert_response_structure(self, response_json):
        """验证响应的基本结构"""
        assert "data" in response_json, "响应缺少data字段"
        assert "code" in response_json, "响应缺少code字段"
        assert "error" in response_json, "响应缺少error字段"
    
    def assert_success_response(self, response_json):
        """验证成功响应的状态"""
        assert response_json["code"] == 0, "响应码不是成功状态(0)"
        assert response_json["error"] == "", "成功响应不应该包含错误信息"

    def test_register_user_success(self, client):
        """测试用户注册成功
        
        POST /api/v1/auth/register - 使用有效的用户名和密码注册
        """
        # 准备请求数据
        register_data = {
            "username": "new_user",
            "password": "Password123",
            "confirm_password": "Password123"
        }
        
        # 发送请求
        response = client.post("/api/v1/auth/register", json=register_data)
        
        # 检查响应
        response_json = response.json()
        
        # 验证响应状态码和基本结构
        assert response.status_code == 200
        self.assert_response_structure(response_json)
        self.assert_success_response(response_json)
        
        # 验证注册返回的用户数据
        user_data = response_json["data"]
        assert "user_id" in user_data, "注册响应缺少user_id"
        #其它字段验证

    def test_{端点名称}_{场景描述}(self, client):
        """测试说明
        
        HTTP方法 API路径 - 具体场景描述
        """
        # 1. 准备请求数据
        request_data = {...}
        
        # 2. 发送请求
        response = client.post("/api/v1/{路径}", json=request_data)
        
        # 3. 检查响应
        response_json = response.json()
        
        # 4. 验证响应状态
        assert response.status_code == 200
        self.assert_response_structure(response_json)
        self.assert_success_response(response_json)
        
        # 5. 验证业务数据
        data = response_json["data"]
        assert "field1" in data
        assert data["field1"] == expected_value
```
