---
description:
globs: backend/app/controller/**/*.go
alwaysApply: false
---
# Controller 层 (控制器层) 开发规范 (Go Kratos DDD)

> **核心原则：简洁优先**

## 1. 核心职责

### 1.1 边界定义
- 负责HTTP请求处理、参数绑定、输入验证、授权检查、响应序列化
- 禁止直接访问Repository、DAO或ORM；仅调用Service层
- 统一响应格式：`GenericResponse` 结构
- 记录入口与结果日志；异常统一处理

### 1.2 依赖规则
- Controller依赖Service接口，不依赖具体实现
- 通过Wire进行依赖注入
- 使用Gin框架处理HTTP请求
- 遵循RESTful API设计原则

## 2. 命名规范

### 2.1 文件命名
- Controller文件：`controller/{resource}_controller.go`
- 单元测试文件：`controller/{resource}_controller_test.go`
- Wire Provider文件：`controller/wire.go`

### 2.2 结构体命名
- Controller结构体：`{Resource}Controller`
- 方法命名：`{Action}{Resource}`，如 `CreateUser`、`GetUser`

### 2.3 路由规范
- URL格式：`/api/v{n}/{resources}`
- HTTP方法：GET、POST、PUT、PATCH、DELETE
- 路径参数：使用查询参数而非路径参数

## 3. 实现规范

### 3.1 结构体定义
```go
// app/user/controller/user_controller.go
package controller

import (
    "context"
    "net/http"
    
    "github.com/gin-gonic/gin"
    "github.com/go-kratos/kratos/v2/log"
    
    "your_project/app/user/service"
    "your_project/app/user/dto"
    "your_project/app/consts"
    "your_project/app/core/loger/logger_new"
)

type UserController struct {
    userService service.UserService
    log         *log.Helper
}

func NewUserController(
    userService service.UserService,
    logger log.Logger,
) *UserController {
    return &UserController{
        userService: userService,
        log:         log.NewHelper(logger),
    }
}
```

### 3.2 HTTP处理方法
```go
// CreateUser 创建用户
func (c *UserController) CreateUser(ctx *gin.Context) {
    startTime := time.Now()
    defer func() {
        duration := time.Since(startTime).Milliseconds()
        c.log.Infof("CreateUser completed in %d ms", duration)
    }()

    // 1. 参数绑定
    var req dto.CreateUserRequestDTO
    if err := ctx.ShouldBindJSON(&req); err != nil {
        c.log.Errorf("参数绑定失败: %v", err)
        ctx.JSON(http.StatusBadRequest, dto.GenericResponse{
            Code:    consts.ErrInvalidParams,
            Message: "参数格式错误",
            Data:    nil,
        })
        return
    }

    // 2. 参数验证
    if err := c.validateCreateUserRequest(&req); err != nil {
        c.log.Errorf("参数验证失败: %v", err)
        ctx.JSON(http.StatusBadRequest, dto.GenericResponse{
            Code:    consts.ErrInvalidParams,
            Message: err.Error(),
            Data:    nil,
        })
        return
    }

    // 3. 调用Service
    resp, err := c.userService.CreateUser(ctx.Request.Context(), &req)
    if err != nil {
        c.handleServiceError(ctx, err)
        return
    }

    // 4. 返回成功响应
    ctx.JSON(http.StatusCreated, dto.GenericResponse{
        Code:    consts.Success,
        Message: "创建成功",
        Data:    resp,
    })
}

// GetUser 获取用户信息
func (c *UserController) GetUser(ctx *gin.Context) {
    startTime := time.Now()
    defer func() {
        duration := time.Since(startTime).Milliseconds()
        c.log.Infof("GetUser completed in %d ms", duration)
    }()

    // 获取用户ID
    userIDStr := ctx.Query("userId")
    if userIDStr == "" {
        ctx.JSON(http.StatusBadRequest, dto.GenericResponse{
            Code:    consts.ErrInvalidParams,
            Message: "用户ID不能为空",
            Data:    nil,
        })
        return
    }

    userID, err := strconv.ParseInt(userIDStr, 10, 64)
    if err != nil {
        ctx.JSON(http.StatusBadRequest, dto.GenericResponse{
            Code:    consts.ErrInvalidParams,
            Message: "用户ID格式错误",
            Data:    nil,
        })
        return
    }

    // 调用Service
    resp, err := c.userService.GetUser(ctx.Request.Context(), userID)
    if err != nil {
        c.handleServiceError(ctx, err)
        return
    }

    ctx.JSON(http.StatusOK, dto.GenericResponse{
        Code:    consts.Success,
        Message: "获取成功",
        Data:    resp,
    })
}
```

### 3.3 错误处理
```go
// handleServiceError 统一处理Service层错误
func (c *UserController) handleServiceError(ctx *gin.Context, err error) {
    c.log.Errorf("Service error: %v", err)
    
    // 根据错误类型返回不同的HTTP状态码
    switch {
    case errors.Is(err, service.ErrUserNotFound):
        ctx.JSON(http.StatusOK, dto.GenericResponse{
            Code:    consts.ErrUserNotFound,
            Message: "用户不存在",
            Data:    nil,
        })
    case errors.Is(err, service.ErrUserAlreadyExists):
        ctx.JSON(http.StatusOK, dto.GenericResponse{
            Code:    consts.ErrUserAlreadyExists,
            Message: "用户已存在",
            Data:    nil,
        })
    default:
        ctx.JSON(http.StatusInternalServerError, dto.GenericResponse{
            Code:    consts.ErrInternalServer,
            Message: "服务器内部错误",
            Data:    nil,
        })
    }
}

// validateCreateUserRequest 验证创建用户请求参数
func (c *UserController) validateCreateUserRequest(req *dto.CreateUserRequestDTO) error {
    if req.Username == "" {
        return errors.New("用户名不能为空")
    }
    if len(req.Username) < 3 || len(req.Username) > 20 {
        return errors.New("用户名长度必须在3-20个字符之间")
    }
    if req.Email == "" {
        return errors.New("邮箱不能为空")
    }
    // 更多验证逻辑...
    return nil
}
```

## 4. 中间件使用

### 4.1 认证中间件
```go
// AuthMiddleware JWT认证中间件
func (c *UserController) AuthMiddleware() gin.HandlerFunc {
    return func(ctx *gin.Context) {
        token := ctx.GetHeader("Authorization")
        if token == "" {
            ctx.JSON(http.StatusUnauthorized, dto.GenericResponse{
                Code:    consts.ErrUnauthorized,
                Message: "缺少认证token",
                Data:    nil,
            })
            ctx.Abort()
            return
        }
        
        // 验证token逻辑...
        ctx.Next()
    }
}
```

### 4.2 日志中间件
```go
// LoggingMiddleware 请求日志中间件
func LoggingMiddleware(logger log.Logger) gin.HandlerFunc {
    return func(ctx *gin.Context) {
        startTime := time.Now()
        
        // 处理请求
        ctx.Next()
        
        // 记录请求日志
        duration := time.Since(startTime)
        log.NewHelper(logger).Infof(
            "HTTP %s %s %d %v",
            ctx.Request.Method,
            ctx.Request.URL.Path,
            ctx.Writer.Status(),
            duration,
        )
    }
}
```

## 5. 开发要求

### 5.1 技术要求
- 所有int类型使用int64
- 时间字段使用毫秒时间戳
- 使用app/core/loger/logger_new记录日志
- 每个方法添加性能计时统计
- 常量定义在app/consts中

### 5.2 质量要求
- 单元测试覆盖率 > 80%
- 所有公开方法必须有注释
- 错误处理完整且统一
- 参数验证严格且全面
```