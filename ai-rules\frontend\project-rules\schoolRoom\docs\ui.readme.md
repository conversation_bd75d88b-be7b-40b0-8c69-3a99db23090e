# @repo/ui 组件库

## 简介

@repo/ui 是基于 shadcn/ui 实现的 React UI 组件库，为教育系统提供了丰富的 UI 组件，以构建一致、美观且可访问的用户界面。

## 特性

- **类型安全**：全面使用 TypeScript，提供完整类型定义
- **主题系统**：支持亮色/暗色主题切换，基于 CSS 变量
- **响应式设计**：自适应各种屏幕尺寸
- **可访问性**：符合 WCAG 标准，支持键盘导航和屏幕阅读器
- **模块化**：组件可单独导入，减小打包体积

## 安装与设置

本组件库作为 monorepo 的内部包使用，无需单独安装。

引入组件示例：
```tsx
import { Button } from "@repo/ui/components/button";
import { cn } from "@repo/ui/lib/utils";
```

## 组件分类

### 布局组件

#### [Card](/components/card)
- **基本说明**：卡片容器。
- **场景说明**：[例如：当需要将相关内容或控件组织在一个视觉隔离的区块中时使用，如文章预览、产品信息展示等。]
- **输入信息 (Props)**：
    - `children` (React.ReactNode): [卡片的主要内容。]
    - `className` (string, 可选): [应用于卡片根元素的额外CSS类。]
    - `asChild` (boolean, 可选): [是否将 props 直接传递给子组件而不是渲染一个额外的 div。]
    - `...` (其他继承自 shadcn/ui Card (如 CardHeader, CardTitle, CardDescription, CardContent, CardFooter) 的可用 props，请参考 shadcn/ui 文档。)
- **输出信息 (渲染)**：[渲染一个结构化的卡片UI，通常可以包含头部（CardHeader）、标题（CardTitle）、描述（CardDescription）、内容区（CardContent）和底部（CardFooter）。]

#### [Separator](/components/separator)
- **基本说明**：分隔线。
- **场景说明**：[例如：用于在视觉上分隔不同组的内容、列表项或菜单项。]
- **输入信息 (Props)**：
    - `orientation` ("horizontal" | "vertical", 可选): [分隔线的方向，默认为 "horizontal"。]
    - `decorative` (boolean, 可选): [如果为 true，则该分隔线为装饰性的，对可访问性工具隐藏。]
    - `className` (string, 可选): [应用于分隔线元素的额外CSS类。]
    - `...` (其他 shadcn/ui Separator 的可用 props。)
- **输出信息 (渲染)**：[渲染一条水平或垂直的细线，用于内容分隔。]

#### [Table](/components/table)
- **基本说明**：表格。
- **场景说明**：[例如：用于结构化地展示多行多列的数据，如用户列表、产品规格等。]
- **输入信息 (Props)**：
    - `children` (React.ReactNode): [表格内容，通常是 TableHeader, TableBody, TableFooter, TableRow, TableHead, TableCell, TableCaption 等组件。]
    - `className` (string, 可选): [应用于表格根元素的额外CSS类。]
    - `...` (其他 shadcn/ui Table 及其子组件的可用 props。)
- **输出信息 (渲染)**：[渲染一个HTML表格，包含表头、表体、表脚等结构，用于展示数据集。]

#### [Sidebar](/components/sidebar) (假设这是一个自定义或组合组件)
- **基本说明**：侧边栏。
- **场景说明**：[例如：用于应用的主导航、内容筛选器或上下文相关的操作面板。]
- **输入信息 (Props)**：
    - `children` (React.ReactNode): [侧边栏中显示的内容。]
    - `isOpen` (boolean): [控制侧边栏的显示与隐藏状态。]
    - `onClose` (() => void): [关闭侧边栏时的回调函数。]
    - `position` ("left" | "right", 可选): [侧边栏显示位置，默认为 "left"。]
    - `className` (string, 可选): [应用于侧边栏容器的额外CSS类。]
- **输出信息 (渲染)**：[渲染一个通常位于页面一侧的面板，可以包含导航链接、菜单或其他UI元素。]

### 表单组件

#### [Button](/components/button)
- **基本说明**：按钮。
- **场景说明**：[例如：用于触发操作、提交表单、导航或任何需要用户点击交互的地方。]
- **输入信息 (Props)**：
    - `children` (React.ReactNode): [按钮上显示的文本或图标。]
    - `variant` ("default" | "destructive" | "outline" | "secondary" | "ghost" | "link", 可选): [按钮的视觉样式。]
    - `size` ("default" | "sm" | "lg" | "icon", 可选): [按钮的尺寸。]
    - `asChild` (boolean, 可选): [是否将 props 直接传递给子组件。]
    - `onClick` (React.MouseEventHandler<HTMLButtonElement>, 可选): [点击事件处理函数。]
    - `disabled` (boolean, 可选): [是否禁用按钮。]
    - `className` (string, 可选): [自定义CSS类名。]
    - `...` (其他 shadcn/ui Button 的可用 props。)
- **输出信息 (渲染)**：[渲染一个可点击的按钮元素，具有指定的样式和尺寸。]

#### [Input](/components/input)
- **基本说明**：输入框。
- **场景说明**：[例如：用于接收用户单行文本输入，如用户名、搜索词等。]
- **输入信息 (Props)**：
    - `type` (string, 可选): [输入框类型，如 "text", "password", "email", "number" 等，默认为 "text"。]
    - `placeholder` (string, 可选): [输入框的占位提示文本。]
    - `value` (string | number, 可选): [输入框的当前值（用于受控组件）。]
    - `onChange` (React.ChangeEventHandler<HTMLInputElement>, 可选): [值改变时的回调函数。]
    - `disabled` (boolean, 可选): [是否禁用输入框。]
    - `className` (string, 可选): [自定义CSS类名。]
    - `...` (其他 shadcn/ui Input 的可用 props。)
- **输出信息 (渲染)**：[渲染一个HTML输入字段，允许用户输入文本。]

#### [Textarea](/components/textarea)
- **基本说明**：多行文本框。
- **场景说明**：[例如：用于接收用户多行文本输入，如评论、详细描述等。]
- **输入信息 (Props)**：
    - `placeholder` (string, 可选): [文本框的占位提示文本。]
    - `value` (string, 可选): [文本框的当前值（用于受控组件）。]
    - `onChange` (React.ChangeEventHandler<HTMLTextAreaElement>, 可选): [值改变时的回调函数。]
    - `disabled` (boolean, 可选): [是否禁用文本框。]
    - `rows` (number, 可选): [可见的文本行数。]
    - `className` (string, 可选): [自定义CSS类名。]
    - `...` (其他 shadcn/ui Textarea 的可用 props。)
- **输出信息 (渲染)**：[渲染一个HTML多行文本输入区域。]

#### [Checkbox](/components/checkbox)
- **基本说明**：复选框。
- **场景说明**：[例如：用于允许用户在一组选项中选择一个或多个，或表示一个开关状态。]
- **输入信息 (Props)**：
    - `checked` (boolean | "indeterminate", 可选): [复选框的选中状态。]
    - `onCheckedChange` ((checked: boolean | "indeterminate") => void, 可选): [选中状态改变时的回调函数。]
    - `disabled` (boolean, 可选): [是否禁用复选框。]
    - `id` (string, 可选): [关联label用的id。]
    - `className` (string, 可选): [自定义CSS类名。]
    - `...` (其他 shadcn/ui Checkbox 的可用 props。)
- **输出信息 (渲染)**：[渲染一个可以被选中或取消选中的复选框，通常与一个标签关联。]

#### [RadioGroup](/components/radio-group)
- **基本说明**：单选框组。
- **场景说明**：[例如：用于让用户在一组互斥选项中选择一个。]
- **输入信息 (Props)**：
    - `children` (React.ReactNode): [包含 RadioGroupItem 组件。]
    - `value` (string, 可选): [当前选中的 RadioGroupItem 的值。]
    - `onValueChange` ((value: string) => void, 可选): [选中值改变时的回调。]
    - `defaultValue` (string, 可选): [默认选中的值。]
    - `disabled` (boolean, 可选): [是否禁用整个单选框组。]
    - `className` (string, 可选): [自定义CSS类名。]
    - `...` (其他 shadcn/ui RadioGroup 及 RadioGroupItem 的可用 props。)
- **输出信息 (渲染)**：[渲染一组单选按钮，用户只能从中选择一个选项。]

#### [Select](/components/select)
- **基本说明**：选择框。
- **场景说明**：[例如：用于从下拉列表中选择一个选项。]
- **输入信息 (Props)**：
    - `children` (React.ReactNode): [包含 SelectTrigger, SelectContent, SelectItem, SelectLabel, SelectSeparator 等组件。]
    - `value` (string, 可选): [当前选中的值。]
    - `onValueChange` ((value: string) => void, 可选): [值改变回调。]
    - `disabled` (boolean, 可选): [是否禁用选择框。]
    - `...` (其他 shadcn/ui Select 及其子组件的可用 props。)
- **输出信息 (渲染)**：[渲染一个点击后展开下拉列表的选择框，允许用户选择其中一项。]

#### [Switch](/components/switch)
- **基本说明**：开关。
- **场景说明**：[例如：用于切换某个设置的开启/关闭状态。]
- **输入信息 (Props)**：
    - `checked` (boolean, 可选): [开关的当前状态。]
    - `onCheckedChange` ((checked: boolean) => void, 可选): [状态改变回调。]
    - `disabled` (boolean, 可选): [是否禁用开关。]
    - `className` (string, 可选): [自定义CSS类名。]
    - `...` (其他 shadcn/ui Switch 的可用 props。)
- **输出信息 (渲染)**：[渲染一个可以在开启和关闭状态之间切换的开关控件。]

### 数据展示组件

#### [Avatar](/components/avatar)
- **基本说明**：头像。
- **场景说明**：[例如：用于显示用户、组织或其他实体的图片表示。]
- **输入信息 (Props)**：
    - `children` (React.ReactNode): [通常是 AvatarImage 和 AvatarFallback 组件。]
    - `className` (string, 可选): [自定义CSS类名。]
    - `...` (其他 shadcn/ui Avatar, AvatarImage, AvatarFallback 的可用 props。)
- **输出信息 (渲染)**：[渲染一个圆形或方形的头像区域，可以包含图片或在图片加载失败/未提供时显示回退内容。]

#### [Badge](/components/badge)
- **基本说明**：徽章。
- **场景说明**：[例如：用于标记状态、类别、计数或突出显示少量信息。]
- **输入信息 (Props)**：
    - `children` (React.ReactNode): [徽章内显示的文本或内容。]
    - `variant` ("default" | "secondary" | "outline" | "destructive", 可选): [徽章的视觉样式。]
    - `className` (string, 可选): [自定义CSS类名。]
    - `...` (其他 shadcn/ui Badge 的可用 props。)
- **输出信息 (渲染)**：[渲染一个小巧的、通常带有背景色的标签，用于展示简短信息。]

#### [Progress](/components/progress)
- **基本说明**：进度条。
- **场景说明**：[例如：用于显示任务的完成百分比或加载进度。]
- **输入信息 (Props)**：
    - `value` (number, 可选): [当前的进度值（通常是0-100）。]
    - `max` (number, 可选): [最大进度值，默认为100。]
    - `className` (string, 可选): [自定义CSS类名。]
    - `...` (其他 shadcn/ui Progress 的可用 props。)
- **输出信息 (渲染)**：[渲染一个水平的进度条，视觉上表示任务的完成程度。]

#### [Skeleton](/components/skeleton)
- **基本说明**：骨架屏。
- **场景说明**：[例如：在内容加载期间显示占位符，提升用户感知性能。]
- **输入信息 (Props)**：
    - `className` (string, 可选): [用于定义骨架屏的形状和大小（如 w-full h-4 rounded-full）。]
    - `...` (其他 shadcn/ui Skeleton 的可用 props。)
- **输出信息 (渲染)**：[渲染一个通常为灰色、带有动画效果的占位块，模拟正在加载的内容的轮廓。]

#### [MindMap](/components/mind-map) (假设这是一个自定义或组合组件)
- **基本说明**：思维导图。
- **场景说明**：[例如：用于可视化地组织和展示层级关系或相互关联的想法。]
- **输入信息 (Props)**：
    - `data` (object): [符合特定格式的思维导图数据结构。]
    - `options` (object, 可选): [思维导图的配置选项，如布局、样式等。]
    - `onNodeClick` ((nodeData: object) => void, 可选): [节点点击事件回调。]
    - `className` (string, 可选): [自定义CSS类名。]
- **输出信息 (渲染)**：[渲染一个可交互的思维导图视图。]

### 弹出组件

#### [Dialog](/components/dialog)
- **基本说明**：对话框。
- **场景说明**：[例如：用于显示重要的信息、需要用户确认的操作或短表单。]
- **输入信息 (Props)**：
    - `children` (React.ReactNode): [DialogTrigger, DialogContent 等组件。]
    - `open` (boolean, 可选): [控制对话框的打开状态（用于受控模式）。]
    - `onOpenChange` ((open: boolean) => void, 可选): [打开状态改变回调。]
    - `...` (其他 shadcn/ui Dialog 及其子组件的可用 props。)
- **输出信息 (渲染)**：[渲染一个模态或非模态的对话框窗口，通常覆盖在主内容之上。]

#### [AlertDialog](/components/alert-dialog)
- **基本说明**：警告对话框。
- **场景说明**：[例如：用于确认具有破坏性或不可逆的操作，如删除数据。]
- **输入信息 (Props)**：
    - `children` (React.ReactNode): [AlertDialogTrigger, AlertDialogContent, AlertDialogAction, AlertDialogCancel 等组件。]
    - `...` (其他 shadcn/ui AlertDialog 及其子组件的可用 props。)
- **输出信息 (渲染)**：[渲染一个特殊的模态对话框，用于中断用户流程并要求用户对警告信息作出响应。]

#### [Popover](/components/popover)
- **基本说明**：弹出框。
- **场景说明**：[例如：当点击某个触发元素时，在其附近显示额外的信息、选项或控件。]
- **输入信息 (Props)**：
    - `children` (React.ReactNode): [PopoverTrigger, PopoverContent 等组件。]
    - `...` (其他 shadcn/ui Popover 及其子组件的可用 props。)
- **输出信息 (渲染)**：[渲染一个由触发器激活的非模态浮层，用于展示上下文相关内容。]

#### [DropdownMenu](/components/dropdown-menu)
- **基本说明**：下拉菜单。
- **场景说明**：[例如：提供一个包含多个操作选项的列表，当用户点击触发元素时显示。]
- **输入信息 (Props)**：
    - `children` (React.ReactNode): [DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem 等组件。]
    - `...` (其他 shadcn/ui DropdownMenu 及其子组件的可用 props。)
- **输出信息 (渲染)**：[渲染一个点击触发器后展开的菜单列表。]

#### [Sheet](/components/sheet)
- **基本说明**：滑动面板。
- **场景说明**：[例如：从屏幕边缘滑出，用于显示导航、表单或详细信息，常见于移动端或作为侧边栏的替代。]
- **输入信息 (Props)**：
    - `children` (React.ReactNode): [SheetTrigger, SheetContent 等组件。]
    - `side` ("top" | "bottom" | "left" | "right", 可选): [面板滑出的方向。]
    - `...` (其他 shadcn/ui Sheet 及其子组件的可用 props。)
- **输出信息 (渲染)**：[渲染一个从屏幕边缘滑入滑出的面板。]

#### [Tooltip](/components/tooltip)
- **基本说明**：工具提示。
- **场景说明**：[例如：当用户鼠标悬停或聚焦在某个元素上时，显示简短的解释性文本。]
- **输入信息 (Props)**：
    - `children` (React.ReactNode): [TooltipTrigger, TooltipContent, TooltipProvider 组件。]
    - `delayDuration` (number, 可选): [触发显示的延迟时间（毫秒）。]
    - `...` (其他 shadcn/ui Tooltip 及其子组件的可用 props。)
- **输出信息 (渲染)**：[渲染一个在触发元素附近显示的小型提示浮层。]

#### [Toast](/components/toast)
- **基本说明**：轻提示。
- **场景说明**：[例如：用于显示简短的、非侵入式的系统消息或操作反馈，如“保存成功”。]
- **输入信息 (Props)**：
    - `title` (React.ReactNode, 可选): [提示的标题。]
    - `description` (React.ReactNode, 可选): [提示的描述内容。]
    - `action` (React.ReactNode, 可选): [提示中的可操作元素，如一个按钮。]
    - `variant` ("default" | "destructive", 可选): [提示的视觉样式。]
    - `...` (通常通过 `useToast` 钩子或 Toaster 组件来触发和管理。)
- **输出信息 (渲染)**：[渲染一个短暂显示在屏幕角落（通常）的消息框。]

### 导航组件

#### [Tabs](/components/tabs)
- **基本说明**：标签页。
- **场景说明**：[例如：将不同部分的内容组织在同一区域，用户可以通过点击标签切换显示不同的内容面板。]
- **输入信息 (Props)**：
    - `children` (React.ReactNode): [TabsList, TabsTrigger, TabsContent 组件。]
    - `defaultValue` (string, 可选): [默认激活的标签页的 value。]
    - `value` (string, 可选): [当前激活的标签页的 value（用于受控模式）。]
    - `onValueChange` ((value: string) => void, 可选): [激活标签页改变时的回调。]
    - `orientation` ("horizontal" | "vertical", 可选): [标签列表的方向。]
    - `className` (string, 可选): [自定义CSS类名。]
    - `...` (其他 shadcn/ui Tabs 及其子组件的可用 props。)
- **输出信息 (渲染)**：[渲染一组标签头和对应的内容面板，允许用户切换视图。]

#### [Pagination](/components/pagination)
- **基本说明**：分页。
- **场景说明**：[例如：当内容（如列表、搜索结果）数量过多，需要分成多个页面展示时使用。]
- **输入信息 (Props)**：
    - `children` (React.ReactNode): [PaginationContent, PaginationItem, PaginationLink, PaginationPrevious, PaginationNext, PaginationEllipsis 等组件。]
    - `count` (number): [总页数或总条目数，具体取决于实现。]
    - `page` (number): [当前页码。]
    - `onPageChange` ((page: number) => void): [页码改变时的回调。]
    - `siblingCount` (number, 可选): [当前页码两侧显示的页码数量。]
    - `className` (string, 可选): [自定义CSS类名。]
    - `...` (其他 shadcn/ui Pagination 及其子组件的可用 props。)
- **输出信息 (渲染)**：[渲染一组分页控件，如页码链接、上一页/下一页按钮，允许用户在不同页面间导航。]

### 其他组件

#### [Accordion](/components/accordion)
- **基本说明**：手风琴。
- **场景说明**：[例如：垂直堆叠一组可折叠的面板，一次通常只展开一个面板的内容，用于FAQ、分步指南等。]
- **输入信息 (Props)**：
    - `children` (React.ReactNode): [AccordionItem, AccordionTrigger, AccordionContent 组件。]
    - `type` ("single" | "multiple"): [手风琴类型，"single"表示一次只能展开一项，"multiple"允许多项同时展开。]
    - `defaultValue` (string | string[], 可选): [默认展开项的value。]
    - `value` (string | string[], 可选): [当前展开项的value（用于受控模式）。]
    - `onValueChange` ((value: string | string[]) => void, 可选): [展开项改变回调。]
    - `collapsible` (boolean, 可选): [当 type 为 "single" 时，是否允许所有项都折叠。]
    - `className` (string, 可选): [自定义CSS类名。]
    - `...` (其他 shadcn/ui Accordion 及其子组件的可用 props。)
- **输出信息 (渲染)**：[渲染一组可折叠的面板，每个面板包含一个触发器和对应的内容区域。]

#### [Calendar](/components/calendar)
- **基本说明**：日历。
- **场景说明**：[例如：用于选择单个日期、日期范围或展示月份视图。]
- **输入信息 (Props)**：
    - `mode` ("default" | "single" | "multiple" | "range", 可选): [日历选择模式。]
    - `selected` (Date | Date[] | { from?: Date; to?: Date }, 可选): [当前选中的日期。]
    - `onSelect` ((day: Date | Date[] | { from?: Date; to?: Date } | undefined) => void, 可选): [日期选择回调。]
    - `month` (Date, 可选): [日历当前显示的月份。]
    - `onMonthChange` ((month: Date) => void, 可选): [月份改变回调。]
    - `disabled` (Matcher | Matcher[], 可选): [禁用的日期匹配规则。]
    - `className` (string, 可选): [自定义CSS类名。]
    - `...` (其他 react-day-picker (shadcn/ui Calendar基于此) 的可用 props。)
- **输出信息 (渲染)**：[渲染一个可交互的日历视图，允许用户查看和选择日期。]

#### [Captcha](/components/captcha) (假设这是一个自定义或组合组件)
- **基本说明**：验证码。
- **场景说明**：[例如：用于表单提交前的人机验证，防止机器人滥用。]
- **输入信息 (Props)**：
    - `siteKey` (string): [验证码服务提供的站点密钥。]
    - `onVerify` ((token: string | null) => void): [验证成功或失败时的回调，返回验证token。]
    - `theme` ("light" | "dark", 可选): [验证码组件的主题。]
    - `className` (string, 可选): [自定义CSS类名。]
- **输出信息 (渲染)**：[渲染一个验证码小部件（如Google reCAPTCHA），用户需要完成验证操作。]

#### [Collapsible](/components/collapsible)
- **基本说明**：折叠面板。
- **场景说明**：[例如：用于显示或隐藏一段内容，通常由一个触发器控制。]
- **输入信息 (Props)**：
    - `children` (React.ReactNode): [CollapsibleTrigger, CollapsibleContent 组件。]
    - `open` (boolean, 可选): [当前是否展开（用于受控模式）。]
    - `defaultOpen` (boolean, 可选): [默认是否展开。]
    - `onOpenChange` ((open: boolean) => void, 可选): [展开状态改变回调。]
    - `disabled` (boolean, 可选): [是否禁用。]
    - `className` (string, 可选): [自定义CSS类名。]
    - `...` (其他 shadcn/ui Collapsible 及其子组件的可用 props。)
- **输出信息 (渲染)**：[渲染一个可以展开和折叠其内容区域的组件。]

#### [ScrollArea](/components/scroll-area)
- **基本说明**：滚动区域。
- **场景说明**：[例如：当内容超出其容器大小时，提供自定义样式的滚动条并优化滚动体验。]
- **输入信息 (Props)**：
    - `children` (React.ReactNode): [需要滚动的内容。]
    - `className` (string, 可选): [应用于滚动区域容器的CSS类。]
    - `type` ("auto" | "always" | "scroll" | "hover", 可选): [滚动条显示策略。]
    - `...` (其他 shadcn/ui ScrollArea 及 ScrollBar 的可用 props。)
- **输出信息 (渲染)**：[渲染一个带有自定义滚动条的区域，用于显示可滚动内容。]

#### [ThemeProvider](/components/theme-provider) (通常在应用顶层使用)
- **基本说明**：主题提供者。
- **场景说明**：[用于为整个应用或其一部分提供主题（如亮色/暗色模式）切换能力和上下文。]
- **输入信息 (Props)**：
    - `children` (React.ReactNode): [被主题包裹的子组件。]
    - `defaultTheme` (string, 可选): [默认主题名称，如 "system", "light", "dark"。]
    - `storageKey` (string, 可选): [用于在localStorage中存储用户主题偏好的键名。]
    - `...` (其他 next-themes (shadcn/ui ThemeProvider基于此) 的可用 props。)
- **输出信息 (渲染/行为)**：[不直接渲染可见UI，但为其后代组件提供主题相关的上下文和切换逻辑。]

## 工具和钩子

### [cn](/lib/utils)
- **基本说明**：一个工具函数，用于方便地合并和条件化CSS类名。通常是 `clsx` 和 `tailwind-merge` 的组合。
- **场景说明**：[在React组件中动态构建 `className` 字符串时使用，特别是当需要根据条件添加或覆盖Tailwind CSS类时，它可以很好地处理类名冲突和冗余。]
- **输入信息**：
    - `...inputs` (any[]): [一个或多个参数，可以是字符串、对象（键为类名，值为布尔值）或数组。]
- **输出信息 (返回)**：
    - `string`: [合并和优化后的CSS类名字符串。]

### [useIsMobile](/hooks/use-mobile) (假设这是一个自定义钩子)
- **基本说明**：一个React Hook，用于检测当前窗口尺寸是否符合移动设备的标准。
- **场景说明**：[当需要根据用户设备是桌面还是移动端来动态改变组件行为、布局或渲染不同内容时使用。]
- **输入信息**：
    - `breakpoint` (number, 可选): [自定义的断点值（像素），低于此值则视为移动设备。默认为一个通用值（如768px）。]
- **输出信息 (返回)**：
    - `isMobile` (boolean): [如果当前窗口宽度小于等于断点值，则返回 `true`，否则返回 `false`。]

## 设计原则

1. **一致性**：所有组件遵循统一的设计语言
2. **可组合性**：组件可以自由组合构建复杂界面
3. **可访问性**：支持键盘导航和屏幕阅读器
4. **性能优化**：组件懒加载和代码分割
5. **可扩展性**：支持通过 className 和自定义属性扩展

## 贡献指南

1. 组件应遵循 shadcn/ui 的设计理念和实现模式
2. 每个UI组件、钩子和模块的文档应详细且完整，至少包含以下信息：
    - **基本说明**：清晰描述其功能和用途（是什么东西）。
    - **场景说明**：列举适合使用该组件/钩子/模块的具体场景（什么时候可以用）。
    - **输入信息**：详细说明其接收的props、参数或依赖（需要传入什么）。对于组件，应描述核心props及其类型和作用。
    - **输出信息**：描述其渲染的UI结构、返回的数据或产生的副作用（输出什么结果，展示为什么样）。对于组件，应说明其主要的视觉表现和交互行为。
3. 新组件应通过 Pull Request 提交，并附有示例和测试
4. 遵循项目的代码风格和命名约定