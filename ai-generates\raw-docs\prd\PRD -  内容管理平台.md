PRD -  内容管理平台

## 版本管理

| 版本号 | 日期 | 变更人 | 变更类型 | 变更详情 | 相关人 |
| --- | --- | --- | --- | --- | --- |
| V 1.0 | 2025.01.04 | 张博 | 新建文档 | 新建文档。 |  |
| V 1.1 | 2025.02.09 | 张博 | 补充需求 | 补充业务流程、用户角色描述及交互说明。 |  |
| V 1.2 | 2025.02.21 | 张博 | 补充内容 | 补充各模块核心页面 及 属性、操作。 |  |
| V 1.3 | 2025.02.24 | 张博 | 字段变更 | 题目管理字段，删除 【点评】，合并【分析】【解答】为【解析】。 |  |
| V 1.4 | 2025.02.24 | 张博 | 字段补充 | 试卷管理字段，新增浏览量、下载量、题目数量 |  |
| V 1.4 | 2025.03.05 | 张博 | 变更 | 题目管理字段试卷管理字段， |  |
| V5.0 | 2025.03.10 | 张博 | 调整需求 |  |  |
| V5.1 | 2025.03.11 | 张博 | 调整需求 |  |  |
| V9.1 | 2025.03.13 | 张博 | 更新需求 |  |  |
| V9.2 | 2025.03.28 | 张博 | 新增 |  |  |
| V9.3 | 2025.03.31 | 张博 | 补充需求 |  |  |
| V9.4 | 2025.04.01 | 张博 | 补充需求 |  |  |

- 删除 【题目用途】、【题目用途 ID】
- 【难度】变更为五档“易、较易、中档、较难、难”
- 删除 【试卷用途】
- 【难度】变更为五档“易、较易、中档、较难、难”
- 审核流程变更（调整部分标黄）
- 细节调整（调整部分标黄）
- 场景选题支持通过基础树、业务树查题选题。
- 更新选题场景审核不通过处理逻辑
- 运管平台开校配置会根据教材版本确认学校资源范围，故内容平台需要新增教材版本字段、教材版本和业务树映射关系
- 增加基础数据  - 业务树字段，教材版本
- 业务树选择变更为多级选择，先选教材版本，再选教材版本下的业务树，涉及场景题库选题，业务树列表
- 基础管理 - 业务树列表展示，增加教材版本字段的展示。
- 补充审核列表中审核任务的排序规则
- 增加了审核环节知识点校验不可为空。
## 关联需求

| 关联需求名称 | 所属 PM | 需求进度 | 文档链接 |
| --- | --- | --- | --- |
| 课程设计系统（CMS） | 袁全 |  |  |

## 设计稿

交互稿：

视觉稿：

动效：

## 一、背景和目标

### 需求背景

- 统一管理：构建一个统一的内容管理平台，实现对教育资源（基础树、题目、试卷、课程等）的标准化管理，提升资源管理效率。
- 完整体系建设：建立完善的知识体系和业务体系，支持资源的精准分类与检索，满足多场景使用需求。
- 高效创作与审核：提供高效的内容创作、题目录入及审核工具，确保内容的高质量输出，同时缩短审核周期。
### 项目收益

- 管理效率提升  
- 统一平台管理：整合基础树、业务树、题目、试卷、课程等资源，实现集中管理，便于数据更新、版本控制和权限分配。  
- 智能检索与分类：通过标准化的资源分类和检索机制，用户可以快速定位所需内容，大幅提升管理与查找效率。
- 内容质量保障  
- 审核打回机制：当录入内容存在错误或不符合标准时，审核人员会将内容打回，要求录入人员根据详细反馈进行修改，确保最终发布的内容准确无误。  
- 用户体验改善  
- 操作便捷性：统一的管理平台和智能工具不仅提升了内容录入和审核效率，还让后续内容使用、检索和组合更加便捷，提高了各环节的用户体验。
### 覆盖用户

- 学科教研：查看题目、试卷等内容。
- 内容录入人员：负责题目、试卷等内容的初步录入及编辑。
- 审核人员：对录入内容进行审核、打回与反馈，确保内容质量。
### 方案简述

搭建一个教育内容管理平台，让所有教学内容（如知识点、题目、试卷和课程）都能在一个地方轻松管理和使用。主要特点包括：

- 资源集中管理
所有内容统一存放，避免分散在各个系统中，提高管理和维护的效率。
- 便捷录入方式
用户只需上传 Word 文件，系统自动提取题目和试卷内容，省去繁琐的手动录入步骤。
- 高效审核流程
录入的内容会自动进入审核流程，审核人员可以快速检查并反馈问题，确保内容质量。
- 简单易用的搜索和选题
平台支持多种筛选和搜索方式，用户可以根据学段、学科等条件快速找到需要的资源，并可以将题目加入符合需求的场景题库。


## 二、名词说明

1. 基础树：以学科为单位，将所有知识点以树形结构进行组织。各学科按学段（小学、初中、高中）分别构建独立基础树，便于题目打标和资源检索。
1. 业务树：基础树的子集。根据实际使用场景（如教材版本、教学阶段）进行组织，用于辅助题目分类及组卷。
1. 场景题库：按照不同教学或练习场景（如 AI 课、巩固练习、拓展化练习）组织的题库，题目来自资源管理 - 题目管理。
## 三、业务流程

![board_Y8R4w7VZbh7oyfbaYHscLf0dnfg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524646264.png)

各模块核心页面 及 属性、操作

![board_TIvuw9z3ThU6dQbgvywccDBUnCc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524647184.png)

## 四、需求概览

| 序号 | 需求名称 | 内容概述 | 优先级 |
| --- | --- | --- | --- |
| 1 | 内容管理平台框架 | 构建统一平台，实现对基础树、业务树、题目、试卷、课程等资源管理 | P0 |
| 2 | 题目管理 | 支持题目的查看、检索、录入、编辑、纠错等功能 | P0 |
| 3 | 试卷管理 | 支持试卷的查看、检索、录入、预览与下载功能 | P1 |
| 3 | 资源录入 | 提供将 jinyou 网下载题目和试卷录入平台并完成题干解析和题目标签标记等功能 | P0 |
| 4 | 场景题库选题 | 实现可以按场景从【资源管理 - 题目管理】中选题或录入新题 | P0 |
| 5 | 基础数据管理 | 支持基础树与业务树的查看、筛选、搜索及树形展示 | P0 |
| 6 | 审核功能 | 支持对题目录入与选题的审核，包括状态流转、反馈与通知机制 | P0 |

## 五、详细产品方案

### 内容管理平台框架

![board_IsTswI8f2hLWEXbRI9GclhO1nue](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524647759.png)

| 模块 | 原型图 | 详情 |
| --- | --- | --- |
| 框架 - 导航 | ![in_table_image_AgiYbXLwDo31x2xR9yucV1ztngg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524652294.png) | 导航：导航定制： |
| 框架 - 消息 | / | 消息类型：消息内容： |
| 框架 - 我的 | / | 我的： |
| 框架 - 权限 | / | 权限类型：权限配置逻辑：权限分配方式： |

![analyze_in_table_image_AgiYbXLwDo31x2xR9yucV1ztngg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524652294.png)

- 题目管理（默认选中）
- 题目录入
- 场景题库
- 巩固练习
- 拓展练习
- 基础数据管理
- 基础树管理
- 业务树管理
- 课程设计管理
- 审核
- 根据用户权限展示相应的导航菜单，保证用户只访问其权限范围内的模块。
- 通知：
- 审核通知（待审核、审核结果反馈）
- 系统通知（版本更新、权限变更等）
- 来源
- 标题
- 内容
- 时间
- 操作按钮
- 用户名
- 登录/退出登录
- 查看权限：允许用户查看模块中的内容，但无法修改内容。
- 编辑权限：允许用户查看模块中的内容，并且可以修改、更新内容。
- 在 MVP 阶段，权限配置将由研发通过后台数据进行控制。
- 每个用户会被分配 查看 或 编辑 权限到某个模块，具体的权限设置将按模块（如题目管理、基础树管理等）进行。
- 每个模块的权限将单独配置，用户可以拥有该模块的 查看 权限、编辑 权限，或者同时拥有两者。
### 题目管理

#### 用户流程 - 查看题目

![board_HgeYwLB04hUyvBbaPYRc3kvWnsf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524648312.png)

#### 字段说明

| 字段名 | 描述 | 枚举值/选项（待确认） |
| --- | --- | --- |
| 学段 | 学段分类 | 小学、初中、高中 |
| 学段 id | 学段对应的 id |  |
| 学科 | 学科分类 | 语文、数学、英语、物理、化学、生物、政治、历史、地理 |
| 学科 id | 学科对应的 id |  |
| 基础树目录 | 关联的基础树目录 | 基础树目录列表（例如：小学数学，初中英语等） |
| 业务树目录 | 关联的业务树目录 | 业务树目录列表（例如：教材 A，教材 B 等） |
| 题目类型 | 题目类型 | 单选题，多选题，判断题，解答题，填空题，作文题等 |
| 题目类型 ID | 题目类型 ID |  |
| 难度等级 | 题目的难度等级 | 简单、较易、中等、较难、困难 |
| 年份 | 题目所属的年份 | 2023、2024、2025 等 |
| 地区 | 题目所属的地区 |  |
| 来源 | 题目的来源 | 互联网，出版社，内部开发 |
| 专题 | 题目所属的专题 |  |
| 所属试卷 | 题目所属的试卷来源 | 具体的试卷名称和试卷 id |
| 场景题库 | 是否关联场景题库 | AI 课、巩固练习、拓展练习、未关联 |
| 题目 ID | 题目的唯一标识 | 系统自动生成，位数由研发确定，全局唯一 |
| 题目内容 | 输入题目的具体内容 | 支持文本、公式、图片混排 |
| 选项内容 | 如果是选择题，输入每个选项的内容 | 无 |
| 选项历史数据 | 记录选择题各个选项的历史被选择数据 | 无 |
| 主知识点名称 | 输入题目关联的主知识点名称 | 无 |
| 主知识点 ID | 主知识点 ID |  |
| 次知识点名称 | 输入题目关联的次知识点名称 | 无 |
| 次知识点 ID | 次知识点 ID |  |
| 答案 | 输入题目的答案 | 无 |
| 解析 | 输入题目的解析内容 | 无 |
| 正确率 | 题目在过往使用中的正确率 | 百分比显示（如 85.0%） |
| 题目状态 | 题目的当前状态 | 待审核，审核通过 |
| 创建时间 | 题目创建的时间 | yyyy 年 MM 月 dd 日 HH:mm:ss |
| 更新时间 | 题目最近一次更新的时间 | yyyy 年 MM 月 dd 日 HH:mm:ss |
| 修改历史 | 系统自动记录题目的修改历史 | 无 |

#### 需求说明

| 模块 | 原型图 | 详情 |
| --- | --- | --- |
| 题目管理 | ![in_table_image_SMIybNTctoCqvAxgL4DcRzmenjf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524653974.png) | 目录检索筛选题目列表 |

![analyze_in_table_image_VqW9bZ9DnoipJxxj2EScgFYvnjf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524652955.png)

![analyze_in_table_image_SMIybNTctoCqvAxgL4DcRzmenjf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524653974.png)

- 学科学段检索
- 按学科、学段的关联列表选择需要学科学段。
- 默认：
- 首次进入：选中列表中第一个学段第一个学科。
- 非首次进入：选中上次选中的学段学科。
- 知识点查题
- 数据来源：展示对应学段学科线上最新版本基础树。
- 展示形式：树形结构。
- 默认不选中，展示挂在当前基础树题目的全集。
- 默认展示：
- 一级目录展开。
- 其他层级折叠。
- 交互说明：
- 点击目录节点，筛选该节点下所有题目。
- 选中节点高亮显示。
- 展示内容：
- 各级节点对应的知识点名称。
- 知识点搜索：可以考虑支持模糊搜索，允许用户通过输入知识点的关键字进行查询。
- 章节查题
- 数据来源：展示对应学段学科线上最新版本业务树。
- 展示形式：树形结构。
- 默认不选中，展示该学科学段下题目的全集。
- 默认展示：
- 一级目录展开。
- 其他层级折叠。
- 交互说明：
- 点击目录节点，筛选该节点下所有题目。
- 选中节点高亮显示。
- 展示内容：
- 节点对应的章节名称。
- 章节搜索：可以考虑支持模糊搜索，允许用户通过输入章节的关键字进行查询。
- 筛选维度
- 支持多项选择，允许用户根据多个维度来筛选题目。
- 类型，支持多选，枚举来自筛选到的题目的该字段的集合。
- 难度，支持多选，枚举值：简单、较易、中等、较难、困难。
- 年份，支持多选，支持多选，枚举来自筛选到的题目的该字段的集合。
- 省份：支持多选，如北京市、山西省、河北省枚举来自筛选到的题目的该字段的集合。
- 排序
- 最新题目：创建时间排序按题目创建的时间从新到旧。
- 最多使用：按使用量从高到低（考察哪些题目使用的较多）。
- 搜索：
- 搜索范围：筛选条件下所有的题目范围内进行搜索。
- 支持按题目名称、题目 ID 模糊搜索。
- 结果：
- 题目列表：分页展示，支持调整每页展示条数。
- 题目数量：展示筛选、搜索到的题目总数。
- 题目列表
- 分页展示，支持调整每页展示条数。
- 题目内容
- 题干：清晰展示题目的主干内容。
- 选项：展示题目的所有选项，适配不同题型（如单选、多选等）。
- 难度标签：简单、较易、中等、较难、困难。
- 答案解析（默认折叠，点击展开/收起）
- 题目 ID ：点击支持复制。
- 类型：题型如选择题、填空题等。
- 答案：展示题目的答案。
- 解析：提供该题目的详细解析。
- 知识点标签：展示该题目的知识点，第一个为主知识点，其余为次知识点。
### 试卷管理（前端不展示）

#### 用户流程 - 查看试卷

![board_NWm3wsFHqhuoA3b0pWVcudIZn1b](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524648913.png)

#### 字段说明

| 字段名 | 描述 | 枚举值/选项（待确认） |
| --- | --- | --- |
| 学科 | 学段分类 | 小学、初中、高中 |
| 学段 id | 学段对应的 id |  |
| 学科 | 学科分类 | 语文、数学、英语、物理、化学、生物、政治、历史、地理 |
| 学科 id | 学科对应的 id |  |
| 基础树目录 | 关联的基础树目录 | 基础树目录列表（例如：小学数学，初中英语等） |
| 业务树目录 | 关联的业务树目录 | 业务树目录列表（例如：教材 A，教材 B 等） |
| 试卷类型 | 试卷类型 |  |
| 年份 | 试卷所属的年份 | 2023，2024，2025，等等 |
| 地区 |  |  |
| 来源 | 试卷的来源 | 互联网，出版社，内部开发、用户上传 |
| 试卷 ID | 试卷的唯一标识 | 系统自动生成，位数由研发确定，全局唯一 |
| 试卷内容 | 试卷的具体内容 | 无 |
| 试卷包含题目 | 试卷包含的所有题目 id 的集合 | 无 |
| 题目数量 | 试卷包含的题目数量 |  |
| 知识点名称集合 | 输入题目关联的知识点名称 | 无 |
| 知识点 ID 集合 |  |  |
| 试卷状态 | 试卷的当前状态 | 待审核，审核通过 |
| 浏览量 | 试卷被浏览的数量 |  |
| 下载量 | 试卷被下载的数量 |  |
| 创建时间 | 题目创建的时间 | yyyy 年 MM 月 dd 日 HH:mm:ss |
| 更新时间 | 题目最近一次更新的时间 | yyyy 年 MM 月 dd 日 HH:mm:ss |
| 修改历史 | 系统自动记录试卷的修改历史 | 无 |

#### 需求说明

| 模块 | 原型图 | 详情 |
| --- | --- | --- |
| 资源管理 -试卷管理 | ![in_table_image_RfDhbJnCQofC5RxxPbrcWc6jnaf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524655598.png) | 试卷检索搜索筛选排序试卷查看和下载 |

![analyze_in_table_image_RfDhbJnCQofC5RxxPbrcWc6jnaf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524655598.png)

- 学科学段检索
- 按学科、学段的关联列表选择需要学科学段。
- 试卷名称搜索：考虑为搜索框添加搜索建议（如自动补全），提高搜索效率。
- 试卷 ID 搜索：需要支持精确匹配，确保用户能通过 ID 快速定位到题目。
- 支持多项选择，允许用户根据多个维度来精确筛选题目。
- 筛选维度
- 类型，枚举值：
- 年份，枚举值：
- 来源，枚举值：
- 排序维度
- 按更新时间排序：按试卷更新的时间从新到旧或从旧到新排序。
- 按下载数量排序：从高到低（考察哪些试卷下载的多）或从低到高（考察哪些试卷下载的少）。
- 排序方式：每个排序维度可以提供升序和降序两种排序方式。例如：
- 升序：从最小到最大或从最简到最难。
- 降序：从最大到最小或从最难到最简。
- 试卷的 word 文件预览
- 试卷的 word 文件下载
### 资源录入

#### 用户流程  - 录入资源流程

![board_DiYjwigHuhWbP2bWMAJcUWjqnHe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524649709.png)

#### 需求说明

| 模块 | 原型图 | 详情 |
| --- | --- | --- |
| 题目录入 | ![in_table_image_FVyzbCdDColbhSxaH9kcIesCnvD](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524657625.png) | 题目录入流程示意上传 word 文件上传历史 |

![analyze_in_table_image_UAd2b5ab5opCX7xxFCOcKK4wnhK](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524656584.png)

![analyze_in_table_image_FVyzbCdDColbhSxaH9kcIesCnvD](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524657625.png)

- 流程图：上传 word 文件 -- 后台自动识别 -- 审核 -- 数据入库。
- 上传支持
- 仅支持单文件，文件格式校验（仅限 Word 文档）。
- 上传交互
- 入口：“题目录入”模块选择【上传】。
- 选择上传文件对应的学段学科。
- 上传文件，调用本地文件管理器，选中 1 个要上传的文件，支持删除已上传的文件，点击返回退出上传流程，点击提交，上传 word 。
- 上传历史展示刚上传的 word 文件。
- 数据来源：展示用户历史上传的所有文件。
- 排序：按 word 上传的时间从近到远排序。
- 内容：
- 已上传 word 文件：展示上传 word 的名称，根据宽度自动展示，超出展示 ...
- 上传时间：格式 xxxx/xx/xx xx：xx
- 进度
- 后台自动识别环节：
- 后台自动识别中
- 识别失败重新上传
- 审核环节：
- 审核中
- 审核不通过（查看）
- 数据已入库
### 场景题库

#### 用户流程  - 选题流程（含补题）

![board_OCPqwnRy1huulcbDo6CcU4iAnEg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524650281.png)

#### 字段说明

| 字段名 | 描述 | 枚举值/选项 |
| --- | --- | --- |
| 场景题库 | 按照不同教学或练习场景组织的题库。 | AI 课、巩固练习、个性化练习 |

#### 需求说明

| 模块 | 原型图 | 详情 |
| --- | --- | --- |
| 场景题库 - 巩固练习/拓展练习 | ![in_table_image_Qhi1ba1CloOTZTxg5Y9cVJTcnCh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524660239.png) | 练习基础信息练习上架/提交审核选题编辑 |

![analyze_in_table_image_AsLtbYIyYo6GwGxxcyacFAxXnfh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524658252.png)

![analyze_in_table_image_GCcbbH4PEo0BGMxp6o0cTwpTnWd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524659216.png)

![analyze_in_table_image_Qhi1ba1CloOTZTxg5Y9cVJTcnCh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524660239.png)

![image_CbUkbbh8uoi0VGxTFNFcC4VGnoh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524660854.png)

![image_IA8ibIjKqoyXX2xLyfHcsK4Tnjc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524661814.png)

- 练习目录：
- 选择学段学科，默认选中逻辑，同上。
- 选择需要选题的业务树，展示最新的业务树。
- 业务树选择列表为树形选择
- 一级：教材版本。
- 二级：教材版本映射下的业务树。
![image_Rc8zbMkbvo0lCQxPJH1caKLZnYZ](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524662363.png)

- 业务树父级展示末级选题状态占比，格式已上架：已上架/全部，如 已上架 1/5 。
- 末级展示对应选题上架状态：已上架、未上架。
- 筛选：
- 筛选项：全部、已上架、未上架，只能同时选中一个。
- 当末级节点有一个及以上的未上架节点时，展示父节点。
- 选题详情：
- 名称：取选中的业务树末级节点名称。
- ID：对应的业务树末级节点的名称。
- 上架状态：未上架、已上架、待审核
- 选题进度：按各难度，根据当前已选择题目的数量与最低题目下限进行比对，展示选题进度。
- 已选题目展示：展示当前已选定的题目，用户可以查看所有已选择的题目以及它们的属性（同题目管理）。
- 操作：
- 上架：当练习内容已完成选题点击上架后，会进入审核，审核通过后自动将该练习上架，让其可供使用。
- 编辑：点击编辑进入选题界面，可以加入/移出题目，对于已上架的练习，任何变更均需要审核，审核通过后生效。
- 上架：首次选题后，每个难度级别必须达到最低题目数量，需点击上架，自动进入审核状态，审核通过自动完成上架；
- 提交审核：非首次选题，当已选题目发生变化（移出或新增）题目，在选题后，需点击提交审核，进入审核状态，审核通过变更在用户侧生效。
- 上架按钮校验：
![image_Q5OAbwQNAo6jwUxIgj3c2NrBn4c](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524662988.png)

- 满足最少题目数量，二次确认是否上架。
- 主标题：“是否确认上架？”. 
- 副标题：“点击确认将进入审核阶段，通过后自动完成上架。”. 
- 操作：返回（关闭弹窗），确认。
- 不满足最少题目数量
- 提示：“不满足上架题目要求数量。请添加题目后重试”。
- 操作，返回（关闭弹窗）。
- 审核撤回：
- 审核中的选题任务不可编辑
- 审核支持撤回，撤回后审核任务终止，选题任务可重新编辑
- 撤回确认：
- 主标题：“是否确认撤回？”. 
- 副标题：“点击确认将撤回审核，可以编辑选题后重新提交。”. 
- 操作：返回（关闭弹窗），确认。
- 选题进度管理：
- 每个难度级别必须达到最低题目数量（例如：简单难度不少于 xx 题，较易难度不少于 xx 题，中等难度不少于 xx 题，较难难度不少于 xx 题，困难不少于 xx 题）
- 已选题目管理：
- 入口：右上角收件箱 icon，点击展开/收起已选题目页面。
- 支持已选题目的查看、移出。
![image_ZGXsb0pyrofZOjxuubgcINdonLd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524663885.png)

- 选题界面：
- 支持按基础树、业务树查题，默认选中对应的业务树末级节点。
- 展示题库资源（已审核通过的题目），并可按照题目类型、难度等条件筛选。
- 用户可以将题库中的题目加入或移出当前练习中，每次加入自动保存。
- 上传题目快捷入口
- 已上架练习编辑
- 校验：当用户移出某难度题目小于其难度对应的最小数量时，不允许用户移出，需要先添加新题目后再移出。
- 提示弹窗
![image_TRg0b5tzVoJmTLxYT6lcNVLBnhg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524664391.png)

- 提示：“移除后不满足上架题目要求数量，请先加入题目”。
- 操作：返回，点击关闭提示弹窗。
- 操作日志（后台记录）：
- 记录用户的操作历史，包括选题、修改、审核、上架等操作。每次操作都需要生成日志，便于追溯和管理。
### 基础数据管理

#### 用户流程 - 查看基础树、业务树

![board_Dp5owS8e8hHc3bbu5JCc0P9Znob](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524650894.png)

#### 字段说明

| 字段名 | 描述 | 枚举值/选项（待确认） |
| --- | --- | --- |
| 学段 | 学段分类 | 枚举值：全部、小学、初中、高中 |
| 学段 id | 学段对应的 id |  |
| 学科 | 学科分类 | 枚举值：全部、语文、数学、英语、物理、化学、生物、政治、历史、地理 |
| 学科 id | 学科对应的 id |  |
| 基础树 ID | 基础树的唯一标识符 | / |
| 基础树名称 | 基础树的名称 | 最大显示长度：20 个字符，超出显示省略号，鼠标悬浮显示完整内容 |
| 基础树内容 | 基础树的树状结构 | / |
| 基础树映射关系 | 基础树和业务树的映射关系 | / |
| 业务树 ID | 业务树的唯一标识符 | / |
| 业务树名称 | 业务树的名称 | 最大显示长度：20 个字符，超出显示省略号，鼠标悬浮显示完整内容 |
| 业务树教材版本 | 业务树所属的教材版本 | 展示业务树对应的教材版本 |
| 业务树内容 | 业务树的树状结构 | / |
| 创建人 | 基础树、业务树的创建人 | / |
| 创建时间 | 基础树、业务树的创建时间 | yyyy 年 MM 月 dd 日 HH:mm:ss |
| 更新时间 | 基础树、业务树的更新时间 | yyyy 年 MM 月 dd 日 HH:mm:ss |
| 修改历史 | 系统自动记录的修改历史 | / |

#### 需求说明

| 模块 | 原型图 | 详情 |
| --- | --- | --- |
| 基础数据管理 - 基础树管理 | ![in_table_image_WelBbrouboRWTtx6K9WcY3Ffnlc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524664944.png) | 基础树管理 |
| 基础数据管理 - 业务树管理 | ![in_table_image_B8ZmbwXbiooSswx8dXWcxsx7nUf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524666564.png) | 业务树管理 |

![analyze_in_table_image_WelBbrouboRWTtx6K9WcY3Ffnlc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524664944.png)

- 筛选和搜索
- 学段：
- 类型：下拉单选框
- 枚举值：全部、小学、初中、高中，
- 交互说明：
- 默认显示"全部"选项
- 选择后立即触发列表刷新
- 切换学段时，学科选项同步更新
- 学科：
- 类型：下拉单选框
- 枚举值：全部、语文、数学、英语、物理、化学、生物、政治、历史、地理
- 交互说明：
- 默认显示"全部"选项
- 选择后立即触发列表刷新
- 与学段筛选联动，根据学段显示相应学科
- 搜索
- 类型：输入框 + 搜索按钮
- 支持模糊匹配
- 最大输入长度 50 个字符
交互说明：

- 输入框内显示默认提示文案："请输入基础树名称关键词"
- 点击搜索按钮或按回车键触发搜索
- 基础树列表
- 基础树 ID，支持点击复制
- 基础树名称，
- 最大显示长度：20 个字符，超出显示省略号，鼠标悬浮显示完整内容
- 学段：
- 学科：关联学段进行展示
- 线上版本，显示版本号
- 操作：查看，点击进入基础树详情页
- 分页：每页默认显示 20 条
- 排序：支持按 ID 排序
- 空状态：无数据时显示提示文案
- 加载状态：显示加载动画
- 查看基础树
- 显示基础树名称、ID、学段、学科、版本等信息
- 提供返回列表按钮
- 采用树形结构展示
- 支持功能：
- 展开/收起节点
- 显示每个节点的知识点名称
- 显示节点层级关系
![analyze_in_table_image_C8clbggbHokLLkxYuIYcmpLcnfg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524665496.png)

![analyze_in_table_image_B8ZmbwXbiooSswx8dXWcxsx7nUf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524666564.png)

- 筛选和搜索
- 学段：
- 类型：下拉单选框
- 枚举值：小学、初中、高中
- 交互说明：
- 默认显示"全部"选项
- 选择后立即触发列表刷新
- 切换学段时，学科选项同步更新
- 学科：
- 类型：下拉单选框
- 枚举值：语文、数学、英语、物理、化学、生物、政治、历史、地理
- 交互说明：
- 默认显示"全部"选项
- 选择后立即触发列表刷新
- 与学段筛选联动，根据学段显示相应学科
- 搜索
- 类型：输入框 + 搜索按钮
- 支持模糊匹配
- 最大输入长度 50 个字符
交互说明：

- 输入框内显示默认提示文案："请输入业务树名称关键词"。
- 点击搜索按钮或按回车键触发搜索
- 业务树列表
- 业务树 ID，支持点击复制
- 业务树名称
- 新增业务树教材版本
- 学段
- 学科：关联学段进行展示
- 线上版本
- 操作：查看
- 基础树列表
- 基础树 ID，支持点击复制
- 基础树名称，
- 最大显示长度：20 个字符，超出显示省略号，鼠标悬浮显示完整内容
- 学段
- 学科：关联学段进行展示
- 线上版本，显示最近更新时间
- 操作：查看，点击进入基础树详情页
- 分页：每页默认显示 20 条
- 排序：支持按 ID 排序
- 空状态：无数据时显示提示文案
- 加载状态：显示加载动画
- 查看业务树
- 显示业务树名称、ID、学段、学科、版本等信息
- 提供返回列表按钮
- 采用树形结构展示
- 支持功能：
- 展开/收起节点
- 显示每个节点的知识点名称
- 显示节点层级关系
- 后期需支持和基础树建立映射
### 审核

#### 用户流程 

![board_PbMQw4EN1hgaHzbIw6Fc6i1Unth](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524651521.png)

#### 字段说明

| 字段名 | 描述 | 枚举值/选项 |
| --- | --- | --- |
| 审核状态 | 审核人员对审核任务赋予的审核状态 | 待审核、审核通过、审核打回、审核不通过 |
| 审核时间 | 系统自动记录的审核时间 | yyyy 年 MM 月 dd 日 HH:mm:ss |

#### 需求说明

| 模块 | 原型图 | 详情 |
| --- | --- | --- |
| 审核列表 | ![in_table_image_HsB8bUm10oj2tHxG42qcnEUln9b](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524667468.png) | 审核列表：操作日志： 每次审核操作（如通过、打回、修改等）记录系统日志中，确保操作的可追溯性。日志记录包括操作人员、操作时间、审核意见等信息。数据统计与分析（后台）： 审核模块将统计每个审核任务的通过率、打回率、修改后的通过率等数据，为平台的内容优化和审核效率提升提供依据。 |
| 题目录入任务审核 | ![in_table_image_G0sMbpofjoM0BUxL9KncnwMRneh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524668663.png) | 入口： 某个题目录入待审核任务，点击去审批，点击后弹开新页面，点击返回回到审核列表。题目录入任务详情：任务状态流转： |
| 选题任务审核 | ![in_table_image_RAhqbOhnioXIyMx1ISxcAyz7nKc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524677573.png) | ![in_table_image_LDoMb8qnuoB2GCxHtcccO2r7njs](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524678684.png) |

![analyze_in_table_image_HsB8bUm10oj2tHxG42qcnEUln9b](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524667468.png)

- 筛选：
- 支持按任务类型筛选：题目录入审核和选题审核。
- 支持按审核状态筛选：待审核、审核通过、审核不通过。
- 支持输入任务 ID 搜索。
- 排序：
- 第一排序规则：待审核>已审核
- 第二排序规则：按提交时间倒序排序，最新的待审核任务排最上面。
- 任务 ID：每个审核任务的唯一标识符，便于追踪和管理。
- 任务类型： 
- 题目录入审核。
- 选题审核。
- 任务名称：
- 题目录入审核：名称取上传 word 名称。
- 选题审核：对应场景 + 名称取对应的业务树末级节点名称 。
- 学段：任务对应的学段。
- 学科：任务对应的学科。
- 提交人员：提交该审核任务的用户名，便于审核人员了解任务来源。
- 提交时间：任务提交的时间，帮助审核人员评估任务的处理时效。
- 操作：去审核，点击进入审核页面。
- 任务状态：
- 待审核：任务已提交，等待审核人员进行审核。
- 审核通过：审核人员已通过该任务，任务已完成并进入系统。
- 审核不通过：任务存在问题，审核人员选择不通过，需提交人员重新提交。
![analyze_in_table_image_UDAobB5VLo0VcjxOFSpc2mRenWg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524668074.png)

![analyze_in_table_image_G0sMbpofjoM0BUxL9KncnwMRneh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524668663.png)

![image_LaydbVoGBoToCjxH4x7cvXY9nfh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524669690.png)

![image_RIvCbAkQ1o3GAWxWFNgcQbA9nyc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524670235.png)

![image_D0JNb7F11oFAySxuiiucA3KRnYI](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524671254.png)

![image_Ci34bLXbNo3Wuux7WnecSzTSnkf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524671769.png)

![image_NuGJbPY3Uo3HWkxE3q2c3hsFn0d](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524672286.png)

![image_E1ACbJ2IPoK0vtxZgJNc134inUd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524672803.png)

- 任务名称：取上传 word 的名称，同上。
- 任务 ID：每个审核任务的唯一标识符，便于追踪和管理，同上。
- 任务内容：
- 每个 word 为一个题目录入任务，由 word 中所有的题目组成。
- 题目选择器：
- 数量，等于 word 解析出的题目数量。
- 状态：
- 未审核状态（初始状态）。
- 审核通过（绿色）。
- 审核不通过（红色）。
- 题目内容：同展示项。
- 题干：清晰展示题目的主干内容。
- 选项：展示题目的所有选项，适配不同题型（如单选、多选等）。
- 答案：展示题目的答案。
- 解析：提供该题目的详细解析。
- 知识点标签：展示该题目的知识点，第一个为主知识点，其余为次知识点（不可为空）。
- 如果为空（没有匹配到知识点或审核人员取消了关联知识点），则输入框标红，不可点击通过按钮。
- 操作项（每个题目均需要审核）
- 通过
- 不通过
- 任务操作项：
- 根据任务中的题目审核状态，确定按钮状态。
- 当任务中有题目未完成审核时，则 提交 按钮不可点击；
- 当任务中题目全部完成审核时，如果有不通过的则按钮为 提交 点击后弹窗二次确认。
- 弹窗内容：是否提交审核结果 - 不通过
- 操作：返回、确认
- 当任务中题目全部完成审核时，如果全通过的则按钮为 提交 点击后弹窗二次确认。
- 弹窗内容：是否提交审核结果 - 通过
- 操作：返回、确认
- 审核人员审核完成后，题目录入人员中上传历史进度变更为
- 审核通过，进度变更为“数据已入库”
- 审核不通过。进度变更为“审批不通过”+查看按钮
- 点击查看，弹出审核结果
![image_JdlVbcPjUoOh1YxuxjRczlvKnNb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524673414.png)

![analyze_in_table_image_AUiybnwZCoemBvxShm2cKa3Bnoe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524674412.png)

![image_HWUEbk8KMonidCxrOuocOMBrnmg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524675487.png)

![image_Ml0ibFjHooMdrMxMRGrcmNu8nTf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524676531.png)

![analyze_in_table_image_RAhqbOhnioXIyMx1ISxcAyz7nKc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524677573.png)

- 任务名称：名称取对应的场景+ "-" + 对应的业务树末级节点名称 ，同上。
- 任务 ID：每个审核任务的唯一标识符，便于追踪和管理，同上。
- 任务内容：
- 难度标签：
- 按五档难度展示对应难度下的题目，通过点击难度标签切换 tab，展示 难度名称+ 已选题目数量/最低题目要求数量。
- 题目内容：同展示项。
- 题干：清晰展示题目的主干内容。
- 选项：展示题目的所有选项，适配不同题型（如单选、多选等）。
- 答案：展示题目的答案。
- 解析：提供该题目的详细解析。
- 知识点标签：展示该题目的知识点，第一个为主知识点，其余为次知识点。
- 题目标签：
- 新增题目：在原有审核通过基础上，新增的题目。
- 首次提交审核全部为新增题目
- 移出题目：在原有审核通过基础上，移出的题目。
- 题目操作项（每个题目均需要审核）
- 标记为不通过，勾选则视为标记不通过。
- 审核任务操作项
- 当所有新增/移出的变更都没有被标记不通过，则按钮为 提交 点击后弹窗二次确认。
- 弹窗内容：是否提交审核结果 - 通过
- 操作：返回、确认
- 当有新增/移出的变更都被标记不通过，则按钮为 提交 点击后弹窗二次确认。
- 标题：不通过原因；
- 弹窗内容：“ x 道题目被标记为不通过，”+用户自定义内容
- 操作：返回、确认
- 审核撤回：场景选题支持用户撤回未审核的任务，撤回时如果审核人员正在审核，当审核人员进行下一步操作（标记、取消标记、提交）时，弹窗提示“当前审核任务已被撤回”，点击返回回到审核列表页。
![analyze_in_table_image_LDoMb8qnuoB2GCxHtcccO2r7njs](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524678684.png)

- 审核人员审核完成后，对应练习页面的审核状态变更
- 审核通过，审核中状态消失，撤销审核按钮变更为编辑。
- 审核不通过。审核中状态变更为审核不通过，撤销审核按钮变更为异常处理。
- 点击异常处理，进入异常处理页面
- 仅展示被标记为不通过的选题记录。
- 展示标签
- 用户新增但审核标记不通过的，展示【建议删除】，点击采纳删除该题，点击忽略保留该题。
- 用户删除但审核标记不通过的，展示【建议保留】，点击采纳恢复该题，点击忽略删除该题。
- 点击查看原因，弹窗展示审核不通过原因，点击返回关闭弹窗。
- 所有被标记为不通过的选题记录都被处理完，点击返回，回到选题基础信息页，按钮变更为编辑和提交审核。
- 所有被标记为不通过的选题记录没有被处理完，点击返回，回到选题基础信息页，按钮仍为处理异常。
## 六、数据需求

#### 6.1 效果追踪

描述计划监控数据效果的方式及所需数据字段

#### 6.2 数据埋点

| 页面 | 模块 | 动作 | 埋点名称 | 图示（如需） |
| --- | --- | --- | --- | --- |
|  |  |  |  |  |

## 七、a/b 实验需求

暂无



## 附：评审记录

