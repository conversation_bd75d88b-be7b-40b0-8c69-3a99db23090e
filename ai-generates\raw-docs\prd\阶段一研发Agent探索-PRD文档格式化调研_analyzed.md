阶段一：研发Agent探索 - PRD文档格式化调研

![board_MKJ0wbYfxhpyT0bxBaIcx9lnn6b](https://static.test.xiaoluxue.cn/demo41/prd_images/1747386645481.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

### 1. 图片概述与核心价值

*   **图片类型**: 流程图。
*   **核心元素**:
    此流程图名为“ADT - 银河AI驱动自动化测试（流程）”，包含五个主要阶段（Step0至Step4），每个阶段均包含若干关键产出物或处理活动。
*   **层级化结构与关联**:
    1.  **Step0 - 需求与设计文档准备**: 流程的起点，产出`PRD`和`TD`。
        *   `PRD` 和 `TD` 是后续`Step1 - AI文档智能生成`中`AI结构化`处理的输入。
    2.  **Step1 - AI文档智能生成**: 核心处理是`AI结构化`，产出`AI-PRD`和`AI-TD`。
        *   `AI-PRD` 和 `AI-TD` 是后续`Step2 - 测试用例智能生成`的输入。
    3.  **Step2 - 测试用例智能生成**: 基于AI处理后的文档，产出`API测试用例`、`APP测试用例`和`Web测试用例`。
        *   这些测试用例是后续`Step3 - 测试脚本智能生成`的输入。
    4.  **Step3 - 测试脚本智能生成**: 基于各类测试用例，产出具体的测试脚本，如`API测试脚本 (HTTP)`、`Web测试脚本 (Testim, Midscene.js)`和`APP测试脚本 (Maestro)`。
        *   这些测试脚本是后续`Step4 - 自动化测试执行与优化`的输入。
    5.  **Step4 - 自动化测试执行与优化**: 最终阶段，进行`自动化测试执行与报告分析`。
*   **核心作用与价值**:
    该流程图清晰地展示了一个利用AI技术赋能自动化测试的全过程。其核心价值在于通过AI对初始需求与设计文档进行智能化处理，进而自动化生成测试用例和测试脚本，最终实现自动化测试的执行与优化。这旨在提高测试工作的效率、准确性和覆盖度，尤其在快速迭代的互联网教育产品研发中，能够显著缩短测试周期，保障产品质量。

### 2. 功能模块拆解

*   **Step0 - 需求与设计文档准备**:
    *   `PRD` (产品需求文档): 描述产品功能、用户场景和业务逻辑的文档。
    *   `TD` (技术设计文档): 描述产品技术实现方案、架构设计、接口定义等的文档。
*   **Step1 - AI文档智能生成**:
    *   `AI结构化`: 利用AI技术对输入的PRD和TD进行语义理解、信息提取和结构化处理。
    *   `AI-PRD`: 经过AI结构化处理后的产品需求文档，信息更规整，易于机器读取和分析。
    *   `AI-TD`: 经过AI结构化处理后的技术设计文档，同样提升了信息的规整性和可分析性。
*   **Step2 - 测试用例智能生成**:
    *   `API测试用例`: 基于AI-PRD和AI-TD（尤其是接口定义部分）生成的针对API接口的功能、性能、安全等方面的测试场景和验证点。
    *   `APP测试用例`: 基于AI-PRD（尤其是用户交互流程、功能点）生成的针对移动端应用的功能、UI、兼容性等方面的测试场景和验证点。
    *   `Web测试用例`: 基于AI-PRD（尤其是用户交互流程、功能点）生成的针对Web应用的功能、UI、兼容性等方面的测试场景和验证点。
*   **Step3 - 测试脚本智能生成**:
    *   `API测试脚本 (HTTP)`: 基于API测试用例，自动生成的可以直接执行的API测试代码，通常使用HTTP协议进行交互。
    *   `Web测试脚本 (Testim, Midscene.js)`: 基于Web测试用例，自动生成的Web端自动化测试脚本，可能使用了Testim、Midscene.js等特定测试框架或工具。
    *   `APP测试脚本 (Maestro)`: 基于APP测试用例，自动生成的移动端自动化测试脚本，可能使用了Maestro等特定测试框架或工具。
*   **Step4 - 自动化测试执行与优化**:
    *   `自动化测试执行与报告分析`: 执行生成的各类自动化测试脚本，收集测试结果，并对结果进行分析，生成测试报告，可能包含对测试过程和脚本的优化建议。

### 3. 服务端功能与数据需求

服务端需要支持以下功能并处理相应数据：

1.  **文档管理与存储**：服务端需要提供存储和管理原始需求与设计文档（PRD、TD）的功能，并允许后续流程访问这些文档内容。
2.  **AI文档结构化处理服务**：服务端需提供AI能力，接收PRD和TD文档内容作为输入，进行智能分析和结构化处理，然后输出并存储结构化后的AI-PRD和AI-TD内容。
3.  **测试用例生成服务**：服务端需提供基于AI-PRD和AI-TD内容生成测试用例的功能。此服务接收结构化的文档内容，根据预设规则和AI模型，输出并存储API测试用例、APP测试用例和Web测试用例。
4.  **测试脚本生成服务**：服务端需提供将测试用例转换为可执行测试脚本的功能。此服务接收API、APP、Web测试用例数据，结合指定的测试工具或框架（如HTTP客户端、Testim、Midscene.js、Maestro），输出并存储相应的API测试脚本、Web测试脚本和APP测试脚本。
5.  **自动化测试执行与结果分析服务**：服务端需提供执行自动化测试脚本的环境或调度能力，并能够收集测试执行过程中的日志和结果数据。之后，对这些数据进行聚合、分析，并最终输出测试报告内容。


【============== 图片解析 END ==============】



PRD是产品生命的萌芽，创新的开端，万里长城的设计图。AI基于PRD构建的蓝图之上，继续添砖加瓦，那如何让AI可以正确认识PRD，就显得尤为重要。



## 一、 PRD直接生成测试case

将PRD、技术设计、数据库结构、API接口通过dify工作流的形式都喂给AI

Dify: https://dify.test.xiaoluxue.cn/app/e53030f7-4c1c-46ff-b14f-825f69b763b5/workflow

测试case：

[deepseek-v3-250324.md](https://wcng60ba718p.feishu.cn/wiki/Yruzwbf8IiRy25kKhlKc4qcznQg)



## 二、 PRD生成功能点

从PRD总结功能点

[claud3.7-thinking.txt](https://wcng60ba718p.feishu.cn/wiki/IzKjwVrsEiDprzkQivDcIyDfnKb)



## 三、AI喜欢的形状



### Markdown



1. 结构化与机器友好性
- 层级清晰​：通过 #、-、``` 等简单符号标记标题、列表、代码块，AI 生成的文本逻辑性更强，便于机器解析和人类阅读。
- 标准化语法​：Markdown 的固定规则（如 加粗）让 AI 更容易生成格式统一的内容，减少歧义。
1.  轻量化与高效性
- 低计算成本​：相比富文本（如 Word），MD 文件体积小，节省 AI 模型的 token 消耗和传输资源。
- 快速渲染​：纯文本格式可直接转换为 HTML/PDF，无需复杂排版处理。
1.  跨平台兼容性
- 无缝适配​：MD 文件可在 GitHub、Notion、博客等平台直接使用，AI 生成的内容能一键嵌入工作流。
- 多设备支持​：手机、电脑、平板均能流畅显示，无需依赖特定软件。
1.  人机协作优化
- 双向友好​：人类可快速编辑 MD 原始文本，AI 也能通过标记理解内容结构（如将 ##识别为二级标题）。
- 扩展性强​：支持数学公式（$E=mc^2$）、流程图（Mermaid）、表格等高级功能，满足技术文档需求。


飞书使用的也是 Markdown 格式

**推荐工具**：https://typora.io/

**快速入门**  菜鸟教程

### 纯文本

- 优点：简单通用
- 缺点：简单通用
Markdown 就是结构化的纯文本

### Word、PDF

支持图文双排，但对AI和编码不友好



## 四、 导出PDF转MD

![image_D5YvbB9bboanXaxckmAcEGIwn9e](https://static.test.xiaoluxue.cn/demo41/prd_images/1747386647698.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

1.  该图片为一份**功能操作列表**截图，出自产品需求文档。它展示了文档处理或协作平台中，针对单个文档可执行的一系列操作。
    *   **关键元素**：图片中的关键元素是各项操作命令，如“分享”、“编辑”、“下载为”等。
    *   **组成部分**：主要由一系列并列的功能按钮/菜单项组成，其中“下载为”选项下级联了“Word”和“PDF”两个子选项，表明存在层级关系。
    *   **层级化结构与关联**：
        *   顶层：包含分享、编辑、页面设置、演示、关注、查找替换、快捷方式、移动、置顶、收藏、模板转换、副本创建、下载、翻译、打印等直接操作。
        *   次层：“下载为”功能下，包含具体的下载格式选项（Word, PDF）。
        这些元素共同构成了一个围绕文档的操作集合。
    *   **核心作用与价值**：在互联网教育产品中，此类功能菜单是用户与教学文档、学习资料进行交互的核心界面。它使用户能够便捷地管理、编辑、分发和消费内容，支持协作学习、个性化学习材料制作、以及教学成果的多种格式输出，提升了教育资源的使用效率和教学活动的灵活性。

2.  图片组成部分拆解及功能模块概述：
    *   **分享 (Share)**: 允许用户将当前文档分享给其他人。
    *   **编辑 (Edit)**: 用户对文档内容进行修改和编辑。
    *   **页宽设置 (Page Width Settings)**: 调整文档在视图中的显示宽度。
    *   **演示模式 (Presentation Mode)**: 以全屏或优化阅读的模式展示文档内容。
    *   **关注文档更新 (Follow Document Updates)**: 订阅文档的变更通知，当文档被修改时用户会收到提醒。
    *   **查找和替换 (Find and Replace)**: 在文档中搜索特定文本，并可选择替换为其他文本。
    *   **添加快捷方式到 (Add Shortcut to)**: 在指定位置创建指向此文档的快捷方式，方便快速访问。
    *   **移动到 (Move to)**: 将文档从当前位置移动到其他文件夹或位置。
    *   **添加到“置顶” (Add to Pinned/Top)**: 将文档标记并在特定列表（如常用列表）中置顶显示。
    *   **添加到收藏 (Add to Favorites)**: 将文档加入用户的个人收藏列表。
    *   **转换为模板 (Convert to Template)**: 将当前文档的结构和样式保存为一个新模板，供后续创建同类文档使用。
    *   **创建副本 (Create a Copy)**: 复制当前文档，生成一个内容相同的新文档。
    *   **下载为 (Download as)**: 将文档下载到本地。
        *   **Word**: 以 Microsoft Word 格式 (.doc/.docx) 下载文档。
        *   **PDF**: 以 PDF 格式下载文档。
    *   **翻译为印地语 (Translate to Hindi)**: 将文档内容翻译成印地语。
    *   **打印 (Print)**: 将文档发送到打印机进行打印。

3.  服务端需提供的功能和返回的数据内容：
    *   为实现**分享**功能，服务端需要接收文档标识和分享参数（如分享对象、权限等），处理分享逻辑，存储分享状态，并可能生成和返回分享链接或通知信息。
    *   为支持**编辑**功能，服务端需要能够锁定文档以防止冲突（如果需要），接收用户提交的文档内容更新，并保存这些更改。服务端需返回操作成功的状态。
    *   对于**页宽设置**，如果此设置为用户或文档的持久化偏好，服务端需要存储和读取该文档的页宽偏好设置信息。
    *   对于**演示模式**，服务端可能需要提供一个适合演示的文档数据版本或视图配置。
    *   **关注文档更新**功能要求服务端记录用户对特定文档的关注状态，并在文档更新时向关注者发送通知。服务端需返回关注操作成功的状态。
    *   **查找和替换**功能可能需要在服务端执行，特别是对于大型文档或需要记录历史的场景。服务端需要接收文档标识、查找词、替换词，执行操作，并返回操作结果或更新后的文档内容片段。
    *   **添加快捷方式到**功能，服务端需要接收文档标识和目标位置信息，创建快捷方式链接的记录。
    *   **移动到**功能，服务端需要接收文档标识和新的目标位置标识，更新文档的存储位置元数据。
    *   **添加到“置顶”**功能，服务端需要接收文档标识和用户标识，记录文档的置顶状态。
    *   **添加到收藏**功能，服务端需要接收文档标识和用户标识，记录文档的收藏状态。
    *   **转换为模板**功能，服务端需要接收文档标识，复制文档内容和结构，并将其标记为模板类型，存储为新的模板资源。
    *   **创建副本**功能，服务端需要接收源文档标识，完整复制文档数据及相关元数据，生成一个具有新标识的独立文档。
    *   **下载为**特定格式（Word, PDF）功能，服务端需要接收文档标识和目标格式，将文档内容转换为指定的格式，并提供转换后的文件数据流供用户下载。
    *   **翻译为印地语**功能，服务端需要接收文档标识和目标语言（印地语），调用翻译服务处理文档内容，并返回翻译后的内容或翻译任务的状态。
    *   **打印**功能，服务端可能需要生成一个适合打印的文档版本数据，供客户端渲染或直接处理打印任务。


【============== 图片解析 END ==============】



从飞书导出PDF和Word，解析后，交给AI识别，生产出 MD

导出文档：[运营后台&用户中心-技术设计](https://wcng60ba718p.feishu.cn/wiki/WL6ewuBWCi23DbkirNIcVde8nCc)

**图片识别后MD**

[gemini-2.5-pro-preview-03-25-2.md](https://wcng60ba718p.feishu.cn/wiki/NS9bwYFr6ige9MkueAHczxrJnGb)



**缺陷**

- 无格式
- 收不住AI


## 五、飞书转Markdown

保存格式的前提下，转换成 Markdown



- 浏览器插件
https://github.com/whale4113/cloud-document-converter
解析HTML，组装md



- 第三方库
https://github.com/Wsine/feishu2md

- 缺陷
有格式无图片



- 飞书API
放弃AI解析，用官方工具

飞书开发文档



## 六、大模型解析图片

**Prompt**

**Mermain**



示例图片：https://static.test.xiaoluxue.cn/demo41/prd_images/1746522656734.png

解析结果

- 
加入OCR

- 
![image_HJhxbzf4gofUbex6KsUccW2nn5d](https://static.test.xiaoluxue.cn/demo41/prd_images/1747386649580.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

该图片展示的是一段程序运行的日志输出，记录了一个 Python 脚本 (`parse_feishu.py`) 执行的完整过程。该脚本的核心功能是从指定的飞书文档链接中提取内容，将其转换为 Markdown 格式，并对 Markdown 文件中包含的图片进行解析处理。

1.  **图片类型与核心价值解析**

    *   **图片类型**: 命令行日志截图，实质上可视为一个**程序执行流程图**的文本化表现。
    *   **关键元素与组成部分**:
        *   **Initiation (初始化)**: `python parse_feishu.py [Feishu URL]` - 脚本启动命令，输入参数为飞书文档链接。
        *   **Feishu Document Processing (飞书文档处理)**:
            *   `开始下载并转换飞书文档` - 标志着文档处理阶段的开始。
            *   `解析响应失败：'page_token'` - 一条错误日志，指示在与飞书 API 交互时，可能在处理分页数据（page_token）时遇到问题。
            *   `开始处理 X 块白板...` - 表明脚本能够识别并处理飞书文档中的白板内容。
            *   `第 N/X 块白板处理完成` - 逐个处理白板的进度反馈。
            *   `文档已成功转换为Markdown：[Path to raw.md]` - 飞书文档到 Markdown 转换成功，并输出原始 Markdown 文件路径。
        *   **Markdown Image Processing (Markdown 图片处理)**:
            *   `开始处理Markdown文件中的图片` - 标志着图片处理阶段的开始。
            *   `找到 X 张图片` - 识别出 Markdown 文件中待处理的图片数量。
            *   `进度：[M/X] 第 Y 张图片解析完成` - 逐个解析图片的进度反馈。
            *   `文件处理完成，已保存到：[Path to analyzed.md]` - 所有图片处理完毕，输出最终分析后的 Markdown 文件路径。
        *   **Termination (终止)**:
            *   `文档处理成功，结果保存到：[Path to analyzed.md]，耗时：XX.XX秒` - 脚本执行完毕，报告最终结果和总耗时。
    *   **层级化结构与关联**:
        1.  **顶层**: 脚本执行 (`parse_feishu.py`)。
        2.  **第二层**:
            *   飞书文档获取与转换模块。
            *   Markdown 内容（特别是图片）解析模块。
        3.  **第三层 (飞书文档处理子模块)**:
            *   文档下载。
            *   基础内容转换。
            *   白板内容处理。
            *   错误处理（如 `page_token` 问题）。
        4.  **第三层 (Markdown 图片处理子模块)**:
            *   图片发现。
            *   逐个图片解析。
    *   **核心作用与价值 (结合互联网教育领域)**:
        *   **自动化教研资料转换**: 在互联网教育领域，教研团队常使用飞书文档撰写教案、PRD、产品设计稿等。此脚本能自动化地将这些飞书文档（包含文字、白板图示）转换为通用的 Markdown 格式，便于版本控制、跨平台分享和二次加工。
        *   **智能化内容分析**: 通过对 Markdown 中的图片进行“解析”，可能意味着提取图片中的文本 (OCR)、识别图表类型、或对教学示意图进行结构化理解。这对于构建智能知识库、教学内容分析、甚至自动化生成练习题等具有重要价值。
        *   **提升协作效率**: 自动化的文档格式转换和初步内容解析，可以减少人工重复劳动，提升教研、产品、技术团队间的协作效率。
        *   **知识沉淀与复用**: 将动态的飞书文档固化为结构化的 Markdown 及分析结果，有助于知识的长期沉淀和高效复用。例如，PRD文档中的流程图、架构图可以被解析并索引，方便后续检索和理解。

2.  **功能模块拆解**

    *   **脚本执行与参数解析模块**:
        *   功能概述: 接收命令行传入的飞书文档 URL 作为输入，启动整个处理流程。
    *   **飞书文档下载与转换模块**:
        *   功能概述: 根据提供的 URL 从飞书服务器下载文档内容，处理文档结构（包括内嵌的白板），并将其转换为 Markdown 格式。
    *   **飞书 API 交互与错误处理模块**:
        *   功能概述: 负责与飞书开放平台 API 通信以下载文档数据，并处理可能发生的 API 错误（如日志中显示的 `page_token` 解析失败）。
    *   **白板内容处理模块**:
        *   功能概述: 专门处理飞书文档中嵌入的白板内容，将其转换为适合在 Markdown 中展示或进一步分析的格式。
    *   **Markdown 文件生成与存储模块 (原始)**:
        *   功能概述: 将转换后的飞书文档内容保存为 `.md` 文件到指定的本地路径。
    *   **Markdown 图片识别模块**:
        *   功能概述: 扫描生成的 Markdown 文件，识别并定位其中所有引用的图片。
    *   **图片内容解析模块**:
        *   功能概述: 对识别出的每张图片执行解析操作。具体的解析行为未在日志中详述，但可能包括 OCR、对象识别、图表分析等。
    *   **Markdown 文件更新与存储模块 (分析后)**:
        *   功能概述: 将图片解析的结果（可能以文本或其他形式）整合回 Markdown 文件或另存，并将最终处理完毕的 Markdown 文件保存到指定的本地路径。
    *   **日志记录与状态反馈模块**:
        *   功能概述: 在整个处理过程中，输出详细的日志信息，包括 INFO 级别的进度、ERROR 级别的错误，以及最终的处理结果和耗时。

3.  **服务端需提供的功能和返回的数据内容**

    基于日志中脚本与飞书的交互以及后续的图片处理，服务端（主要是指飞书服务器，以及潜在的图片解析服务）需要提供以下功能和数据内容：

    *   **飞书文档数据获取功能**:
        *   服务端需要能够根据客户端（即 `parse_feishu.py` 脚本）提供的文档唯一标识（如URL中的 `028fwaQ5uijuieMkawesceyvCnnV`），返回该文档的完整内容。
        *   返回的文档内容数据需要包含所有文本信息。
        *   返回的文档内容数据需要包含文档的结构信息，例如标题层级、列表、段落等，以便能够转换为 Markdown 格式。
        *   返回的文档内容数据需要包含所有嵌入式对象的信息，特别是图片和白板。
        *   对于图片，需要提供图片的直接访问链接或其元数据。
        *   对于白板，需要提供构成白板的所有元素的详细数据，如形状、文本、连接线、颜色、位置等，以便客户端能够处理或转换这些白板内容。
        *   如果文档内容过大，服务端需要支持分页返回数据，并提供用于获取下一页数据的标识（如日志中提及的 `page_token`）。当客户端请求下一页数据时，服务端需要根据此标识返回相应部分的内容。
    *   **错误信息反馈**:
        *   当无法成功返回文档数据时（例如文档不存在、无权限访问、请求参数错误如无效的 `page_token`），服务端需要返回明确的错误代码和错误信息。
    *   **（潜在）图片解析服务**:
        *   如果图片解析是调用了远端服务，该服务端需要提供接收图片数据（可能为图片URL或二进制流）的接口。
        *   该服务端需要返回对图片的解析结果，具体结果内容依据解析任务而定（例如，若是OCR，则返回提取的文本；若是图表识别，则返回图表类型和数据）。

4.  **Mermaid 流程图描述**

    ```mermaid
    flowchart TD
        A[开始: 执行 python parse_feishu.py 携带飞书文档URL] --> B(下载并转换飞书文档);
        B -- API请求 --> FS[飞书服务器];
        FS -- 文档数据/错误 --> B;
        B -- 成功 --> C{处理文档中的白板内容};
        C -- 遍历所有白板 --> C_LOOP[处理单个白板];
        C_LOOP -- 完成 --> C_NEXT(下一个白板或完成);
        C_NEXT -- 所有白板处理完成 --> D[生成并保存原始Markdown文件];
        B -- 'page_token'解析等API错误 --> ERR_FEISHU[记录飞书API错误];
        D --> E(开始处理Markdown文件中的图片);
        E --> F[扫描Markdown文件以查找图片];
        F -- 找到X张图片 --> G{逐个解析图片};
        subgraph 图片处理循环
            direction LR
            G_ITEM[处理第N张图片]
        end
        G -- 遍历所有图片 --> G_ITEM;
        G_ITEM -- 解析完成 --> G_NEXT(下一张图片或完成);
        G_NEXT -- 所有图片解析完成 --> H[更新/保存包含图片解析结果的Markdown文件];
        H --> I[记录文档处理成功信息及耗时];
        I --> Z[结束];
        ERR_FEISHU --> Z;
    ```

【============== 图片解析 END ==============】



## 七、工程化

用Go将所有的依赖打包，点击一下、就能使用

- 解决依赖问题
- 完善解析飞书格式
- 网页化


## 八、 安全性

**涉及密钥**

- 飞书密钥
- 大模型密钥


**解决**

- 与大模型交互的密钥不存储在工具中，单独起一个和模型交互服务，只允许内网访问。
- http://172.16.31.74:8095/api/v1/llm/chat
- 内网网页化。


