---
description: 
globs: backend/**/*.py
alwaysApply: false
---
# FastAPI Router 代码生成规则

## 边界
- 通过 `Depends(get_<app>_app_service)` 注入 `AppService`  
- 禁止直接访问仓储或领域逻辑  
- 返回 `api_response` JSON：`{"data": {...}, "code": 0, "error": ""}`，状态码统一 200  
- 业务错误可直接抛自定义异常，由全局异常处理器统一映射；无需在每个路由内 `try/except`  
- 每次请求生成/透传 `X-Trace-Id`，在入口/出口 `logger.info` 

## 约定
- 路由文件命名：每个聚合一个 `*_router.py`  
- 路由前缀统一 `/api/v{version}/{resource}`  
- 函数命名采用 `<verb>_<noun>`  
- 请求/响应模型使用 `pydantic`  
- 成功状态码统一 `200`，业务错误返回 `200` + `code != 0` 
- 日志在入口出口调用 `logger.info` 
- 耗时操作使用 `BackgroundTasks` 

## 示例代码
```python
router = APIRouter()

@router.post("/login")
def login_user(
    request: Request,
    body: UserLoginRequest,
    svc: AuthAppService = Depends(get_auth_app_service),
):
    user, token = svc.login(body.username, body.password, request.client.host)
    return api_response(data={"user_id": user.user_id, "token": token.token})
```