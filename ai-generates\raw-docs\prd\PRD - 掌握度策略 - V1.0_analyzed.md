PRD - 掌握度策略 - V1.0

**版本管理**

| 版本号 | 日期 | 变更人 | 变更类型 | 变更详情 |
| --- | --- | --- | --- | --- |
| V1.0 | 2025-04-11 | 钱晋菲 | 新建 | 新建文档 |
| V1.0.1 | 2025-05-12 | 钱晋菲 | 更新 |  |
|  |  |  |  |  |

1. 外化掌握度
1. 更新计算方式，由星星改成百分比展示
1. 掌握度更新时机，中途退出学习不更新
1. 补充对特殊情况的处理
1. 目标掌握度
1. 更新各维度系数
1. 换算成百分比，按0.05向上取整
**关联需求**

| 关联需求名称 | 所属 PM | 需求进度 | 文档链接 |
| --- | --- | --- | --- |
| AI课中 V1.0 |  |  |  |



# 一、背景和目标

##  需求背景

在自适应学习系统中，掌握度是非常重要的功能模块，是未来各种推荐策略的基础输入。

1. 精准评估学习状况：掌握度能够精确反映学生对各个知识点的理解和掌握程度。让学生和教师都能清楚了解学生在学习过程中的优势和不足。
1. 优化学习路径：巩固练习和拓展练习都需要依据掌握度来为学生量身定制学习路径，推送适合该学生的学习内容和练习题目。


## 项目收益

1. 提升产品体验，让用户感受到系统的个性化和智能性
1. 提高学习效率，避免重复学习已经掌握的内容
1. 是巩固练习和拓展练习等推荐策略的前置输入


## 覆盖用户

使用新AI课学习的全部用户



# 二、概念厘清

## 掌握度的定义

掌握度是衡量学生对特定知识点理解与运用程度的关键指标。在新AI课系统中定义了多种类型的掌握度：

1. 学科能力（用户分层）：量化学生在某个学科的能力水平，用于学生分层&学习路径规划。仅基于考试数据更新。
1. 知识点掌握度：量化学生对某个知识点的熟练程度，用于动态调整学习内容和路径，在每次答题后更新。通常和用户的答题表现（正确率、知识点难度、错误模式）、行为数据（答题时长、尝试次数、学习间隔时长）等因素相关。
1. 外化掌握度：用于向学生展示对于单节课程的掌握程度，牵引用户学习。在学生完成AI课、巩固练习、作业、测试等学习任务后更新。
由于知识点掌握度是一个动态变化的指标且和多种因素相关不便于用户理解，因此在向用户展示掌握度时采用简化的和答题正确率&题目难度更相关的计算方式，降低用户理解成本。

1. 目标掌握度：在目标设定阶段，系统依据学生设定的目标，推导出学生在每节课应达到的外化掌握度。
|  | 学科能力（用户分层） | 知识点掌握度 | 外化掌握度（星星） | 目标掌握度 |
| --- | --- | --- | --- | --- |
| 评估对象 | 学生在某个学科的能力水平 | 学生对某个知识点的掌握程度 | 学生对单节课程的掌握程度 | 根据目标推导出学生对单节课程应该达到的掌握程度 |
| 目的 | 学生分层&学习路径规划 | 动态调整学习内容和路径 | 激励手段，牵引用户学习 |  |
| 更新时机 | 前期每次考试后更新后续根据知识点掌握度计算 | 每次答题后更新 | 完成AI课、巩固练习、作业、测试等学习任务后更新 | 用户设置/修改目标后更新 |
| 计算方式 | 冷启动阶段基于策略计算后续模型直接输出 |  | 基于策略计算 |  |



## 内容结构

掌握度结算仅考虑业务树节点

![board_Llxiw3ZzGhTPlyb3QLtcfVkhnLf](https://static.test.xiaoluxue.cn/demo41/prd_images/1747049196585.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

作为互联网产品经理专家，我们来解析这份PRD文档中关于“掌握度策略V1.0”的图片。

1.  **图片类型、关键元素、结构、核心作用与价值**

    *   **图片类型**：该图片是一张 **层级结构图**（或称为知识树状图）。
    *   **关键元素**：
        *   业务树 (顶层概念)
        *   一级知识点 (A, B, C)
        *   二级知识点 (a, b)
        *   三级知识点 (1, 2)
    *   **组成部分与层级化结构**：
        *   该结构以“业务树”为根节点或总体框架。
        *   “业务树”下包含多个“一级知识点”（如 A, B, C）。
        *   每个“一级知识点”下可以包含若干“二级知识点”（如“一级知识点 A”下包含“二级知识点a”和“二级知识点b”）。
        *   每个“二级知识点”下可以包含若干“三级知识点”（如“二级知识点a”下包含“三级知识点1”和“三级知识点2”）。
        整体呈现为一个从 общего (业务树) 到具体 (三级知识点) 的树状层级结构。
    *   **核心作用与价值**：
        在“掌握度策略”的需求文档中，此层级结构图的核心作用是**定义知识的组织框架和细分粒度**。
        *   **作用**：它清晰地展示了知识点是如何从宏观到微观逐级拆分的，为后续的教学内容设计、学习路径规划、掌握度评估以及个性化推荐等功能提供了基础蓝图。
        *   **价值**：
            *   **结构化知识体系**：使得复杂的知识体系变得有序和易于管理。
            *   **精准定位**：能够帮助系统和用户精准定位到具体的学习单元和考核点。
            *   **支持度量**：为后续量化学生对各层级知识点的掌握程度提供了明确的考核对象。
            *   **扩展性**：这种结构易于未来知识点的增删和调整。

2.  **各组成部分功能模块拆解与概述**

    *   **业务树**：
        *   **功能概述**：代表整个知识体系或业务领域的顶层框架，是所有知识点的根目录或总纲。
    *   **一级知识点 (例如：一级知识点 A, 一级知识点 B, 一级知识点 C)**：
        *   **功能概述**：知识体系中的主要分支或核心模块，对业务树下的知识领域进行初步划分，代表较大的知识单元。
    *   **二级知识点 (例如：二级知识点a, 二级知识点b)**：
        *   **功能概述**：对一级知识点的进一步细化，将较大的知识单元分解为更具体、更易于学习和理解的子模块或主题。
    *   **三级知识点 (例如：三级知识点1, 三级知识点2)**：
        *   **功能概述**：知识体系中最细致的知识单元或具体的技能点，通常是学习和考核的最小单位，直接对应具体的学习目标或考核内容。

3.  **服务端需提供的功能和返回的数据内容描述**

    服务端需要能够提供整个知识点层级结构的数据。具体而言：
    *   服务端需要能够返回完整的“业务树”结构，包含所有层级的知识点信息。
    *   对于树中的每一个节点（无论是一级、二级还是三级知识点，乃至业务树本身），服务端需要提供该节点的唯一标识、名称或标题。
    *   服务端需要明确定义并能够返回各个知识点之间的父子层级关系。例如，能够查询指定一级知识点下的所有二级知识点，以及指定二级知识点下的所有三级知识点。
    *   服务端需要支持从任意一个知识点向上追溯其所有父节点的路径信息。
    *   服务端需要能够提供一个列表，包含所有的一级知识点。
    *   服务端需要支持根据父知识点的标识，查询其直接子知识点列表的功能。

4.  **Mermaid 流程图描述**

    ```mermaid
    graph TD
        Root[业务树] --> L1A[一级知识点 A]
        Root --> L1B[一级知识点 B]
        Root --> L1C[一级知识点 C]
    
        L1A --> L2a[二级知识点a]
        L1A --> L2b[二级知识点b]
    
        L2a --> L3_1[三级知识点1]
        L2a --> L3_2[三级知识点2]
    ```

【============== 图片解析 END ==============】



- 一节课等于一个知识点：业务树的末级节点 = 单节课
- 题目和课关联：1道题目可关联多节课，不存在没有关联课的题目。
![board_UeQ1wSFkYhjvkPbbXOac47DQnch](https://static.test.xiaoluxue.cn/demo41/prd_images/1747049196988.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

根据您提供的图片及其OCR文本内容，这是一份关于“PRD - 掌握度策略 - V1.0”文档中的概念示意图。

1.  **图片解析与核心价值**

    *   **图片类型**: 此图片可识别为 **概念示意图** 或 **核心要素关联图**。它出自产品需求文档，用于直观展示“掌握度策略”所涉及的核心实体。
    *   **关键元素与组成部分**:
        *   **题目 (Questions)**: 代表用于评估用户掌握程度的工具或载体。具体实例包括“题目1”、“题目2”、“题目3”、“题目4”。
        *   **课程与知识点组合 (Courses with associated Knowledge Points)**: 代表学习内容的基本单元和衡量掌握度的对象。具体实例包括“课程1 (知识点1)”、“课程2 (知识点2)”。这里，知识点是内嵌或紧密关联于课程的。
    *   **元素间关联 (层级化结构)**:
        *   图片将“题目”和“课程(知识点)”作为两大类核心元素并列展示。
        *   在“掌握度策略”的业务背景下，它们之间存在着明确的关联：“题目”是用来检验和衡量学习者对“课程”中所包含的“知识点”的掌握情况。即 “题目”评估“课程(知识点)”。
        *   虽然图中未显式绘制连接线表明具体某个题目对应哪个课程/知识点，但其并列结构和文档主题（掌握度策略）清晰地暗示了这种评估与被评估的关系。
    *   **核心作用与价值**:
        *   **明确评估对象与工具**: 该图清晰地列出了掌握度策略中涉及的两类核心实体：评估工具（题目）和评估内容/对象（课程及其包含的知识点）。
        *   **奠定策略基础**: 为后续定义具体的掌握度计算逻辑、题目与知识点的精确映射规则、以及用户学习路径规划等功能需求奠定了基础。它帮助团队在初期就对策略的核心构成要素达成共识。

2.  **功能模块拆解**

    *   **题目模块**:
        *   **简要功能概述**: 提供一系列用于测试和评估用户对特定知识点掌握情况的试题。这些题目是衡量学习效果和掌握度的直接手段。
    *   **课程与知识点模块**:
        *   **简要功能概述**: 组织和呈现教学内容。课程作为学习的大纲或单元，其内部包含具体的知识点，这些知识点是用户需要学习和掌握的核心内容，也是题目评估的目标。

3.  **服务端需提供的功能和数据内容**

    服务端需要能够提供以下数据信息：
    *   关于课程的详细信息。
    *   对于每一个课程，需要能够提供其直接关联或包含的知识点信息。
    *   关于题目的详细信息。
    *   题目与课程或知识点之间的关联关系信息，即明确每个题目是针对哪个或哪些课程/知识点进行考核的。
    *   （虽然图片未直接展示，但“掌握度策略”隐含）可能需要提供用户的答题记录以及基于这些记录计算出的针对特定知识点或课程的掌握度状态信息。

4.  **Mermaid 图表描述**

    由于图片内容主要是功能模块/实体的列举，且上下文为“掌握度策略”，最能体现其关系的图表为流程图（Flowchart Diagram），用以表示题目对课程/知识点的评估关系。

    ```mermaid
    graph TD
        subgraph AssessmentTargets [评估对象]
            direction LR
            CKP1["课程1 (知识点1)"]
            CKP2["课程2 (知识点2)"]
        end
    
        subgraph AssessmentTools [评估工具]
            direction TB
            Q1["题目1"]
            Q2["题目2"]
            Q3["题目3"]
            Q4["题目4"]
        end
    
        AssessmentTools -- "用于评估" --> AssessmentTargets
    ```

【============== 图片解析 END ==============】





# 三、策略方案

## 学科能力（用户分层）

**冷启动阶段计算思路：**

1. 锚定学校在全国范围内的大致水平
1. 校准不同届的生源质量
1. 基于学生各科成绩在学校的排名&学校在全国范围的水平进行分层
### 数据准备

| 数据类型 | 字段 | 字段说明 |
| --- | --- | --- |
| 学生信息 | 学生ID | 和AI课系统一一对应的学生ID |
|  | 各学科年级排名百分比 | 如果拿到的是学生排名信息，需要转换为排名百分比。 |
| 学校信息 - 仅高中收集 | 学校近3年的高考本科录取率 |  |
|  | 学生中考成绩全省/市排名或一分一段表 |  |

1. 学生单科排名
1. 年级人数
示例：年级50名 / 年级共200名学生 → 排名百分比=25% 。代表这名学生在年级所有学生中处于前 25% 的位置。

1. 高考年份
1. 对应年份的本科录取率
1. 对应高考年份的学生的中考排名
1. 本届学生的中考排名
注：每届新生都需要收集对应信息，重新计算下文中的各类系数

### 计算方式

#### 高中

1. 锚定学校水平
根据学校历届高考的平均本科录取率，对应到不同的校准系数a

| 本科录取率区间 | [90,100] | [80,90) | [60,80) | [40,60) | [20,40） | [0,20) |
| --- | --- | --- | --- | --- | --- | --- |
| 校准系数a | 1 | 1.2 | 1.3 | 1.5 | 2 | 3 |

如果学校未提供录取率，可简化为按学校类型设置系数

| 学校类型 | 重点中学 | 普通中学 | 较差中学 |
| --- | --- | --- | --- |
| 校准系数a | 1 | 1.3 | 3 |

1. 校准生源波动
根据学校中考排名计算校准系数b，如果学校没有提供排名数据，则校准系数默认为1

**情况一：**学校近3年高考本科录取率的相对变化小于10%

- 校准系数b = √（历年排名中位数的倒数的平均 / 今年排名中位数的倒数）
示例：

|  | 今年 | 2024 | 2023 | 2022 |
| --- | --- | --- | --- | --- |
| 排名中位数 | a | b | b' | b'' |
|  | 150 | 210 | 189 | 203 |

校准系数
$$
b = \sqrt{ \frac{\left( \frac{1}{b} + \frac{1}{b'} + \frac{1}{b''} \right)}{3} \div \frac{1}{a}} = \sqrt{ \frac{\left( \frac{1}{210} + \frac{1}{189} + \frac{1}{203} \right)}{3} \div \frac{1}{150}} \approx 0.82
$$



**情况二：**学校近3年高考本科录取率的相对变化大于10%，仅采用一年的分数进行校准（人工指定）



1. 确定用户分层
- 学科能力排名 = 用户单科排名百分比 * 校准系数a * 校准系数b，取值[0,1]，向上取整
| 用户分层 | 学科能力分层区间 |
| --- | --- |
| S+ | [0 , 5] |
| S | (5 , 10] |
| A | (10 , 50] |
| B | (50 , 80] |
| C | (80 , 100] |

注：本科录取率在40%以下的学校 或 较差中学 ，用户分层中没有S+，落入S+区间的学生分层记为 S



**示例：**

一个高考本科录取率为57%的学校，今年入学全市排名中位数为2187，历届入学排名中位数为2000、1897、1878。

数学在年级排名百分比为38%的学生对应的学科能力为：
$$
38 \times 1.3 \times \sqrt{\frac{\left(\frac{1}{2000} + \frac{1}{1897} + \frac{1}{1878}\right) / 3}{1/2187}} \approx 53
$$

该学生的数学能力分层为B



#### 初中

1. 锚定学校水平
根据学校类型，对应到不同的校准系数a

| 学校类型 | 重点中学 | 普通中学 | 较差中学 |
| --- | --- | --- | --- |
| 校准系数a | 1 | 1.3 | 3 |

1. 确定用户分层
- 学科能力 = 用户单科排名百分比 * 校准系数a，取值[0,1]，向上取整
| 用户分层 | 学科能力分层区间 |
| --- | --- |
| S+ | [0 , 5] |
| S | (5 , 10] |
| A | (10 , 50] |
| B | (50 , 80] |
| C | (80 , 100] |

注：较差中学的用户分层中没有S+，落入S+区间的学生分层记为 S



**示例：**

一个较差中学的学生，数学在年级排名百分比为1%，对应的学科能力为：1 * 3 = 3

由于较差中学不设置S+分层，该学生的数学能力分层为S



### 更新用户分层

每次年级统一考试后，根据学生本次考试的年级排名百分比，更新用户所处分层。

统考成绩数据怎么拿到？



## 知识点掌握度

### 初始掌握度

V1.0不考虑知识点之间的迁移性，将所有知识点视为孤立知识点

1. 学科初始值 0.3
V1.0每个学科设置统一的初始掌握度，后续需要支持不同学科设置不同的掌握度初始值

1. 根据用户分层设置不同的初始掌握系数，每个用户在每个知识点上的初始掌握度 = 学科初始值 * 分层掌握系数
| 用户分层 | S+ | S | A | B | C |
| --- | --- | --- | --- | --- | --- |
| 分层掌握系数 | 1.5 | 1.2 | 0.8 | 0.5 | 0.2 |
| 初始掌握度 | 0.45 | 0.36 | 0.24 | 0.15 | 0.06 |

### 单次答题的掌握度增量

#### 数据准备

- 题目难度分级
此处的难度等级为知识点内的题目之间的相对难度，不会出现简单知识点下全部都是L1，复杂知识点下全部都是L5的情况。

| 难度档位 | L1 | L2 | L3 | L4 | L5 |
| --- | --- | --- | --- | --- | --- |
| 难度系数 | 0.2 | 0.4 | 0.6 | 0.8 | 1.0 |

#### 计算方式

- 整体逻辑
通过判断**答题结果**（正确或错误），结合**题目本身的难度系数**及**用户当前的知识点掌握度**，来确定掌握度的增减数值。这意味着不同水平的学生对不同难度题目的作答情况，会以不同幅度影响知识点的掌握度。

- 引入答错补偿系数
为避免答错了高难度题而被过度惩罚，在作答错误场景需要引入补偿系数

| 题目难度档位 | L1 | L2 | L3 | L4 | L5 |
| --- | --- | --- | --- | --- | --- |
| 答错补偿系数 | 1.8 | 0.8 | 0.5 | 0.3 | 0.2 |

- 设置默认增量
- 作答正确：默认掌握度增量为 0.2
- 作答错误：默认掌握度增量为 -0.1
- 计算方式
1. 作答正确：掌握度增量 = 0.2 *（题目难度系数 - 用户当前掌握度）
1. 部分正确：掌握度增量 = 0.2 *（题目难度系数 - 用户当前掌握度） * 0.5
1. 作答错误：掌握度增量 = -0.1 *（题目难度系数 - 用户当前掌握度）* 答错补偿系数
1. 特殊情况：
1. 作答正确/部分正确：如果题目难度系数-用户当前掌握度≤0.01，按0.01计算
1. 作答错误：
1. 如果题目难度系数-用户当前掌握度在[-0.01 , 0.01] ，按0.01计算
1. 如果题目难度系数-用户当前掌握度在[-1 , -0.01) , 取绝对值计算
1. 自评的主观题，在掌握度计算时需要降低权重。
1. 如果作答时间小于5秒，答题结果不进行结算
1. 如果作答时间大于等于5秒，计算增量时需要额外 * 0.5
- 示例：
1. 用户当前掌握度为0.24，做对了一道难度为L3的题目，单题掌握度增量的计算过程如下：
- 掌握度增量 = 0.2 * （0.6-0.24）= 0.07
1. 用户当前掌握度为0.24，做错了一道难度为L5的题目，单题掌握度增量的计算过程如下：
- 掌握度增量 = -0.1 *（1-0.24）* 0.2= -0.02
1. 用户当前掌握度为0.6，做错了一道难度为L1的题目，单题掌握度增量的计算过程如下：
- 掌握度增量 = -0.1 * |0.2 - 0.6| * 1.8 = -0.072
### 累计掌握度更新

**计算方式**：累计掌握度=当前掌握度 + 单题掌握度增量，**结果限制在[0,1]区间**

**限制：**用户有可能同一道题做多次，在同一节课里多次答题，仅更新第一次作答的掌握度变化。在不同的课程里的第一次作答依然结算掌握度。

**更新时机：**用户每次作答后更新

**更新范围：**用户作答题目关联的所有知识点。如果一道题关联了多个知识点，则多个知识点的掌握度都会更新。





## 外化掌握度

**应用场景：**用于在报告页展示学生对于本节课的掌握情况

### 更新时机

1. 用户学完本节课的「AI课」，中途退出不更新。
1. 用户完成「巩固练习」，中途退出不更新。
1. 用户完成「作业任务」，中途退出不更新。
1. 用户完成「测验任务」，中途退出不更新。


### 计算方式

1. 用户在本节课的知识点掌握度，换算成百分比展示。
1. 外化掌握度只增不降，当用户的知识点掌握度数值下降时，外化掌握度保持不变。
#### 最少答题量限制

为避免答题量过少导致的偶然性偏差，提升统计可靠性。需要对最少答题量进行限制。

答题量限制：5题

用户距离上次更新掌握度，需累计答题5道以上，才更新外化掌握度。

举例：

1. 用户首次学习课程，在AI课中答了3道题后退出，此时掌握度依然为0颗星。
1. 用户当前掌握度为2星，完成4道巩固练习后退出，掌握度不更新，需要累计作答5题才更新。
注：累计答题量为用户在本节课的答题量，不包括用户在别的课中做过本节课的题目。

#### 特殊情况

1. AI课中有题目，但是题目数量不足5题，则用户完成一节AI课后，需要更新外化掌握度。
1. AI课中没有配置题目。用户学完后，外化掌握度统一按20%展示。


## 目标掌握度

在用户设置/修改目标后，离线计算每节课对应的目标掌握度。

**目标设置：**高中阶段选择大学院校为目标，初中阶段选择年级排名为目标。

**整体逻辑：**目标掌握度和每节课的难度、考试频率、目标难度高度相关。单节课难度系数越低，考试频率越高，目标难度越高，要求的目标掌握度就越高。

### 数据准备

1. 单课难度系数
1. 考试频率
1. 目标难度系数
- 高中阶段
- 初中阶段
如果C端设置目标时选择的是年级排名的绝对值，数据收集时需要拿到学校的年级人数，通过年级人数和目标名次计算排名百分比，确定对应的难度系数。



### 计算方式

1. 每节课对应的目标掌握度 = 目标难度系数 * 单课难度系数 * 考试频率
1. 换算成百分比，按0.05向上取整
举例：

理科，课程难度高、考频中、用户目标一本

目标掌握度=0.8*0.9*1=0.72 ≈ 75%



## 指标跟踪

用于上线后监测异常情况，进行策略优化

- 外化掌握度变化：记录每个学生，每一次外化掌握度结算后的星级变化情况。是否会出现掌握度突然大幅下降或上升，超出了正常波动范围。
- 用户答题量：记录每个用户在每次星级变化时的答题数量。




# 四、学校数据收集

## 初始化数据

| 数据类型 | 是否必填 | 字段 | 目的 |
| --- | --- | --- | --- |
| 学生信息 | 是 | 学生姓名/ ID | 对应AI课系统的学生ID |
|  |  | 学生单科年级排名 | 用于计算学生单科成绩的年级排名百分比 |
|  |  | 年级人数 |  |
| 学校信息 - 高中收集 | 2选1 必填 |  | 根据学校近3年的高考本科录取率 / 学校类型，判断学校在全国范围内的大致水平 |
|  |  | 学校类型（重点/普通/较差） |  |
|  | 2选1 非必填 |  | 用于校准本届学生和历届学生的生源波动 |
|  |  |  |  |
| 学校信息 - 初中收集 | 是 | 学校类型（重点/普通/较差） | 根据学校类型，大致判断学校在全省/市范围内的水平 |

1. 高考年份
1. 对应年份的本科录取率
1. 对应高考年份的学生的中考排名
1. 本届学生的中考排名
1. 对应高考年份的学生的一分一段表
1. 本届学生的一分一段表
## 使用中数据

学生在年级统一考试（期中、期末）中的单科排名，用于更新用户分层。







# 历史存档（不用看）

### 内容结构

| 基础树（知识点） | 业务树（教材目录） |
| --- | --- |
| ![in_table_board_Rzpqwqw0UhUq87bAaufcXgjlnm5](https://static.test.xiaoluxue.cn/demo41/prd_images/1747049197629.png) | ![in_table_board_O2HOw193whUxfobCP4TcsBQUnQf](https://static.test.xiaoluxue.cn/demo41/prd_images/1747049198114.png) |

![in_table_board_Rzpqwqw0UhUq87bAaufcXgjlnm5]

###### 图片分析
【============== 图片解析 BEGIN ==============】

### 1. 图片类型、关键元素、组成关系及核心作用

**图片类型：**
知识点层级结构图（或知识树图）。

**关键元素及组成部分：**
该图片展示了一个以 "基础树" 为顶层的知识点层级结构。关键元素包括：
*   **基础树**: 代表整个知识体系的根节点或起点。
*   **一级知识点**: 包括 "一级知识点 A", "一级知识点 B", "一级知识点 C"。这些是 "基础树" 下的第一层级分支，代表核心知识模块。
*   **二级知识点**: 包括 "二级知识点a", "二级知识点b"。这些是 "一级知识点 A" 的下一层级分支，代表对一级知识点的细分。
*   **三级知识点**: 包括 "三级知识点1", "三级知识点2"。这些是 "二级知识点a" 的下一层级分支，代表更具体的知识单元。
*   **四级知识点**: 包括 "四级知识点 a"。这是 "三级知识点1" 的下一层级分支，代表目前图示中最细致的知识点。

**元素间关联：**
元素之间是清晰的父子层级关系，自上而下逐级展开：
*   "基础树" 是所有一级知识点的父节点。
*   "一级知识点 A" 是 "二级知识点a" 和 "二级知识点b" 的父节点。
*   "二级知识点a" 是 "三级知识点1" 和 "三级知识点2" 的父节点。
*   "三级知识点1" 是 "四级知识点 a" 的父节点。
一级知识点 B 和 一级知识点 C 在当前图中未展示其子节点。

**核心作用与价值（结合互联网教育领域 "掌握度策略"）：**
在 "掌握度策略" 的需求文档中，此知识点层级结构图的核心作用与价值在于：
*   **定义知识范围与结构**：清晰地界定了学习内容的基本构成单元及其相互关系，为整个掌握度模型的建立提供了基础框架。
*   **支持掌握度评估**：允许对不同层级的知识点进行掌握度评估。学生的掌握情况可以从最细粒度的四级知识点逐级汇总到上层知识点，直至整体 "基础树" 的掌握度。
*   **指导个性化学习**：根据学生在各知识点上的掌握情况，系统可以定位其薄弱环节，并推荐相应的学习资源或练习，实现个性化辅导和学习路径规划。
*   **内容组织与教学设计**：为课程设计、教材编排、习题库建设等提供了结构化依据，确保教学内容系统性和完整性。
*   **数据分析与反馈**：为教学效果分析、学生学习行为追踪提供了数据维度，有助于优化教学策略和产品功能。

### 2. 图片组成部分拆解及功能概述

图片主要由不同层级的知识点构成，这些知识点共同组成了知识树的结构。
*   **基础树**：
    *   **功能概述**：代表整个学科或课程的知识体系总览，是所有知识点的最高层级聚合。
*   **一级知识点 (如：一级知识点 A, 一级知识点 B, 一级知识点 C)**：
    *   **功能概述**：代表学科下的主要知识领域或核心模块，是知识结构的第一层分解。
*   **二级知识点 (如：二级知识点a, 二级知识点b)**：
    *   **功能概述**：对一级知识点的进一步细分，代表某一核心模块下的具体主题或单元。
*   **三级知识点 (如：三级知识点1, 三级知识点2)**：
    *   **功能概述**：对二级知识点的再次细分，代表某一主题下的关键概念、原理或技能。
*   **四级知识点 (如：四级知识点 a)**：
    *   **功能概述**：对三级知识点的细化，是知识体系中较为具体和细致的知识单元，可能是具体定义、公式、步骤或事实性知识。

### 3. 服务端需提供的功能和返回的数据内容

为支持前端展示及后端逻辑处理此知识点层级结构，服务端需要提供以下数据内容和功能：
服务端必须能够提供描述整个层级化知识结构的数据。针对此结构中的每一个知识点节点，服务端需要返回该知识点的唯一标识信息、知识点的名称或文本描述内容、该知识点所处的层级深度信息。同时，服务端需提供能确定每个知识点与其父节点隶属关系的数据，以及确定其所有直接子节点构成的数据。服务端应支持获取完整的知识树结构数据，或根据需要获取树中特定部分或子树的数据。这些数据共同构成了知识点间的完整拓扑关系，是实现掌握度策略和相关教育功能的基础。

### 4. Mermaid 流程图描述

```mermaid
graph TD
    R["基础树"] --> L1_A["一级知识点 A"];
    R --> L1_B["一级知识点 B"];
    R --> L1_C["一级知识点 C"];

    L1_A --> L2_Aa["二级知识点a"];
    L1_A --> L2_Ab["二级知识点b"];

    L2_Aa --> L3_A1["三级知识点1"];
    L2_Aa --> L3_A2["三级知识点2"];

    L3_A1 --> L4_Aa["四级知识点 a"];
```

【============== 图片解析 END ==============】



![in_table_board_O2HOw193whUxfobCP4TcsBQUnQf]

###### 图片分析
【============== 图片解析 BEGIN ==============】

根据提供的图片及其OCR文本，这是一份关于互联网教育产品中知识点掌握度策略的PRD文档插图。

1.  **图片类型、关键元素、层级结构及核心作用**

    *   **图片类型**：该图片是一个 **层级结构图** (或称为树状结构图、知识点结构图)。
    *   **关键元素**：
        *   **根节点**：业务树
        *   **一级节点**：级知识点 A、级知识点 B、级知识点 C
        *   **二级节点**：二级知识点a、二级知识点b (隶属于 级知识点 A)
        *   **三级节点**：三级知识点1、三级知识点2 (隶属于 二级知识点a)
    *   **层级化结构阐述**：
        1.  最顶层是“业务树”，代表整个知识体系的根或起点。
        2.  “业务树”下包含若干个一级知识点，如图中的“级知识点 A”、“级知识点 B”和“级知识点 C”。这些一级知识点代表了主要的知识领域或模块。
        3.  “级知识点 A”下又细分为二级知识点，如图中的“二级知识点a”和“二级知识点b”。这些二级知识点是一级知识点下的子主题或更细化的分类。
        4.  “二级知识点a”下进一步细分为三级知识点，如图中的“三级知识点1”和“三级知识点2”。这些三级知识点代表了具体的、更细微的知识概念或技能点。
    *   **核心作用与价值**：
        此层级结构图核心作用在于清晰地展示知识点的组织方式和相互之间的从属关系。在互联网教育产品中，这样的结构是构建学习路径、设计课程内容、追踪学习进度、评估知识点掌握程度以及实现个性化推荐的基础。它能够帮助产品和研发团队理解知识体系的脉络，确保教学内容和评估体系的系统性和完整性。对于掌握度策略而言，这意味着可以针对不同层级的知识点设定不同的掌握标准和评估方法。

2.  **各组成部分功能模块拆解**

    *   **业务树**：
        *   功能概述：代表整个知识体系的顶层入口或总览，是所有一级知识点的父级。
    *   **级知识点 A**：
        *   功能概述：一个主要的一级知识分类或领域，作为“业务树”的子级，同时是其下属二级知识点的父级。
    *   **二级知识点a**：
        *   功能概述：隶属于“级知识点 A”的一个二级知识分类或主题，是其下属三级知识点的父级。
    *   **三级知识点1**：
        *   功能概述：隶属于“二级知识点a”的一个具体的三级知识点或技能点。
    *   **三级知识点2**：
        *   功能概述：隶属于“二级知识点a”的另一个具体的三级知识点或技能点。
    *   **二级知识点b**：
        *   功能概述：隶属于“级知识点 A”的另一个二级知识分类或主题。
    *   **级知识点 B**：
        *   功能概述：另一个主要的一级知识分类或领域，作为“业务树”的子级。
    *   **级知识点 C**：
        *   功能概述：又一个主要的一级知识分类或领域，作为“业务树”的子级。

3.  **服务端需提供的功能和返回的数据内容**

    服务端需要提供能够描述这种层级化知识点结构的数据。具体来说：
    *   服务端需要能够返回各个知识点的标识信息和名称（如：“业务树”、“级知识点 A”、“二级知识点a”等）。
    *   服务端需要能够清晰地表达知识点之间的父子从属关系，以便客户端能够构建或理解这个树状结构。例如，需要明确指出“级知识点 A”是“业务树”的子节点，“二级知识点a”是“级知识点 A”的子节点等。
    *   服务端返回的数据应能完整呈现图中所示的所有知识点节点及其相互间的层级关系。
    *   对于每个节点，至少应包含其自身的名称信息，以及用于建立层级关联的指向其父节点或子节点列表的信息。

4.  **Mermaid 流程图描述**

    由于该图是层级结构图，使用 Mermaid 的 `graph` (specifically `graph TD` for Top-Down) 或 `mindmap` 语法比较合适。这里选用 `graph TD`。

    ```mermaid
    graph TD
        A0["业务树"] --> A1["级知识点 A"];
        A0 --> B1["级知识点 B"];
        A0 --> C1["级知识点 C"];
    
        A1 --> A2a["二级知识点a"];
        A1 --> A2b["二级知识点b"];
    
        A2a --> A3_1["三级知识点1"];
        A2a --> A3_2["三级知识点2"];
    ```

【============== 图片解析 END ==============】



- 基础树和业务树的关联：业务树的末级节点（单节课）会关联基础树的末级节点（知识点），一节课可以关联多个知识点标签，不存在没有关联知识点标签的课。
- 基础树和题目的关联：题目需要关联知识点标签，1道题目可关联多个知识点，不存在没有知识点标签的题目。
- 单节课对应的知识标签集合不等于题目对应的知识标签集合，但不存在单节课对应的知识点在巩固练习里没有包括的情况。
- AI课的内容（视频、文档、互动、费曼）不关联知识点标签，看内容不影响掌握度
![board_BbQGwy6hHhRAwMbqWihcu5IFnze](https://static.test.xiaoluxue.cn/demo41/prd_images/1747049198604.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

作为互联网产品经理，该图片是一张**结构关系图**，出自需求文档“掌握度策略 - V1.0”。它清晰地展示了教育内容（业务树）与知识体系（基础树）之间的核心关联方式，是实现个性化学习和掌握度评估的基础。

1.  **图片关键元素、组成部分及层级化结构阐述**

    该图主要包含两大核心区块：“业务树末级节点（单节课）”和“基础树末级节点（知识标签）”，以及连接这两者的“关联关系”。

    *   **业务树末级节点（单节课）**:
        *   **层级**: 这是业务内容结构中的最细化教学单元，例如“A1课”。
        *   **组成**: 一节“单节课”内部由多种教学组件构成，包括：
            *   **内容呈现组件**: 如“视频/文档”。
            *   **互动学习组件**: 如“费曼组件”。
            *   **练习评估组件**: 如“练习组件”（包含“题目1”、“题目2”）和“巩固练习”（包含“题目3”、“题目4”）。
        *   **核心作用**: “单节课”是学生直接学习和互动的对象，承载了具体的教学内容和活动。题目是检验学习效果、收集学习数据的重要手段。

    *   **基础树末级节点（知识标签）**:
        *   **层级**: 这是知识体系结构中的原子化知识单元，例如“知识标签1”、“知识标签2”等。
        *   **核心作用**: “知识标签”代表了需要掌握的最小知识点。通过将这些标签与教学内容和题目进行关联，系统可以精确追踪学生对每个知识点的掌握程度。

    *   **关联关系**:
        *   **类型**:
            *   “为单节课和知识标签关联”：指代将整个“单节课”与一个或多个“知识标签”进行绑定。
            *   “为题目和知识标签关联”：指代将“单节课”中的具体“题目”与一个或多个“知识标签”进行绑定。
        *   **核心作用与价值**: 这是整个掌握度策略的核心。通过这两层关联：
            1.  **内容定位**: 明确了每节课、每个题目所覆盖的知识范围。
            2.  **数据归因**: 学生在题目上的表现可以直接归因到具体的知识标签。
            3.  **掌握度计算**: 为后续基于知识标签的掌握度计算、学习路径规划、薄弱知识点推荐等个性化教育功能提供了数据基础和逻辑通路。
            这种结构使得系统能够从业务教学单元（课、题目）深入到基础知识单元（知识标签）层面进行分析和评估。

2.  **图片各组成部分拆解及功能模块概述**

    *   **业务树末级节点 (单节课)**:
        *   **A1课**: 代表一个具体的课程单元实例。
        *   **视频/文档**: 教学模块，用于承载和展示视频或文档形式的教学内容。
        *   **费曼组件**: 教学模块，用于引导学生通过费曼学习法进行主动思考和知识输出。
        *   **练习组件**: 练习模块，包含若干题目，用于学生进行随堂练习。
            *   **题目1, 题目2**: 具体的练习题。
        *   **巩固练习**: 练习模块，包含若干题目，用于学生在课后或阶段性巩固所学知识。
            *   **题目3, 题目4**: 具体的巩固练习题。
    *   **基础树末级节点 (知识标签)**:
        *   **知识标签1, 知识标签2, 知识标签3, 知识标签4**: 代表原子化的知识点或技能点。
    *   **关联机制**:
        *   **为单节课和知识标签关联**: 功能，用于建立“单节课”与其所覆盖的“知识标签”之间的映射关系。
        *   **为题目和知识标签关联**: 功能，用于建立具体“题目”与其考察的“知识标签”之间的映射关系。

3.  **服务端需提供的功能和返回的数据内容描述**

    服务端需要提供以下数据内容和支持相应的功能：
    *   **业务树末级节点（单节课）数据**:
        *   能够返回指定单节课的详细信息，包括其下所包含的各类组件（如视频/文档、费曼组件、练习组件、巩固练习）的标识和基础信息。
        *   能够返回练习组件和巩固练习组件中所包含的具体题目列表及其详细信息。
    *   **基础树末级节点（知识标签）数据**:
        *   能够返回知识标签的列表及其详细信息。
    *   **关联关系数据**:
        *   能够返回单节课与其关联的知识标签之间的映射关系数据。
        *   能够返回题目与其关联的知识标签之间的映射关系数据。
    *   **支持功能**:
        *   支持查询单节课及其构成组件和题目。
        *   支持查询知识标签。
        *   支持查询和管理（增删改）单节课与知识标签的关联。
        *   支持查询和管理（增删改）题目与知识标签的关联。

4.  **Mermaid 流程图描述**

    ```mermaid
    graph TD
        subgraph BusinessTree [业务树末级节点 (单节课)]
            direction TB
            Lesson["A1课"]
            Lesson --> VideoDoc["视频/文档"]
            Lesson --> Feynman["费曼组件"]
            Lesson --> Practice["练习组件"]
            Practice --> Q1["题目1"]
            Practice --> Q2["题目2"]
            Lesson --> Consolidation["巩固练习"]
            Consolidation --> Q3["题目3"]
            Consolidation --> Q4["题目4"]
        end
    
        subgraph FoundationTree [基础树末级节点 (知识标签)]
            direction TB
            KT1["知识标签1"]
            KT2["知识标签2"]
            KT3["知识标签3"]
            KT4["知识标签4"]
        end
    
        LinkNode1["为单节课和知识标签关联"]
        LinkNode2["为题目和知识标签关联"]
    
        Lesson --> LinkNode1
        LinkNode1 --> KT1
        LinkNode1 --> KT2
        LinkNode1 --> KT3
        LinkNode1 --> KT4
    
        Q1 --> LinkNode2
        Q2 --> LinkNode2
        Q3 --> LinkNode2
        Q4 --> LinkNode2
        LinkNode2 --> KT1
        LinkNode2 --> KT2
        LinkNode2 --> KT3
        LinkNode2 --> KT4
    
        style LinkNode1 fill:#cde4cc,stroke:#333,stroke-width:1px,border-style:dashed
        style LinkNode2 fill:#cde4cc,stroke:#333,stroke-width:1px,border-style:dashed
    ```

【============== 图片解析 END ==============】





### 外化掌握度

**数据准备**

- 课程（业务树末级节点）和知识点的对应关系
一节课可对应多个知识点，其中一个知识点为主知识点，其余知识点为次知识点。不同类型的知识点有不同的结算系数。

|  | 主知识点 | 次知识点 |
| --- | --- | --- |
| 结算系数 | 1 | 0.2 |

注：外化掌握度的结算关系为课程对应的知识点，不是课程中全部题目对应的知识点

**计算方式**

1. 同步课 - 加权平均
外化掌握度 = （知识点1掌握度 * 知识点1结算系数 + 知识点2掌握度 * 知识点2结算系数+ …… ）/ （知识点1结算系数 + 知识点2结算系数+ ……）

1. 复习课 - 算术平均
外化掌握度 = （知识点1掌握度 + 知识点2掌握度 + …… ）/  知识点数量





### 按学生维度计算用户分层

| 数据类型 | 字段 | 字段说明 |
| --- | --- | --- |
| 学生信息 | 学生ID | 和AI课系统一一对应的学生ID |
|  | 年级排名百分比 | 如果拿到的是学生排名信息，需要转换为排名百分比。 |
| 学校的历届高考成绩（近3年） | 年份 | 高考年份 |
|  | 高考成绩对应不同院校梯队的达线率 | 从成绩分布角度，看处于某一分数段（对应能报考某类院校梯队）的考生在学校全部考生中的占比 。 |
| 学校的入学平均分 | 年份 | 对应到历届高考年份 |
|  | 历届入学平均分（近3年） | 历届学生入学时中考成绩的平均分 |
|  | 今年的入学平均分 | 当前届学生入学时中考成绩的平均分 |

1. 学生排名
1. 年级人数
示例：年级50名 / 年级共200名学生 → 排名百分比=25% 。代表这名学生在年级所有学生中处于前 25% 的位置。

示例：2023年，学校有2000名学生参加高考，其中100名学生的高考分数达到985 / 211分数线，则2023年的985 / 211达线率为 100/2000=5%

用于校准不同届的生源质量

**情况一：有学校的历届高考成绩**

1. 计算学校历届高考成绩对应不同院校梯队的平均达线率，向上取整
示例：

| 对应院校梯队 | 高考达线率-2024 | 高考达线率-2023 | 高考达线率-2022 | 平均达线率 |
| --- | --- | --- | --- | --- |
| 985头部 | a % | a' % | a'' % | (a + a'+ a'') / 3 |
| 985 / 211 | b % | b' % | b'' % | (b + b'+ b'') / 3 |
| 一本 | c % | c '% | c ''% | (c + c'+ c'') / 3 |
| 二本 | d % | d '% | d ''% | (d + d' + d'') / 3 |

1. 根据学校入学平均分计算校准系数
1. 校准系数 = √（今年入学平均分/ 历年平均分的平均）
1. 如果学校没有提供入学平均分数据，则校准系数默认为1
示例：

校准系数 = √ [ a/ (b + b'+ b'') / 3 ]= √ [ 620/ (650 + 655+ 647) / 3 ]≈0.98

|  | 今年 | 2024 | 2023 | 2022 |
| --- | --- | --- | --- | --- |
| 入学平均分 | a | b | b' | b'' |
|  | 620 | 650 | 655 | 647 |

1. 用平均达线率 * 校准系数作为最终的分层阈值。根据学生在年级排名百分比，确定学生所属分层
1. 如果院校梯队对应的达线率为0，则该学校没有对应分层的学生
示例：

- 年级共200名学生
- 学生A：年级50名  → 排名百分比=25%   → 分层A
- 学生B：年级138名  → 排名百分比=69%   → 分层B
| 用户分层 | 对应院校梯队 | 平均达线率 | 分层阈值 | 分层区间 |
| --- | --- | --- | --- | --- |
| S+ | 985头部 | 5% | 5 * 0.98 | [0 , 4.9] |
| S | 985 / 211 | 10% | 10 * 0.98 | (4.9 , 9.8] |
| A | 一本 | 50% | 50 * 0.98 | (9.8 , 49] |
| B | 二本 | 80% | 80 * 0.98 | (49 , 78.4] |
| C | 二本以下 | - | - | (78.4 , 100] |



**情况二：仅有学生在年级排名百分比**

采用基础阈值，根据学校是否为重点中学，设置不同的排名系数。（系数可针对学校设置）

- 分层阈值 = 基础阈值 * 排名系数
- 重点中学：1.2
- 普通中学：1.0
- 较差中学：0.7
较差中学冷启动时默认没有S+分层

示例：

| 用户分层 | 对应院校梯队 | 排名百分比基础阈值 | 排名系数 | 学生在校内排名区间 |
| --- | --- | --- | --- | --- |
| S+ | 985头部 | 1% | 0.7 | - |
| S | 985 / 211 | 5% |  | [0 , 3.5] |
| A | 一本 | 40% |  | (3.5 , 28] |
| B | 二本 | 70% |  | (28 , 49] |
| C | 二本以下 | - |  | (49 , 100] |



