---
【Cursor开发中rules配置文件】后端版框架（适配Golang场景）
---

# **Kratos 项目开发与结构规范 (v1.0)**

---

## **1. 项目结构约定 (Project Structure Convention)**

*   **1.1. 目标:** 规范项目目录结构，明确各模块职责，便于团队协作和项目维护。
*   **1.2. 核心思想:** 遵循领域驱动设计 (DDD) 分层思想，结合 Kratos 框架特点。

*   **1.3. 常见模块/目录职责说明 (Standard Directory Purposes):**

    *   **`/app/{biz_module}`:** 核心业务模块目录，按业务垂直划分 (例如 `/app/project`)。
        *   `dao/`: 数据访问对象 (Data Access Object)，负责与数据源（如数据库、缓存）交互。
            *   `interface.go`: 定义 DAO 操作接口。
            *   `struct.go`: 定义 DAO 层数据结构 (通常是 GORM Model)。
            *   `impl/`: DAO 接口的具体实现。
        *   `domain/`: 领域层，包含核心业务逻辑和模型。
            *   `interface.go`: 定义领域逻辑接口 (Repository, Service 接口等)。
            *   `struct.go`: 定义领域实体、值对象。
            *   `impl/`: 领域接口的具体实现 (业务逻辑)。
        *   `service/`: 应用服务层，编排领域逻辑，处理 Use Cases。
        *   `controller/`: 接口层，处理外部请求 (HTTP/gRPC)，调用 Service。
            *   `http_server/`: HTTP 服务实现。
            *   `grpc_server/`: gRPC 服务实现。
        *   `dto/`: 数据传输对象 (Data Transfer Object)，用于层间数据传递，尤其是在 Controller 和 Service 之间。
        *   `conf/`: 当前业务模块特有的配置结构体定义。
    *   **`/app/core`:** 核心库，提供项目级的基础功能封装。
        *   `database/` (或 `postgresqlx/`, `mysqlx/`): 数据库连接和基础操作封装。
        *   `redis/` (或 `redisx/`): Redis 客户端封装。
        *   `logger/`: 日志库实现。
        *   `config/`: 配置加载实现。
        *   *(其他通用核心库，如 Kafka, Elasticsearch 等)*
    *   **`/app/middleware`:** 中间件定义，用于请求处理链 (如认证、日志、限流)。
    *   **`/app/util`:** 通用工具类库，与具体业务无关 (如时间处理、字符串操作、错误封装)。
    *   **`/app/consts`:** 项目全局或模块级常量定义。
    *   **`/cmd/{service_name}`:** 服务启动入口，负责初始化配置、依赖注入 (Wire)、启动 Server。
    *   **`/configs`:** 存放配置文件的目录 (如 `config.yaml`)。
    *   **`/api` (或 `/proto`):** API 定义文件，通常使用 Protobuf 定义 gRPC 和 HTTP 接口。
        *   `/api/gen/go`: Protobuf 生成的 Go 代码。
    *   **`/docs`:** 项目文档 (包括但不限于 API 文档、设计文档、数据库文档)。
    *   **`/scripts` (或 `/tools`):** 存放辅助脚本 (构建、部署、代码生成、数据迁移等)。
    *   **`/internal`:** (可选，如果严格遵循 Go Project Layout) 用于存放不希望外部项目直接导入的内部代码，`app/` 下的内容可以移入 `internal/app/`。
    *   **`/vendor`:** Go 依赖包的本地副本，通过 `go mod vendor` 管理。

*   **1.4. 示例项目结构图 (参考):** *(保留原文件中的 ASCII 结构图)*
    ```
    .
    ├── app/
    │   ├── middleware/
    │   │   └── provider/      # 中间件的 Wire provider (依赖注入)
    │   ├── core/
    │   │   ├── holox/         # (用途待确认，可能与特定框架或库相关)
    │   │   ├── postgresqlx/   # PostgreSQL 数据库操作封装
    │   │   ├── mysqlx/        # MySQL 数据库操作封装
    │   │   ├── polardbx/      # PolarDB-X 数据库操作封装
    │   │   ├── kafka/         # Kafka 消息队列客户端封装
    │   │   ├── redisx/        # Redis 缓存操作封装
    │   │   ├── logger/        # 日志记录实现 (符合规则 logger_new?)
    │   ├── util/
    │   │   ├── providers/     # 工具类的 Wire provider?
    │   │   ├── protoeditor/   # Protobuf 编辑相关工具
    │   │   ├── response/      # API 响应格式化工具
    │   │   ├── base62x/       # Base62 编码/解码工具
    │   │   ├── grpc/          # gRPC 相关工具
    │   │   ├── errorx/        # 错误处理工具
    │   │   ├── middlewares/   # (与顶层 middleware/ 关系待确认)
    │   │   ├── cachex/        # 缓存相关工具
    │   │   ├── cache_manage/  # 缓存管理工具
    │   │   ├── gorm_builder/  # GORM 查询构建器?
    │   │   ├── zipkin_trace/  # Zipkin 分布式追踪集成
    │   │   ├── sidx/          # ID 生成工具 (如 shortid)?
    │   │   ├── time/          # 时间处理工具
    │   │   ├── contextx/      # Context 扩展工具
    │   │   ├── httpcodec/     # HTTP 编解码工具
    │   │   ├── elasticsearch/ # Elasticsearch 客户端封装
    │   │   ├── alipay_service/ # 支付宝服务相关工具?
    │   │   ├── loggerx/       # 日志相关工具 (与 core/logger/ 关系待确认)
    │   │   ├── google.golang.org/ # (需要清理，可能是误导入)
    │   ├── dao/
    │   │   ├── providers/     # DAO 的 Wire provider (用于依赖注入)
    │   │   ├── builder/       # GORM 查询构建器相关工具或封装
    │   │   ├── project/
    │   │   │   ├── interface.go # 定义 `project` DAO 的操作接口
    │   │   │   ├── struct.go  # 定义 `project` 在 DAO 层的数据结构 (通常是 GORM Model)
    │   │   │   └── impl/      # `project` DAO 的具体实现 (interface.go 中接口的实现)
    │   ├── dto/
    │   │   ├── project/
    │   │   │   └── struct.go  # 定义 `project` 在 DTO 层的数据结构
    │   ├── server/            # 服务器启动和配置 (HTTP/gRPC Server)
    │   ├── controller/
    │   │   ├── providers/     # Controller 的 Wire provider
    │   │   ├── grpc_server/   # gRPC 服务实现
    │   │   ├── http_server/
    │   │   │   └── router/
    │   │   │       └── router.go # 主要的路由配置文件，定义 HTTP/gRPC 路由规则，并将路由映射到 Controller 中的处理器方法。
    │   │   └── provider.go    # Wire provider 文件，用于将路由配置注入到应用中。
    │   ├── service/
    │   │   ├── providers/     # Service 的 Wire provider
    │   │   └── project_service/  # project相关业务服务
    │   ├── conf/              # 配置文件的结构体定义
    │   ├── domain/
    │   │   ├── project/
    │   │   │   ├── interface.go # 定义领域 `project`的操作接口
    │   │   │   ├── struct.go  # 定义领域 `project` 实体的数据结构
    │   │   │   └── impl/      # 领域`project`的具体实现 (interface.go 中接口的实现)
    │   ├── consts/            # 项目常量定义 (符合规则)
    │   └── router/            # 路由注册与定义 （废弃）
    ├── configs/               # 配置文件目录 (区分环境)。（废弃）
    ├── docs/                  # 项目文档，包含数据库文件相关
    ├── libs/                  # 项目本地公共库,框架级别
    ├── main/                  # 项目/服务入口
    ├── mock/
    │   └── local/
    ├── nacos/
    │   ├── cache/
    │   │   └── config/
    │   └── log/
    ├── proto/
    │   ├── demoapi/
    │   │   ├── base/          # 基础类型定义 (.proto 文件)
    │   │   ├── user/          # 用户相关的 proto
    │   │   └── api/           # API 接口定义 (.proto 文件)
    │   ├── gen/
    │   │   └── go/            # 生成的 Go 代码
    │   └── openapi/
    │       └── proto/         # 从 proto 生成的 openapi 文件?
    ├── script/                # 脚本文件 (如定时任务、维护脚本)
    ├── vendor/                # Go vendor 目录 (管理项目依赖)
    ├── Dockerfile             # Docker 镜像构建文件
    ├── go.mod                 # Go 模块依赖管理文件
    ├── go.sum                 # Go 模块校验和
    ├── LICENSE                # 项目许可证
    ├── Makefile               # 项目构建和任务脚本 (make all, make build 等)
    └── README.md              # 项目说明文档
    ```

---

## **2. Vendor 目录与依赖管理 (Vendor & Dependency Management)**

*   **2.1. 依赖管理:** 项目使用 Go Modules 进行依赖管理，依赖信息记录在 `go.mod` 和 `go.sum` 文件中。
*   **2.2. Vendor 使用规则:**
    *   2.2.1. 日常开发中可以不强制使用 `vendor` 目录，直接依赖 Go Modules 机制。
    *   2.2.2. **CI/CD 流水线**在执行构建任务时，**必须**首先运行 `go mod vendor` 生成/更新 `vendor` 目录，并使用 `-mod=vendor` 标志进行编译，以确保构建的可重现性和稳定性。
    *   2.2.3. 如果选择在本地开发中提交 `vendor` 目录，则每次修改 `go.mod` 后，必须运行 `go mod tidy && go mod vendor` 并将 `vendor` 目录的变更提交。*(采用此方式需团队达成一致)*

*   **2.3. 当前项目主要依赖说明:** *(保留原文件中的依赖列表及其注释)*

    *   **直接依赖 (Direct Dependencies):**
        ```go
        require (
            // 数据库与缓存
            github.com/elastic/go-elasticsearch/v8 v8.17.1       // Elasticsearch 官方 Go 客户端，支持分布式搜索与分析
            github.com/go-redis/redis/v8 v8.11.5               // Redis 客户端，支持同步/异步操作及集群模式
            gorm.io/driver/mysql v1.5.7                        // GORM 的 MySQL 驱动，支持连接池和高级配置
            gorm.io/driver/postgres v1.5.11                    // GORM 的 PostgreSQL 驱动，支持 PostgreSQL 特性
            gorm.io/gorm v1.25.12                              // 主流 ORM 框架，简化数据库 CRUD 操作
            gorm.io/plugin/dbresolver v1.5.3                   // GORM 读写分离插件，支持多数据源切换

            // 微服务与 Web 框架
            github.com/go-kratos/kratos/v2 v2.8.4               // 高性能微服务框架，内置服务发现、链路追踪、配置管理
            github.com/gin-gonic/gin v1.10.0                   // 轻量级 Web 框架，支持路由、中间件和 RESTful API

            // 工具与基础库
            github.com/google/wire v0.6.0                       // 依赖注入工具，自动生成依赖代码，减少手动初始化
            github.com/sirupsen/logrus v1.9.3                   // 结构化日志库，支持日志级别、格式输出和钩子机制
            github.com/lestrrat-go/file-rotatelogs v2.4.0+incompatible // 日志文件滚动切割库，按时间/大小自动分割
            github.com/sony/sonyflake v1.2.0                    // 分布式唯一 ID 生成器，基于 Snowflake 算法
            google.golang.org/protobuf v1.36.5                  // Google Protocol Buffers 官方库，用于高效数据序列化
            github.com/nacos-group/nacos-sdk-go v1.1.5          // Nacos 客户端，实现服务注册发现和动态配置管理

            // 网络与通信
            github.com/go-resty/resty/v2 v2.16.5                // 功能丰富的 HTTP 客户端，支持 RESTful 请求和响应处理
            github.com/openzipkin/zipkin-go v0.4.3              // Zipkin 分布式追踪客户端，用于链路监控和性能分析

            // 数据处理与转换
            github.com/jinzhu/copier v0.4.0                     // 结构体深度复制工具，支持复杂类型和标签配置
            github.com/mailru/easyjson v0.9.0                   // 高性能 JSON 编解码库，生成定制化序列化代码
            github.com/golang-module/carbon v1.7.3              // 日期时间处理库，提供类似 PHP Carbon 的链式操作

            // 辅助工具
            github.com/pkg/errors v0.9.1                        // 错误处理库，支持错误堆栈追踪和自定义错误类型
            github.com/satori/go.uuid v1.2.0                    // UUID 生成器，支持多种版本 UUID（v1/v4 等）
            github.com/spf13/cast v1.7.1                        // 类型转换工具，支持字符串与数字、时间等互转
        )
        ```
    *   **间接依赖 (Indirect Dependencies):** *(通常无需详细列出，但原文件包含，故保留)*
        ```go
       // 直接依赖
       require (
           // 数据库与缓存
           github.com/elastic/go-elasticsearch/v8 v8.17.1 // Elasticsearch的官方Go客户端（开源），用于与Elasticsearch进行交互，实现分布式搜索与分析功能
           github.com/go-redis/redis/v8 v8.11.5 // Redis客户端（开源），支持与Redis进行交互，如读取、写入、删除数据等操作
           gorm.io/driver/mysql v1.5.7 // GORM MySQL数据库驱动（开源），用于连接MySQL数据库，使GORM框架能够操作MySQL
           gorm.io/driver/postgres v1.5.11 // GORM PostgreSQL数据库驱动（开源），用于连接PostgreSQL数据库，使GORM框架能够操作PostgreSQL
           gorm.io/gorm v1.25.12 // GORM是一个ORM框架（开源），用于简化数据库操作，如创建、读取、更新和删除数据
           gorm.io/plugin/dbresolver v1.5.3 // GORM插件（开源），用于数据库读写分离，提高数据库性能

           // 微服务与Web框架
           github.com/go-kratos/kratos/v2 v2.8.4 // Go语言微服务框架（开源），提供服务发现、链路跟踪等功能，帮助构建高效的微服务架构
           github.com/gin-gonic/gin v1.10.0 // Gin框架（开源），用于Web开发，处理HTTP请求，提供路由、中间件等功能

           // 工具与基础库
           github.com/google/wire v0.6.0 // Go的依赖注入库（开源），用于管理和注入依赖关系，提高代码的可测试性和可维护性
           github.com/sirupsen/logrus v1.9.3 // 日志记录工具（开源），支持多种日志级别和输出格式，方便记录应用程序的运行信息
           github.com/lestrrat-go/file-rotatelogs v2.4.0+incompatible // 日志文件按时间滚动的库（开源），用于自动切割日志文件，便于管理和查看日志
           github.com/sony/sonyflake v1.2.0 // 唯一ID生成器（开源），类似Snowflake，用于生成唯一的标识符
           google.golang.org/protobuf v1.36.5 // Protocol Buffers（protobuf）的Go实现（开源），用于序列化数据，提高数据传输和存储的效率
           github.com/nacos-group/nacos-sdk-go v1.1.5 // Nacos客户端（开源），用于配置管理和服务发现，帮助实现服务的注册、发现和配置管理

           // 网络与通信
           github.com/go-resty/resty/v2 v2.16.5 // HTTP客户端库（开源），用于简化API调用，提供方便的方法来发送HTTP请求和处理响应
           github.com/openzipkin/zipkin-go v0.4.3 // Zipkin分布式追踪系统的Go实现（开源），用于跟踪分布式系统中的请求流程，分析性能瓶颈

           // 数据处理与转换
           github.com/jinzhu/copier v0.4.0 // 结构体复制工具（开源），可以快速复制数据结构，方便进行数据的克隆和转换
           github.com/mailru/easyjson v0.9.0 // JSON编码解码器（开源），比标准库更高效，用于快速进行JSON数据的编码和解码
           github.com/golang-module/carbon v1.7.3 // 处理日期和时间的库（开源），类似于Carbon（PHP），提供方便的日期和时间操作方法

           // 辅助工具
           github.com/pkg/errors v0.9.1 // 错误处理工具（开源），提供堆栈跟踪和错误包装功能，方便处理和管理错误
           github.com/satori/go.uuid v1.2.0 // 生成UUID的库（开源），用于生成通用唯一识别码
           github.com/spf13/cast v1.7.1 // 用于类型转换的工具库（开源），支持在不同类型之间进行转换，如字符串到数字、时间等的转换
       )

       // 间接依赖（通常由直接依赖自动引入）
       require (
           // 基础库与工具
           dario.cat/mergo v1.0.1 // indirect; 深度合并结构体的库（开源），用于合并结构体，支持字段级合并策略
           filippo.io/edwards25519 v1.1.0 // indirect; 高性能Edwards25519签名算法实现（开源），用于进行签名和验证操作
           github.com/aliyun/alibaba-cloud-sdk-go v1.63.88 // indirect; 阿里云SDK（开源），提供对阿里云服务的支持，如访问阿里云的各种API
           github.com/buger/jsonparser v1.1.1 // indirect; 高效的JSON解析库（开源），用于快速解析JSON数据
           github.com/bytedance/sonic v1.12.8 // indirect; 高效的JSON编解码库（开源），比标准库更快，用于快速进行JSON数据的编码和解码
           github.com/bytedance/sonic/loader v0.2.3 // indirect; 相关的加载器（开源），用于加载JSON数据
           github.com/cespare/xxhash/v2 v2.3.0 // indirect; 高效的哈希算法（开源），主要用于快速计算哈希值，如数据校验和哈希表操作
           github.com/cloudwego/base64x v0.1.5 // indirect; 扩展的base64编码/解码库（开源），用于进行base64编码和解码操作
           github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect; Rendezvous hashing算法实现（开源），用于分布式系统中的负载均衡和数据分配
           github.com/elastic/elastic-transport-go/v8 v8.6.1 // indirect; Elasticsearch传输层（开源），支持请求和响应处理，用于与Elasticsearch进行通信
           github.com/fsnotify/fsnotify v1.8.0 // indirect; 文件系统变化监听库（开源），用于实时监听文件系统的变化，如文件的创建、修改和删除
           github.com/gabriel-vasile/mimetype v1.4.8 // indirect; MIME类型检测库（开源），用于检测文件的MIME类型
           github.com/gin-contrib/sse v1.0.0 // indirect; 用于在Gin中发送Server-Sent Events（SSE）（开源），实现服务器向客户端推送实时数据
           github.com/go-errors/errors v1.5.1 // indirect; 错误处理工具（开源），类似于`util/errors`，用于处理和管理错误
           github.com/go-kratos/aegis v0.2.0 // indirect; Kratos微服务框架的健康检查库（开源），用于检查微服务的健康状态
           github.com/go-logr/logr v1.4.2 // indirect; 日志接口库（开源），支持多种日志实现，提供统一的日志接口
           github.com/go-logr/stdr v1.2.2 // indirect; Go-logr的标准日志实现（开源），提供标准的日志记录功能
           github.com/go-playground/form/v4 v4.2.1 // indirect; 表单解析库（开源），用于解析表单数据，方便处理HTTP表单请求
           github.com/go-playground/locales v0.14.1 // indirect; 本地化和翻译支持（开源），用于支持多语言和本地化功能
           github.com/go-playground/universal-translator v0.18.1 // indirect; 多语言支持库（开源），用于实现多语言翻译功能
           github.com/go-playground/validator/v10 v10.24.0 // indirect; 数据验证库（开源），用于验证结构体字段，确保数据的合法性和有效性
           github.com/go-sql-driver/mysql v1.8.1 // indirect; MySQL的Go数据库驱动（开源），用于连接MySQL数据库
           github.com/gobuffalo/envy v1.10.2 // indirect; 环境变量读取库（开源），用于读取和管理环境变量
           github.com/gobuffalo/packd v1.0.2 // indirect; 静态文件打包工具（开源），用于打包静态文件，方便部署和管理
           github.com/gobuffalo/packr v1.30.1 // indirect; 静态文件打包工具（与packd类似）（开源），用于打包静态文件
           github.com/goccy/go-json v0.10.5 // indirect; 快速的JSON处理库（开源），用于快速处理JSON数据
           github.com/google/uuid v1.6.0 // indirect; 生成UUID的库（开源），用于生成通用唯一识别码
           github.com/gorilla/mux v1.8.1 // indirect; 强大的HTTP路由库（开源），用于定义和管理HTTP路由
           github.com/jackc/pgpassfile v1.0.0 // indirect; PostgreSQL密码文件解析库（开源），用于解析PostgreSQL的密码文件
           github.com/jackc/pgservicefile v0.0.0-20240606120523-5a60cdf6a761 // indirect; PostgreSQL服务文件解析库（开源），用于解析PostgreSQL的服务文件
           github.com/jackc/pgx/v5 v5.7.2 // indirect; PostgreSQL数据库驱动和客户端库（开源），用于连接和操作PostgreSQL数据库
           github.com/jackc/puddle/v2 v2.2.2 // indirect; 连接池管理库（开源），用于管理数据库连接池，提高数据库连接的效率和性能
           github.com/jinzhu/inflection v1.0.0 // indirect; 用于处理单复数形式的库（开源），用于处理单词的单复数形式转换
           github.com/jinzhu/now v1.1.5 // indirect; 日期和时间操作库（开源），用于进行日期和时间的操作
           github.com/jmespath/go-jmespath v0.4.0 // indirect; JMESPath查询语言实现（开源），用于在JSON数据中进行查询和过滤
           github.com/joho/godotenv v1.5.1 // indirect; 从 `.env` 文件加载环境变量（开源），用于从环境变量文件中加载配置信息
           github.com/jonboulle/clockwork v0.4.0 // indirect; 方便的时间操作工具（开源），用于进行时间相关的操作
           github.com/josharian/intern v1.0.0 // indirect; 字符串池实现（开源），优化内存，用于管理字符串池，减少内存占用
           github.com/json-iterator/go v1.1.12 // indirect; 高效的JSON编解码库（开源），用于快速进行JSON数据的编码和解码
           github.com/klauspost/cpuid/v2 v2.2.9 // indirect; CPU信息查询工具（开源），用于查询CPU的相关信息
           github.com/leodido/go-urn v1.4.0 // indirect; URN生成工具（开源），用于生成统一资源名称（URN）
           github.com/lestrrat-go/strftime v1.1.0 // indirect; 格式化时间为字符串（开源），用于将时间格式化为指定的字符串格式
           github.com/mattn/go-isatty v0.0.20 // indirect; 检查标准输出是否是TTY终端（开源），用于检查标准输出是否是终端设备
           github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect; 并发操作工具库（开源），用于进行并发操作，如并发控制和同步
           github.com/modern-go/reflect2 v1.0.2 // indirect; 高效的反射库（开源），用于进行反射操作，如获取和操作对象的属性和方法
           github.com/pelletier/go-toml/v2 v2.2.3 // indirect; TOML文件解析库（开源），用于解析TOML格式的文件
           github.com/rogpeppe/go-internal v1.13.1 // indirect; Go的内部工具库（开源），包含一些Go语言内部使用的工具和函数
           github.com/twitchyliquid64/golang-asm v0.15.1 // indirect; Golang汇编优化工具（开源），用于优化Golang的汇编代码
           github.com/ugorji/go/codec v1.2.12 // indirect; 高效的编解码库（开源），支持JSON、BSON等格式，用于进行数据的编码和解码
           go.opentelemetry.io/otel v1.34.0 // indirect; OpenTelemetry的Go实现（开源），用于分布式追踪，帮助跟踪分布式系统中的请求流程
           go.opentelemetry.io/otel/metric v1.34.0 // indirect; OpenTelemetry的Go实现（开源），提供度量支持，用于收集和报告系统的度量数据
           go.opentelemetry.io/otel/trace v1.34.0 // indirect; OpenTelemetry的Go实现（开源），提供追踪支持，用于跟踪分布式系统中的请求流程和性能分析
           go.uber.org/multierr v1.11.0 // indirect; 处理多个错误的工具库（开源），用于处理和管理多个错误
           go.uber.org/zap v1.27.0 // indirect; 高性能的日志库（开源），提供高性能的日志记录功能
           golang.org/x/arch v0.14.0 // indirect; Go架构支持（开源），提供与Go架构相关的支持和工具
           golang.org/x/crypto v0.36.0 // indirect; Go加密库（开源），提供各种加密算法和工具
           golang.org/x/mod v0.23.0 // indirect; Go模块工具库（开源），用于管理Go模块，如依赖管理和版本控制
           golang.org/x/net v0.38.0 // Go的网络库（开源），提供网络相关的功能和工具
           golang.org/x/sync v0.12.0 // indirect; Go的并发工具库（开源），用于进行并发操作和同步控制
           golang.org/x/sys v0.31.0 // indirect; 系统相关的Go工具库（开源），提供与操作系统相关的功能和工具
           golang.org/x/text v0.23.0 // indirect; 文字处理库（开源），用于进行文字处理和操作
           google.golang.org/genproto/googleapis/rpc v0.0.0-20250212204824-5a70512c5d8b // indirect; Google API的协议缓冲库（开源），用于处理Google API的协议缓冲数据
           google.golang.org/grpc v1.70.0 // gRPC框架（开源），用于高效的服务间通信，提供高性能的远程过程调用（RPC）功能
           gopkg.in/ini.v1 v1.67.0 // indirect; INI文件解析库（开源），用于解析INI格式的文件
           gopkg.in/natefinch/lumberjack.v2 v2.2.1 // indirect; 日志轮换工具（开源），用于自动轮换日志文件，便于管理和查看日志
           gopkg.in/yaml.v3 v3.0.1 // indirect; YAML格式解析库（开源），用于解析YAML格式的文件
       )

       // 其他直接依赖
       require (
           github.com/ClickHouse/clickhouse-go/v2 v2.32.1 // ClickHouse的官方Go客户端（开源），用于与ClickHouse数据库进行交互
           github.com/golang/protobuf v1.5.4 // Go语言的protobuf库（开源），用于处理Protocol Buffers数据
           github.com/grpc-ecosystem/go-grpc-middleware v1.4.0 // gRPC中间件库（开源），用于添加和管理gRPC的中间件
           github.com/grpc-ecosystem/grpc-gateway/v2 v2.26.1 // gRPC网关库（开源），用于将gRPC服务转换为HTTP/JSON服务
           github.com/segmentio/kafka-go v0.4.47 // Kafka的Go客户端（开源），用于与Kafka消息队列进行交互
           github.com/xxl-job/xxl-job-executor-go v1.2.0 // xxl-job的Go执行器（开源），用于执行分布式任务调度
           go.mongodb.org/mongo-driver/v2 v2.0.0 // MongoDB的Go驱动（开源），用于与MongoDB数据库进行交互
           google.golang.org/genproto/googleapis/api v0.0.0-20250204164813-702378808489 // Google API的协议缓冲库（开源），用于处理Google API的协议缓冲数据
           google.golang.org/grpc/cmd/protoc-gen-go-grpc v1.5.1 // gRPC代码生成工具（开源），用于生成gRPC服务的代码
       )

       // 其他间接依赖（通常由其他直接依赖自动引入）
       require (
           github.com/ClickHouse/ch-go v0.65.0 // indirect; ClickHouse的底层通信库（开源），用于与ClickHouse数据库进行底层通信
           github.com/andybalholm/brotli v1.1.1 // indirect; Brotli压缩库（开源），用于进行Brotli压缩和解压缩操作
           github.com/go-basic/ipv4 v1.0.0 // indirect; IPv4处理库（开源），用于处理IPv4相关
        ```
---

## **3. 其他 Kratos 相关规范 (Other Kratos Specifics)**

*   **(待补充)** *这里可以补充更多与 Kratos 框架使用相关的具体规范，例如:*
    *   *配置文件 (`configs/`) 的推荐结构和命名。*
    *   *Kratos 中间件的开发和使用规范。*
    *   *Kratos 服务注册与发现的最佳实践。*
    *   *Kratos 错误处理与 API 错误码的映射规则。*
    *   *Wire Provider 的编写规范等。*

---
