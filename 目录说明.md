# Luban Agent 目录结构说明

本文档描述了Luban Agent项目的完整目录结构。

## 目录结构说明

1. `ai-rules/`: AI规则管理根目录，包含所有开发规范和标准
   - `backend/`: 后端相关规则
   - `frontend/`: 前端相关规则

2. `ai-workflows/`: AI工作流程目录，定义各类工作流程
   - `design/`: 设计相关工作流
   - `dev/`: 开发相关工作流
   - `test/`: 测试相关工作流
   - `maintenance/`: 运维相关工作流
   - `progress/`: 进度管理相关

3. `ai-generates/`: AI生成内容根目录，存放AI生成的各类文档
   - `design/`: 设计文档
   - `user-stories/`: 用户故事
   - `action-plans/`: 实施计划
   - `tasks/`: 任务管理
   - `issues/`: 问题管理
   - `test-cases/`: 测试用例
   - `test-scripts/`: 测试脚本
   - `verification/`: 验收管理
   - `analytics/`: 数据分析 
   - `be-service/`: 后端服务列表


## 完整项目目录结构树

```text
luban-agent/
├─ ai-generates/                                         # AI生成内容根目录
│  ├─ action-plans/                                      # 实施计划目录
│  │  ├─ plan-US101.md                                   # US101实施计划
│  │  └─ plan-US102.md                                   # US102实施计划
│  ├─ analytics/                                         # 数据分析目录
│  │  ├─ dashboards.md                                   # 监控面板配置
│  │  ├─ events-mapping.md                               # 事件映射文档
│  │  └─ metrics-definition.md                           # 指标定义文档
│  ├─ be-service/                                        # 后端具体服务功能目录
│  │  ├─ common.md                                       # 公共模块目录
│  │  ├─ ucenter.md                                      #  用户中心功能目录
│  ├─ design/                                            # 设计文档目录
│  │  ├─ ai-fe-td教师端_1期_作业报告_analyzed-Gemini-2.5-Pro.md
│  │  ├─ ai-prd-AI课中-练习组件1.0_analyzed-gemini-pro.md
│  │  ├─ ...
│  │  └─ ai-tdd-产课工具_3期_AI课配课课程管理工具.md
│  ├─ issues/                                            # 问题管理目录
│  │  ├─ milestone-M001/                                 # 里程碑M001问题
│  │  │  ├─ issue-F001.md                                # 功能型问题F001
│  │  │  └─ issue-F002.md                                # 缺陷型问题F002
│  │  └─ milestone-M002/                                 # 里程碑M002问题
│  ├─ raw-docs/                                          # 原始文档目录
│  │  ├─ api/                                           # API接口文档目录
│  │  │  ├─ api-auth.md                                 # 认证模块API文档
│  │  │  └─ api-user.md                                 # 用户模块API文档
│  │  ├─ design/                                        # 系统设计文档目录
│  │  │  ├─ design-arch.md                              # 系统架构设计
│  │  │  ├─ design-db.md                                # 数据库设计
│  │  │  ├─ 运营后台用户中心-技术设计.md
│  │  │  └─ 运营后台用户中心-技术设计_analyzed.md
│  │  └─ prd/                                           # 产品需求文档目录
│  │     ├─ pdf/                                       # PDF文档子目录
│  │     │  ├─ PRD-内容管理平台2.1-单题上传和编辑.pdf
│  │     │  ├─ ...
│  │     │  └─ 运营后台产品_1期_账号_需求PRD.pdf
│  │     ├─ PRD -  内容管理平台.md
│  │     ├─ PRD -  内容管理平台_analyzed.md
│  │     ├─ ...
│  │     ├─ 运营后台产品_1期_账号_需求PRD.md
│  │     └─ 运营后台产品_1期_账号_需求PRD_analyzed.md
│  ├─ tasks/                                             # 任务管理目录
│  │  ├─ milestone-M001/                                 # 里程碑M001任务
│  │  │  ├─ task-T001.md                                 # 任务T001
│  │  │  └─ task-T002.md                                 # 任务T002
│  │  └─ milestone-M002/                                 # 里程碑M002任务
│  │     └─ task-T004.md                                 # 任务T004
│  ├─ test-cases/                                        # 测试用例目录
│  │  ├─ milestone-M001/                                 # 里程碑M001测试用例
│  │  │  ├─ case-TC001.md                                # 测试用例TC001
│  │  │  └─ case-TC002.md                                # 测试用例TC002
│  │  └─ milestone-M002/                                 # 里程碑M002测试用例
│  ├─ test-scripts/                                      # 测试脚本目录
│  │  ├─ milestone-M001/                                 # 里程碑M001测试脚本
│  │  │  ├─ script-TS001.md                              # 测试脚本TS001
│  │  │  └─ script-TS002.md                              # 测试脚本TS002
│  │  └─ milestone-M002/                                 # 里程碑M002测试脚本
│  ├─ user-stories/                                      # 用户故事目录
│  │  ├─ user-story-US101.md                             # 用户故事US101
│  │  └─ user-story-US102.md                             # 用户故事US102
│  └─ verification/                                      # 验收管理目录
│     ├─ checklist-US101.md                              # US101验收清单
│     └─ checklist-US102.md                              # US102验收清单
├─ ai-rules/                                             # AI规则管理根目录
│  ├─ backend/                                           # 后端规则目录
│  │  ├─ guidelines/                                    # 后端开发指南目录
│  │  │  ├─ code-review-guide.md                         # 代码审查指南
│  │  │  ├─ general-dev-standards.md                     # 通用开发规范
│  │  │  ├─ rules-api-code.md                            # API开发规范
│  │  │  └─ rules-logging-code.md                        # 日志开发规范
│  │  ├─ project-rules/                                 # 项目专用规则目录
│  │  │  ├─ golang/                                      # Golang项目规则
│  │  │  │  ├─ code-templates/                          # Golang代码模板目录
│  │  │  │  │  ├─ golang-common.md                       # 通用代码模板
│  │  │  │  │  ├─ golang-const.md                        # 常量定义模板
│  │  │  │  │  ├─ golang-entity.md                       # 实体类模板
│  │  │  │  │  ├─ golang-service.md                      # 服务层模板
│  │  │  │  │  └─ golang-utils.md                        # 工具类模板
│  │  │  │  ├─ ddd/                                     # DDD架构模板目录
│  │  │  │  │  ├─ controller.mdc                         # 控制器层模板
│  │  │  │  │  ├─ dao.mdc                                # 数据访问层模板
│  │  │  │  │  ├─ domain.mdc                             # 领域层模板
│  │  │  │  │  ├─ dto.mdc                                # 数据传输对象模板
│  │  │  │  │  ├─ router.mdc                             # 路由层模板
│  │  │  │  │  └─ service.mdc                            # 服务层模板
│  │  │  │  ├─ ddd-coding-standards.mdc                   # DDD开发规范
│  │  │  │  ├─ gin.mdc                                  # Gin框架规范
│  │  │  │  ├─ golang.mdc                               # Golang基础规范
│  │  │  │  ├─ project-progress.md                      # 项目进度文档
│  │  │  │  ├─ rules-golang-code.mdc                    # Golang代码规则
│  │  │  │  ├─ rules-kratos-code.mdc                    # Kratos框架开发规则
│  │  │  │  ├─ rules-plan-deme.md                       # 开发规划示例
│  │  │  │  └─ rules-tech-stack.md                      # 技术栈说明文档
│  │  │  ├─ php/                                         # PHP项目规则
│  │  │  │  └─ rules-php-code.mdc                         # PHP代码规则
│  │  │  └─ python/                                      # Python项目规则
│  │  │     ├─ code-templates/                          # Python代码模板目录
│  │  │     │  ├─ python-common.md                       # 通用代码模板
│  │  │     │  ├─ python-controller.md                   # 控制器模板
│  │  │     │  ├─ python-entity.md                       # 实体类模板
│  │  │     │  ├─ python-repository.md                   # 数据仓储模板
│  │  │     │  ├─ python-service.md                      # 服务层模板
│  │  │     │  └─ python-utils.md                        # 工具类模板
│  │  │     ├─ ddd/                                     # Python DDD架构规则
│  │  │     │  ├─ application.mdc                        # 应用层规范
│  │  │     │  ├─ interface.mdc                          # 接口层规范
│  │  │     │  ├─ model.mdc                              # 模型层规范
│  │  │     │  ├─ repository.mdc                         # 仓储层规范
│  │  │     │  └─ service.mdc                            # 服务层规范
│  │  │     ├─ tests/                                   # 测试相关规范
│  │  │     │  ├─ application.mdc                        # 应用层测试规范
│  │  │     │  ├─ interface.mdc                          # 接口层测试规范
│  │  │     │  ├─ model.mdc                              # 模型层测试规范
│  │  │     │  ├─ repository.mdc                         # 仓储层测试规范
│  │  │     │  ├─ service.mdc                            # 服务层测试规范
│  │  │     │  └─ unittest.mdc                           # 单元测试规范
│  │  │     ├─ fastapi.mdc                              # FastAPI框架规范
│  │  │     ├─ python.mdc                               # Python基础规范
│  │  │     ├─ rules-fastapi-code.md                    # FastAPI开发规则
│  │  │     └─ rules-python-code.mdc                    # Python代码规则
│  │  ├─ public-rules/                                  # 后端公共开发规则目录
│  │  │  └─ common-code.mdc                              # 通用代码规范
│  │  └─ server-rules/                                  # 服务端组件规则目录
│  │     ├─ rules-clickhouse-code.md                     # ClickHouse数据库规则
│  │     ├─ rules-kafka-code.mdc                         # Kafka消息队列规则
│  │     ├─ rules-mysql-code.mdc                         # MySQL数据库规则
│  │     ├─ rules-postgresql-code.mdc                    # PostgreSQL数据库规则
│  │     └─ rules-redis-code.md                          # Redis缓存规则
│  └─ frontend/                                          # 前端规则目录
│     ├─ code-templates/                                # 前端代码模板目录
│     ├─ guidelines/                                    # 前端开发指南目录
│     │  ├─ build.mdc                                   # 构建规范
│     │  ├─ general.mdc                                 # 通用开发规范
│     │  ├─ vite-common-build.mdc                       # Vite通用构建规范
│     │  ├─ vite-react-build.mdc                        # Vite React构建规范
│     │  └─ vite-vue-build.mdc                          # Vite Vue构建规范
│     ├─ project-rules/                                 # 前端项目专用规则目录
│     │  ├─ detail-code.mdc                             # 详细代码规范
│     │  ├─ react.mdc                                   # React项目规范
│     │  ├─ tasktree.mdc                                # 任务树规范
│     │  └─ ui.mdc                                      # UI开发规范
│     └─ public-rules/                                  # 前端公共开发规则目录
│        ├─ common-code.mdc                              # 通用代码规范
│        ├─ rules-android-code.mdc                       # Android代码规则
│        ├─ rules-codecheck.mdc                          # 代码检查规则
│        ├─ rules-figma-code.mdc                         # Figma设计规则
│        ├─ rules-frontend-code.mdc                      # 前端代码规则
│        ├─ rules-react-code.mdc                         # React代码规则
│        └─ rules-vue-code.mdc                           # Vue代码规则
└─ ai-workflows/                                         # AI工作流程目录
   ├─ deployment/                                        # 部署工作流目录
   │  └─ flow-deployment.md                              # 部署工作流程
   ├─ design/                                            # 设计工作流目录
   │  ├─ gen-action-plan-workflow.md                     # 行动计划生成流程
   │  ├─ gen-issue-workflow.md                           # 问题生成流程
   │  ├─ gen-task-workflow.md                            # 任务生成流程
   │  └─ gen-user-story-workflow.md                      # 用户故事生成流程
   ├─ dev-be/                                            # 后端开发工作流目录
   │  ├─ ai-dev-new-project-workflow.md                  # 新项目开发流程
   │  ├─ be-ai-dev-bugfix-workflow.md                    # 缺陷修复流程
   │  └─ be-ai-dev-feature-workflow.md                   # 功能开发流程
   ├─ dev-fe/                                            # 前端开发工作流目录
   │  ├─ fe-ai-dev-new-project-workflow.md               # 前端新项目开发流程
   │  ├─ fe-design-to-code-workflow.md                   # 设计转代码流程
   │  └─ fe-design-to-code-workflow.mdc                  # 设计转代码流程(详细版)
   ├─ maintenance/                                       # 运维工作流目录
   │  ├─ flow-monitoring.md                              # 系统监控流程
   │  └─ flow-optimization.md                            # 系统优化流程
   ├─ progress/                                          # 进度管理目录
   │  ├─ progress-checklist.md                           # 进度检查清单文档
   │  ├─ progress-plan.md                                # 项目进度计划文档
   │  └─ progress-task.md                                # 任务进度跟踪文档
   └─ test/                                              # 测试工作流目录
      ├─ flow-testing.md                                 # 测试工作流程
      ├─ gen-api-test-case-workflow.md                   # API测试用例生成流程
      ├─ gen-fe-test-case-workflow.md                    # 前端测试用例生成流程
      └─ gen-web-test-case-workflow.md                   # Web测试用例生成流程

```

