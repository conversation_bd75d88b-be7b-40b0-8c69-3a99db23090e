产课工具_1期_AI课生产工具

[产课工具_1期_All-in-one](https://wcng60ba718p.feishu.cn/wiki/TBw0wvyxKi9BSuko87ScEy2unSf)

结合教研管理的需求：[产课流程](https://uathqw3qd4.feishu.cn/docx/SCxJdGjcnohMbRxLSHxcVRIAn8g)

技术文档[ai课生产工具1期-逐字稿工具](https://wcng60ba718p.feishu.cn/wiki/LQPaw2Vt1iU9Xgk2imocUntYnff)

**版本管理**

| 版本号 | 日期 | 变更人 | 变更类型 | 变更详情 |
| --- | --- | --- | --- | --- |
| V1.0.0 | 2025-4-8 | 袁全 | 新建 | 新建文档 |
| V5.0.0 | 4-12 | 袁全 | 升稿 | 补充核心页面交互图 |
| V9.0.0 | 4-15 | 袁全 | 升稿 | 完成9稿 |
|  | 4-16 | 袁全 | 修改了ui图 | ![in_table_image_OGJabJSWZox6HrxWGgXcyPWynxf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498517257.png)

清除修改按钮（p1） |
|  | 4-17 | 袁全 | 根据技术评审添加新说明 | 添加「取消生成功能」：ccloading过程中，显示“取消生成”，点击后弹窗显示二次确认弹窗：“确定取消生成吗？内容将回退到之前版本。” |
|  | 4-18 | 袁全 | 解析字段修改 | 经和确认，为了便于获取数字人+课程类型信息高中-数学-概念课-稿件名.zip   生成：excel中 [ 学科、学段、类型、名称、url ]类型枚举值：概念课、题型课、串讲课、专题课、讲解课、综合课、实验课、同步课、复习课、知识课cc |
|  | 4-21 | 袁全 | 补充：板书编辑：“板书内文字转为图片”需求说明 | ![in_table_image_BVolbC7ZeoNY0hxJvJIckoA5nKb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498520408.png) |

![analyze_in_table_image_Hbi4b1yUeoofbfxaFuhcrUOUnVd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498516534.png)

![analyze_in_table_image_OGJabJSWZox6HrxWGgXcyPWynxf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498517257.png)

- 在识别到用户有新修改后，按钮亮起
- 用户点击后：文本框内容回退至上一次提交的数据状态
- 点“确定”，2/3内容重置到之前版本，结束loading；
- 点“返回”，关闭弹窗，继续loading。
![image_B8Q6bMmcDo5zY3xfIS4ce4hBnxU](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498517914.png)

![image_DQmBbeJJiopoyfx8fQRc7W3dnlg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498518675.png)

![analyze_in_table_image_LgbtbiVXXoe2YXx9q4LcUVp2nyc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498519714.png)

![analyze_in_table_image_BVolbC7ZeoNY0hxJvJIckoA5nKb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498520408.png)

**设计稿**

UE：https://mastergo.com/file/149512448061157?fileOpenFrom=home&page_id=0%3A2



### 一、背景和目标

#### 1.1、需求背景

本期产品目标是：完成供教研老师使用的AI课生产第1步工具，让获得该地址的教研老师可以在PC:web浏览器内输入对应课程地址后，****进行AI课生产的第1步：“逐字稿、朗读稿、板书动画的编辑和校对”****。

为了简化工具尽快上线，本期：**不做任务管理，****校验内网，****不对地址访问做身份控制**

- 业务层面：
- 教研老师：
- 场景1:课程从0到1生产，使用该工具进行“逐字稿、朗读稿、板书动画编辑”，完成后提交至：圈画生产
- 场景2:用于课程修改，使用该工具对已有内容进行：“逐字稿、朗读稿、板书动画修改”，完成后提交重新进行圈画生产
- 各校老师
- 未来使用该工具对AI课内容进行二创
|  | 教研老师 | C端用户 |
| --- | --- | --- |
| P0核心功能 | 编辑：段落核对：逐字稿、系统动画、朗读稿 |  |
| 未来功能 |  | 使用本期工具进行二创 |

#### 1.2、项目收益

完成产品工具P0核心功能，确保在5月上线的版本可以跑通整个产课业务MVP流程，可供老师使用



#### 1.3、覆盖用户

目标用户：教研教师



#### 1.4、方案简述

1. 上下游关系：第1步 逐字稿&朗读稿生产
![board_X1qtwxmsihWp5QbR1g1cN3r3nUC](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498512183.png)

1. 用户访问路径：
1. 总部教研管理在线下完成课程ID创建后，通过feishu表格管理地址，并分配教研老师
1. 教研复制本课的逐字稿编辑“地址1”，粘贴到web浏览器中“地址1”，回车后可进行编辑
![board_RgiYwQEfdhEL09bmPA5ceV8gnmh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498513243.png)

1. 工具交互
- 用户一个段落内完成：0.Part编辑、1.逐字稿编辑、2.朗读稿编辑、3.板书编辑 
- 依次完成各段落后，提交整课
**0.Part编辑**

编辑Part名称

可编辑：位置上下移、在之前/后新增段落、删除本段落

新增Part：默认空值，用户在「1逐字稿」输入内容后「保存」，再点「重新生产」生成后续内容

![image_AOX7bUhHLoIPoTx54lecpz3ynod](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498521059.png)

按顺序展示段落名称

![image_AhmJbL2yUopFzSxfulOcdvGRn2e](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498521702.png)

「编辑模式」

![image_OqNgbZS0YoeYY4xWHaDcpCQAnec](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498522519.png)

新增段落：初始态-空值

**1.逐字稿编辑**

1. 进入本页面时，老师可以先查看预先生成好的视频；
1. 个别字小修改：可编辑文本，编辑LaTex公式，完成后点击「保存」
1. 整段大修改：可复制整段，到豆包等工具重写，再贴回文本框点击「保存」，并在下一步点击「重新生成」，重新生成对应的2、3模块
![image_IDl5b9alEoLn2yxaS17cCscWnYd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498523281.png)

小修改：直接保存

![image_Vv8bbGgWMo90EVx8mTccxc6Mnwb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498524134.png)

大修改：重新生成音频、板书（loading态）

**2.朗读稿编辑**

1. 系统按“。/！/？/......”为标识分割多个句子，用户可以逐句编辑文本，也可以听整段
1. 一般情况：修改文本后，用户点击「生成配音」，否则做报错提示；点击后，可保存继续下一步
1. 配音难以纠正的情况：用户可进行下载，并在本地完成音频加工后重新上传


![image_VzonbbGhno7hfTxP5pOcEQcPn0c](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498524751.png)

逐句编辑，报错提示

![image_HmRzbxbZsojvVvxQKnDc81wynGc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498525339.png)

生成配音，清除提示

**3.板书编辑**

1. 左侧：系统由逐字稿生成对应的 markdown 格式的板书内容，包含：## 标题、公式、文字，及xml符合标识的高亮标记<key1></key1>，用户可进行修改
1. 右侧：左侧修改完成后，点击「更新预览」，可生产对应的预览效果
1. 完成编辑后，点击“生成视频”，进入更新视频的loading态，完成更新后，视频换成最新，视频时间更新为“更新于某日期几点几分”


![image_QsOCbFcYGoloAMxZDUtcrtaonqe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498525919.png)

板书编辑，点“更新预览”看效果

![image_ULCWbJQxzoLcjhxWDk7cFe9Gnqc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498526740.png)

完成点「生成视频」

预计loading20min

**4.预览视频，完成整个段落**

视频生成完成后，由loading态变为「待处理」，用户可点击进入观看视频

若遇到：字幕错误，截带有时间轴的播放图，在excel中at算法同学报错修改

本段已无问题，则点击「保存完整段落」，加锁标记；除非点击「重新编辑」否则只可预览、不可修改

![image_G1u5b2EcGoHuVVxZUwqcNgqhnVf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498527428.png)

新视频生成完毕，右上角可点「保存完整段落」

![image_Jgwrbt3DwoRS4jxCbMbcBkb1nSg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498528443.png)

保存后该段落计作✅，转成“只读”

### 二、名词说明

- 内容的粒度
- 稿件（GuideWidgetSet）：
1. 一个zip包、算法处理的一批数据
1. 含若干文稿组件（Part）的集合
1. 后续用来作为文稿组件集合的查询
- 文稿（GuideWidget），也叫Part：
1. 由三稿（逐字稿、朗读稿、板书）生成的一整个视频资源
1. ai课中的一个视频组件，对应组件类型是：板书
- 生产的几类实体
- 逐字稿（Transcript）：三稿之一，课程的详细讲稿，用于投喂LLM进行 朗读稿+板书 自动化生产
- 朗读稿（Audioscript）：三稿之一，学生端听到的内容，相当于：「逐字稿」的口语稿+ 音频，可逐句编辑
- 朗读稿中的句子（AudioSentence）：朗读稿中每段文案
- 朗读稿中的音频地址（AudioUrl）：朗读稿中每段文案的音频地址
- 板书：三稿之一，学生端看到的可交互页面的内容，比「逐字稿」精炼，带有“圈画”、“高亮”、“划线”等动画
- 板书视频：由三稿生成的的视频内容
- 字幕：学生端看到的字幕内容                                         
### 三、业务流程

**逐字稿工具-核心功能**

![board_KCoCwtHdyhgO9IbAvSGc2esTndu](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498514296.png)

**复制课程页-流程**

![board_IhcvwqHO4hICJnb8MSYcQBNjnrg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498515699.png)

### 四、需求概览

| 序号 | 需求名称 | 内容概述 | 优先级 |
| --- | --- | --- | --- |
| 1 | 读取Part信息 |  | P0 |
| 2 | 预览完整视频 |  | P0 |
| 3 | 逐字稿编辑 |  | P0 |
| 4 | 朗读稿编辑 |  | P0 |
| 5 | 板书编辑 |  | P0 |
| 6 | 提交 |  | P0 |
| 7 | 基于url生成一节新课 |  | P0 |

- 获取各段落顺序、标题名称、状态（未开始、进行中、已完成）、loading状态
- 支持编辑：标题文案，操作：上下移、添加新段落、删除
- 获取算法最开始的Demo视频
- 获取在「3板书动画」最新生产的视频
- 获取算法最开始的逐字稿信息
- 编辑文本框、插入LaTex公式、保存逐字稿
- 提交逐字稿
- 重新生成：整段朗读稿 & 板书动画
- 获取算法最开始的逐字稿信息（按句展示）
- 逐句编辑：文本框、播放、生成音频、下载、上传
- 整段音频播放
- 提交朗读稿
- 获取算法最开始的板书信息
- 编辑markdown、编辑高亮标记、插入公式
- 更新预览：板书及高亮
- 提交板书
- 重新生成视频
- 保存整课
- 重新编辑
- 复制页：提交url+新课名称
- 在新课页面的编辑
### 五、详细产品方案

#### 5.1逐字稿工具页编辑

| 序号 | 模块 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- | --- |
| 1 | 逐字稿工具：顶栏 | 展示本课信息 | 标题：逐字稿生产课程名称：当前课程名称 | ![in_table_image_CnqLblTVSoZaW2xqClncWNVmnDt](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498529306.png) |  |
| 2 | 逐字稿工具:左侧边栏Part模块 | 读取Part信息 | ![in_table_image_U7xvbukAOoKjerxaZiJcdSD4nse](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498529915.png)

交互设计： | ![in_table_image_VZtLbJ5jSoEAHCx2ex7cifAnnjb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498530620.png) |  |
| 3 |  | 编辑Part信息 |  | ![in_table_image_SAyCbtrGXosjb6x5gCEcStmSnPf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498531525.png) |  |
| 4 | 逐字稿工具:右侧边栏视频预览模块 | 预览完整视频 | 提供完整视频的预览功能，用户可以查看算法生成的初始Demo视频以及在板书动画编辑后最新生成的视频。交互设计： | ![in_table_image_Wo5nbgJylomMJUxTqxScUPdGnzg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498532188.png) |  |
| 5 | 逐字稿工具:右侧边栏逐字稿编辑模块 | 逐字稿编辑小改场景 | 提供逐字稿的编辑功能，用户可以对逐字稿进行文本编辑、插入LaTeX公式，并保存编辑内容。交互设计： | ![in_table_image_EojCbK8NXo3w3jx1MC4cW4AVn6c](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498533636.png) |  |
| 6 |  | 逐字稿编辑大改场景（用户自己判断） | 对于用户对整段内容不满意的情况交互设计： | ![in_table_image_Yiujbht6MoXkoQxATzscWmoyntF](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498535064.png) |  |
| 7 | 逐字稿工具:右侧边栏朗读稿编辑模块 | 朗读稿展示 | 提供朗读稿的展示功能： | ![in_table_image_NgoRbJG48oED9LxoW0ccMpzenQh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498535621.png) |  |
| 8 |  | 朗读稿音频播放 | 提供朗读稿的播放功能：交互设计： | 同上 |  |
| 9 |  | 朗读稿修改 | 朗读稿编辑功能，用户可以对朗读稿进行逐句编辑、生成音频、下载和上传音频等操作；每次只能对一句话进行播放/高亮编辑。交互设计： | ![in_table_image_CxfjbUnB0oCGtOx7MRUcJp32nnd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498536801.png) |  |
| 10 | 逐字稿工具:右侧边栏板书编辑模块 | 板书展示和编辑 | 提供板书的编辑功能，用户可以编辑markdown格式的板书内容，并进行高亮标记的编辑。交互设计： | ![in_table_image_LDIwb590goA06FxbCZzcQO7Snsu](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498537496.png) |  |
| 12 |  | 生成板书预览 |  | ![in_table_image_UXZYbtDfdooC1Yxc2HtcnVGhn5f](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498538393.png) |  |
| 13 |  | 重新生成视频 |  | ![in_table_image_IJjeb01EnoDGgCxlekjcG98cnxY](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498539077.png) |  |
| 14 | 逐字稿工具:右侧边栏视频预览模块 | 提交整段/重新编辑 |  | ![in_table_image_LNm8bv6ZTo94PrxsHuJcDMH3n7f](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498540350.png) |  |
| 15 | 逐字稿工具:顶部 | 提交整课 | 用户各个Part都状态设置为✅，则此时可点击右上角“保存”，对整课进行保存 |  |  |

![analyze_in_table_image_CnqLblTVSoZaW2xqClncWNVmnDt](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498529306.png)

- 根据用户是否进入该Part有修改标记：未开始、进行中、已完成：通过不同颜色或图标标识Part的当前状态，如绿色表示“已完成”，蓝色表示“进行中”，灰色表示“未开始”。
- 未开始：尚未在该Part做任何修改
- 进行中：已有修改、但暂未“保存整个段落”；或点击了“重新编辑”
- loading-icon：目前有执行中的生成任务
- ！-icon：loading完成，待用户处理
- 已完成：已点击“保存整个段落”，且没有进入“重新编辑”
- ✅-icon：已完成
![analyze_in_table_image_U7xvbukAOoKjerxaZiJcdSD4nse](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498529915.png)

- 展示方式：左侧导航栏以列表形式展示所有Part，每个Part显示其标题和状态；数字序号自然递增；
![analyze_in_table_image_VZtLbJ5jSoEAHCx2ex7cifAnnjb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498530620.png)

- 编辑模式操作说明：
- 点击「编辑模式」进入编辑态，直至点击“保存/取消”或“删除，弹窗中点确认”退出编辑态
- 支持“编辑标题”、“上下移动”、“新增”、“删除”：
- 编辑标题：10个汉字以内，超出编辑不可保存
- 上下移动：点击后可上移/下移1格，此时段落序号随之改变，点击保存后留存数据，否则不变
- 新增：点击段落前/后的按钮，点击后按钮亮起代表生效（可新增前后各一个），点击保存后新增数据，否则不变
- 删除：点击后二次弹窗提示“点击确定后将清空本Part全部内容，该操作不可撤回，是否继续？确定/取消”；点击删除后后续所有Part顺次提前一个序号
![analyze_in_table_image_SAyCbtrGXosjb6x5gCEcStmSnPf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498531525.png)

- 视频播放器：在页面顶部放置一个视频播放器，用于播放完整视频。
- 视频更新：展示更新时间，用户可以查看最新生成的视频。
- 播放控制：视频播放器应具备基本的播放控制功能，如播放/暂停、拖动进度条、音量调节、倍速播放（0.5、0.75、1、1.25、1.5、1.75、2）、字幕开关等。
![analyze_in_table_image_Wo5nbgJylomMJUxTqxScUPdGnzg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498532188.png)

- 文本编辑框：提供富文本编辑框，用户可以在其中进行逐字稿的编辑，在文本编辑框内的公式展示为预览态
- 公式插入工具：同时提供公式插入按钮，用户可以点击后在新弹窗内的输入框插入由外部复制过来的LaTeX公式
- 提交按钮：
- 在识别到用户有新修改后，提交按钮亮起
- 用户编辑完成后可以点击提交，即将本地结果同步到服务端
- 清除修改按钮：
- 在识别到用户有新修改后，按钮亮起
- 用户点击后：文本框内容回退至上一次提交的数据状态
![analyze_in_table_image_VKHRbD9m1oJahBxNfWjcoekynrc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498532947.png)

![analyze_in_table_image_EojCbK8NXo3w3jx1MC4cW4AVn6c](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498533636.png)

- 文本编辑框--复制工具：点击后复制文本框的全部内容到用户本地的剪切板，便于用户粘贴到外部
- 「重新生成朗读稿和板书」按钮，在用户点击后：
- 提交“逐字稿”中的内容到服务端，并重新生产后面模块内容
- 后面两个模块置灰色，变为：loading状态
- button高亮，文案变为：「正在重新生成朗读稿和板书」
- loading过程中，显示“取消生成”，点击后弹窗显示二次确认弹窗：“确定取消生成吗？内容将回退到之前版本。”
- 点“确定”，2/3内容重置到之前版本，结束loading；
- 点“返回”，关闭弹窗，继续loading。
- 左侧Part状态icon变化为loading中，直至完成加载，变为“！”状态
- 这个过程预计需要10～20分钟
![analyze_in_table_image_ZxmqbrA7Yo0pwox20apccHfJnwc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498534319.png)

![analyze_in_table_image_Yiujbht6MoXkoQxATzscWmoyntF](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498535064.png)

![analyze_in_table_image_NgoRbJG48oED9LxoW0ccMpzenQh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498535621.png)

- 音频播放器：在模块底部放置一个音频播放器，用于播放完整音频。
- 音频更新：展示用户提交后的最新拼接完成的视频，用户逐个句子编辑后，只要点击了“生成配音”，就可以在这里听到最新的音频
- 播放控制：播放器应具备基本的播放控制功能，如播放/暂停、拖动进度条、音量调节、倍速播放（0.5、0.75、1、1.25、1.5、1.75、2）
- 文本编辑：将朗读稿按句分割，用户可以逐句进行编辑。
- 生成配音：在识别到文本框修改后，以“！”符号提示用户提供生成音频，用户点击后可以生成音频（注意不可排队，一次只有一段音频生成）
- 音频播放：提供音频播放功能，用户可以预览生成的音频。
- 下载：提供本句子下载到本地，命名为：《xx课程名称朗读稿第x句音频时间戳.格式》
- 上传：点击后，用户可以上传本地音频到服务端，上传完成后，同时根据识别音频结果，更新这句话的朗读稿文本
- 提交按钮：
- 在识别到用户有修改后，提交按钮亮起
- 用户编辑完成后可以点击提交，即将本地结果同步到服务端
![analyze_in_table_image_BnlAbL6LwoXSSOx1UmwcXfsXnuh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498536186.png)

![analyze_in_table_image_CxfjbUnB0oCGtOx7MRUcJp32nnd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498536801.png)

- markdown编辑器：
- 用户可以在其中进行板书文字编辑
- 标题格式：## 三级标题名称
- 高亮格式&位置标记：使用成对的xml标签提供高亮标记功能，用户可以对板书内容进行高亮标记的添加和删除
- 公式插入工具：同时提供公式插入按钮，用户可以点击后在新弹窗内的输入框插入由外部复制过来的LaTeX公式
![analyze_in_table_image_LDIwb590goA06FxbCZzcQO7Snsu](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498537496.png)

- 预览功能：提供预览功能，用户可以点击「更新预览」数秒后，预览编辑后的板书效果（静态图，需要标记出标题/正文；高亮、圈画、划线在板书上的效果）。
- 提交按钮：
- 在识别到用户有修改后，提交按钮亮起
- 用户编辑完成后可以点击提交，即将本地结果同步到服务端
![analyze_in_table_image_UXZYbtDfdooC1Yxc2HtcnVGhn5f](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498538393.png)

- 「生成视频」按钮，在用户点击后：
- 若有前3步中存在未提交的内容，提示弹窗
- 文案：“请注意，存在：尚未提交的修改。生成的视频将不包含仅在本地修改、尚未提交的内容，是否继续？“
- 按钮：“继续生成”--按服务器存的生成，“取消”--中断生成
- 提交“逐字稿、朗读稿、板书”中的内容到服务端，并重新生产视频
- 视频预览模块置灰色，变为：loading状态
- 更新时间处展示未文案：「新视频生成中」，直至完成加载，变为更新时间的展示
- button高亮，文案变为：「正在重新生成视频」
- loading过程中，显示“取消生成”，点击后弹窗显示二次确认弹窗：“确定取消生成吗？内容将回退到之前版本。”
- 点“确定”，视频重置到之前版本，结束loading；
- 点“返回”，关闭弹窗，继续loading。
- 左侧Part状态icon变化为loading中，直至完成加载，变为“！”状态
- 这个过程预计需要20分钟
![analyze_in_table_image_IJjeb01EnoDGgCxlekjcG98cnxY](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498539077.png)

- 用户可以保存整个Part内容
- 交互设计：
- 保存完整段落：
- 点击后整个part状态修改为“已完成”，显示✅，页面除了可预览视频播放以外，其他模块设置为不可编辑状态
- 点击后button高亮，文案变为：「重新修改」
- 重新修改：
- 点击后整个part状态修改为“进行中”，显示！，页面所有模块转为编辑状态
- 点击后button高亮，文案变为：「保存完整段落」
![analyze_in_table_image_B6wZbGDxeoh8JjxUsUOcAWxdnlb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498539813.png)

![analyze_in_table_image_LNm8bv6ZTo94PrxsHuJcDMH3n7f](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498540350.png)

#### 5.2复制课程页编辑

| 序号 | 模块 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- | --- |
| 1 | 逐字稿工具:复制课程页 | 复制课程页 | URL固定：xxxx.copy-lesson.html交互： | ![in_table_image_AkCabYOFRodDkbxcOfNcS1egnSc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498540924.png) | P0 |

- 在内网状态下可以访问
- 复制自URL：输入之前已存在的URL地址
- 课程名称：输入重命名的课程名称
- 点击“生成新课程”后：
- 在浏览器新开一个页面，该新URL就是新课地址；
- 新课名称：用户刚刚输入的课程名称
- 在这个地址编辑的所有内容都编辑和原始URL解藕；
- 点击“清空”后：
- 清空两个输入框内内容。
![analyze_in_table_image_AkCabYOFRodDkbxcOfNcS1egnSc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498540924.png)

### 六、数据需求

#### 6.1 效果追踪

描述计划监控数据效果的方式及所需数据字段

#### 6.2 数据埋点

| 页面 | 模块 | 动作 | 埋点名称 | 图示（如需） |
| --- | --- | --- | --- | --- |
|  |  |  |  |  |
|  |  |  |  |  |
|  |  |  |  |  |
|  |  |  |  |  |

### 七、a/b实验需求

若有，则写清楚实验方案

**实验目的**

XXX

**实验对象**

限制条件：端/版本/用户

实验组策略：

对照组策略：

**实验及分流**

启动时间、启动时用户量、预计持续时间

**评价指标**

结果指标（标明预期提升值）、过程指标

**暂无**



### 附：评审记录

记录存档历次需求评审中的会议纪要，方便追踪

举例：

**20250204 - 初评纪要**

1. 会后尽快确定巩固类型本期题目类型范围@杨宇航
已确认，并补充进文档

1. 数据下发方式待服务端和客户端最终确认@服务端rd @客户端rd
确定服务端下发，客户端仅做兜底逻辑

1. ...


**20250321 - 二次评审纪要**

1. XX
1. 
