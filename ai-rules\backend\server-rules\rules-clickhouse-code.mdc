# ClickHouse 数据库设计规范与约束

本文件详细定义了在使用 ClickHouse 数据库时必须遵循的设计规范和技术约束。

## 一、通用数据库设计原则

### 1.1 SOLID原则在ClickHouse中的应用
- **单一职责原则 (SRP)**：每个表应表示一个明确的业务实体或分析主题，虽然ClickHouse常用于宽表，但逻辑划分依然重要
- **开闭原则 (OCP)**：表结构应能灵活扩展，通过添加新列或物化视图支持新的聚合维度，避免频繁改动核心表结构
- **里氏替换原则 (LSP)**：确保表间数据转换的兼容性
- **数据隔离原则 (DSP)**：严格按照业务逻辑或分析需求对数据进行组织
- **迪米特法则 (LoD)**：表间关联应确保必要性和简洁性，通过清晰的关联键处理

### 1.2 设计原则
- **高内聚低耦合**：表内数据高度相关于分析主题，与其他实体的依赖通过清晰关联键处理
- **KISS原则**：数据库表设计保持简单明了，易于理解和维护
- **YAGNI原则**：避免过度设计，仅在需求明确时进行添加和优化
- **关注点分离**：将原始数据层、中间聚合层、最终报表层在逻辑上分离
- **DRY原则**：避免无控制的数据重复，通过物化视图管理和同步冗余

## 二、应避免的设计反模式

### 2.1 数据组织反模式
- **数据孤岛**：避免不必要的数据割裂，确保数据可以有效整合分析
- **巨石表极端情况**：避免一个表包含过多完全不相关的分析维度和指标
- **过度规范化**：避免大量JOIN操作，应倾向于反规范化设计
- **重复数据失控**：避免无规划的数据重复导致存储浪费和不一致风险

### 2.2 技术实现反模式
- **过度依赖复杂数据类型**：使用Array、Tuple、Nested、JSON等类型时需考虑对性能的影响
- **冗余或无效的跳数索引**：过多索引增加写入开销，设计不当的索引对性能提升有限
- **不合理的排序键**：选择不当会严重影响性能
- **不合理的分区策略**：分区过细或过粗都会影响查询和维护效率
- **滥用轻量级删除**：需理解其对查询性能和存储的影响

## 三、ClickHouse特定设计约束

### 3.1 命名规范
- **数据库 (Database)**：以`db_`开头，使用小写字母、数字和下划线组成
  - 示例：`db_order_logs`、`db_user_behavior_logs`
- **表 (Table)**：以`tbl_`开头，使用小写字母、数字和下划线组成，清晰表达业务含义
  - 示例：`tbl_user_actions`、`tbl_order_details`
- **字段 (Column)**：使用小写字母、数字和下划线
  - 示例：`user_id`、`event_timestamp`、`order_amount`
- **视图 (View / Materialized View)**：使用小写字母、数字和下划线
  - 普通视图：加`v_`前缀，示例：`v_user_summary`
  - 物化视图：加`mv_`前缀，示例：`mv_daily_sales`
- **避免使用ClickHouse关键字**作为名称

### 3.2 数据类型选择
- **整数类型**：根据数值范围选择`Int8`至`Int256`或对应无符号类型
  - **推荐**：优先考虑使用`Int64`或`UInt64`，便于后端代码类型处理
- **浮点数**：`Float32`、`Float64`，推荐使用`Float64`保证精度
- **定点数**：`Decimal(P, S)`，用于需要精确小数计算的场景
- **字符串**：
  - `String`：动态长度字符串，**常用且推荐**
  - `FixedString(N)`：固定长度字符串，仅在长度绝对固定且对性能有极致要求时考虑
- **日期与时间**：
  - `Date`：存储日期，不含时间，精度为天
  - `Date32`：存储日期，范围更广（1900-2299）
  - `DateTime`：存储日期和时间，精度为秒，**常用且推荐用于存储事件发生时间**
  - `DateTime64(P[, timezone])`：存储日期和时间，带有亚秒级精度
- **枚举**：`Enum8`、`Enum16`，用于存储预定义集合中的值
- **UUID**：`UUID`标准类型，推荐用于全局唯一标识符
- **布尔型**：**统一使用`UInt8`类型，约定`1`代表`true`，`0`代表`false`**
- **数组**：`Array(T)`，用于存储同一类型的数组
- **Nullable**：`Nullable(T)`，允许字段存储`NULL`值，按需使用
- **LowCardinality**：`LowCardinality(T)`，**强烈推荐用于基数较低的字符串或枚举字段**

### 3.3 表引擎选择
- **MergeTree家族引擎**（首选）：
  - `MergeTree`：**最常用且推荐的基础引擎**
  - `ReplacingMergeTree[(ver)]`：在合并时根据排序键去除重复行
  - `SummingMergeTree([columns])`：在合并时对数值类型列进行汇总
  - `AggregatingMergeTree`：存储预聚合函数的中间状态
  - `CollapsingMergeTree(sign)`：基于sign列进行异步折叠删除
  - `VersionedCollapsingMergeTree(sign, version)`：结合版本控制的折叠
  - `GraphiteMergeTree`：用于存储Graphite监控数据

- **日志引擎**：
  - `TinyLog`、`StripeLog`、`Log`：适用于小批量数据或临时表，**生产环境核心业务表应避免使用**

- **集成引擎**：
  - `Kafka`、`JDBC`、`ODBC`、`HDFS`、`S3`：用于与外部系统集成

- **特殊引擎**：
  - `Distributed`：**核心引擎之一，用于实现分布式查询和写入**
  - `MaterializedView`：用于创建物化视图
  - `Memory`：数据仅存于内存
  - `Buffer`：提供写入缓冲区
  - `Dictionary`：用于创建外部字典
  - `Null`：用于测试或数据路由

### 3.4 索引与排序键
- **排序键 (`ORDER BY`)**：**MergeTree家族引擎最重要的性能优化手段**
  - 必须在`CREATE TABLE`语句中显式指定
  - 选择查询中最常用于`WHERE`条件过滤、`GROUP BY`或`ORDER BY`的列
  - 排序键定义了数据在磁盘上的物理存储顺序
  - 通常选择2-4个列，将过滤选择性最高的列放在前面
  
- **主键 (`PRIMARY KEY`)**：默认与`ORDER BY`相同，必须是`ORDER BY`定义的列的前缀

- **跳数索引 (`INDEX`)**：可选的数据跳过索引
  - 语法：`INDEX index_name expression TYPE index_type GRANULARITY N`
  - 索引类型：`minmax`、`set(max_rows)`、`ngrambf_v1`、`tokenbf_v1`、`bloom_filter`
  - **仅在排序键无法有效覆盖某些重要查询场景时，才谨慎添加**

### 3.5 分区策略
- **分区 (`PARTITION BY`)**：用于数据管理和查询优化的重要机制
  - 必须在`CREATE TABLE`语句中显式指定分区表达式
  - **最常用的分区键是时间相关的列**：
    - 按月：`PARTITION BY toYYYYMM(event_date)`
    - 按天：`PARTITION BY toYYYYMMDD(event_date)`或`PARTITION BY event_date`
  
- **分区键选择考虑因素**：
  - 查询过滤：是否经常按该键过滤，实现分区裁剪
  - 数据保留策略（TTL）：是否基于该键按分区删除旧数据
  - 分区数量：活跃分区保持在几百到几千个以内
  - 数据写入模式：确保数据能均匀写入各分区

### 3.6 数据压缩
- **默认压缩**：`MergeTree`引擎默认使用`LZ4`压缩算法
- **常用CODEC**：
  - `NONE`：不压缩
  - `LZ4`（默认）：压缩速度快，压缩率一般
  - `LZ4HC[(level)]`：高压缩率的LZ4
  - `ZSTD[(level)]`：压缩率高，推荐的LZ4替代方案
  - `Delta(delta_bytes)`：差分编码，适用于单调变化数据
  - `DoubleDelta`：二阶差分
  - `Gorilla`：专为浮点数时间序列设计
  - `T64`：专为64位整数设计

### 3.7 数据保留策略 (TTL)
- **行级TTL**：`TTL event_date + INTERVAL 1 MONTH DELETE`
- **条件删除TTL**：`TTL event_date + INTERVAL 1 WEEK DELETE WHERE status = 'temp'`
- **列级TTL**：设置列的TTL，过期时值被替换为默认值
- **表级TTL**：可以在数据过期后重新压缩
- **合理设置TTL是管理存储成本和维护查询性能的关键**

### 3.8 标准字段适配
- **创建时间 (`created_at`)**：推荐使用`DateTime`或`DateTime64`类型，可设置默认值`DEFAULT now()`
- **更新时间 (`updated_at`)**：ClickHouse主要面向分析场景，传统的行级自动更新不常见
- **软删除机制**：
  - **推荐使用`CollapsingMergeTree(sign)`引擎**
  - 添加`sign Int8`类型列，插入时`sign = 1`，删除时`sign = -1`
  - 查询时需要对`sign`列进行聚合
- **主键ID**：推荐使用`UUID`类型，或使用自然主键组合

### 3.9 视图与物化视图
- **普通视图**：只是查询的别名，不存储数据，适用于简化复杂查询
- **物化视图**：**核心特性之一，用于实现高效的预聚合和数据转换**
  - 当源表插入数据时，触发物化视图的SELECT查询
  - 目标表通常选用`SummingMergeTree`、`AggregatingMergeTree`或`ReplacingMergeTree`
  - `POPULATE`关键字表示创建时立即导入源表现有数据
  - 能够显著加速基于预聚合结果的查询

### 3.10 分布式表设计
- **Distributed引擎设计要点**：
  - 集群名称：在ClickHouse配置文件中定义
  - 底层本地表：确保所有分片上的本地表结构一致
  - **分片键选择标准**：
    - 高基数且均匀分布：确保数据能均匀分布到所有分片
    - 查询模式：考虑查询过滤和聚合下推
    - 数据局部性：权衡关联数据的分片分布
    - 避免基数过低或分布严重不均的列

## 四、创建表规范示例

### 4.1 标准表创建模板

```sql
CREATE TABLE db_business.tbl_user_action_logs
(
    -- 业务字段
    `event_time` DateTime64(3) CODEC(Delta(4), ZSTD(1)) COMMENT '事件发生时间（毫秒精度）',
    `user_id` UInt64 CODEC(T64, ZSTD(1)) COMMENT '用户ID',
    `action_type` LowCardinality(String) COMMENT '行为类型（view, click, add_to_cart, order, pay）',
    `product_id` UInt64 CODEC(T64, ZSTD(1)) COMMENT '商品ID',
    `session_id` String COMMENT '会话ID',
    `device_type` LowCardinality(String) COMMENT '设备类型（PC, Mobile, Tablet）',
    `ip_address` IPv6 COMMENT '用户IP地址',
    `event_properties` Map(String, String) COMMENT '事件属性（键值对）',
    
    -- 辅助字段
    `log_date` Date MATERIALIZED toDate(event_time) COMMENT '事件日期（用于分区）',
    
    -- 标准审计字段
    `created_at` DateTime DEFAULT now() COMMENT '记录创建时间'
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(log_date)
ORDER BY (log_date, user_id, event_time, action_type)
TTL log_date + INTERVAL 365 DAY DELETE
SETTINGS index_granularity = 8192, merge_with_ttl_timeout = 86400
COMMENT '电商平台用户行为日志表，记录用户的各种交互行为';

-- 跳数索引示例（根据实际查询需求添加）
ALTER TABLE db_business.tbl_user_action_logs 
ADD INDEX idx_product_id product_id TYPE bloom_filter GRANULARITY 1;

ALTER TABLE db_business.tbl_user_action_logs 
ADD INDEX idx_ip_address ip_address TYPE set(0) GRANULARITY 1;
```

### 4.2 物化视图创建示例

```sql
-- 目标表：按日聚合用户行为统计
CREATE TABLE db_business.tbl_user_actions_daily_summary
(
    `event_date` Date COMMENT '事件日期',
    `user_id` UInt64 COMMENT '用户ID',
    `action_count` AggregateFunction(count) COMMENT '行为次数聚合状态',
    `unique_products` AggregateFunction(uniq, UInt64) COMMENT '唯一商品数聚合状态'
)
ENGINE = AggregatingMergeTree()
PARTITION BY toYYYYMM(event_date)
ORDER BY (event_date, user_id)
COMMENT '用户每日行为聚合统计表';

-- 物化视图：自动聚合数据
CREATE MATERIALIZED VIEW db_business.mv_user_actions_daily_builder
TO db_business.tbl_user_actions_daily_summary
AS SELECT
    toDate(event_time) AS event_date,
    user_id,
    countState() AS action_count,
    uniqState(product_id) AS unique_products
FROM db_business.tbl_user_action_logs
GROUP BY event_date, user_id;

-- 查询聚合结果
SELECT
    event_date,
    user_id,
    countMerge(action_count) AS total_actions,
    uniqMerge(unique_products) AS unique_product_count
FROM db_business.tbl_user_actions_daily_summary
WHERE event_date >= '2024-01-01'
GROUP BY event_date, user_id
ORDER BY event_date DESC, total_actions DESC;
```

## 五、Go代码交互规范

### 5.1 驱动选择
- **推荐使用**：`github.com/ClickHouse/clickhouse-go/v2`官方驱动
- **避免使用**：GORM等ORM，ClickHouse的SQL方言与传统RDBMS差异较大
- **推荐使用**：原生SQL或轻量级查询构建器（如`sqlx`）

### 5.2 数据类型映射
- `DateTime` → `time.Time`
- `Nullable(T)` → `*T`（Go指针）或`sql.Null*`类型
- `LowCardinality(T)` → 基础类型`T`（如`string`）
- `Array(T)` → `[]T`切片类型

### 5.3 最佳实践
- **批量插入**：务必使用批量插入而非逐条插入
- **Context控制**：操作数据库时传递`context.Context`控制超时和取消
- **连接池配置**：配置合理的数据库连接池参数
- **避免SELECT \***：只选择需要的列，尤其对于宽表
- **参数化查询**：使用占位符进行参数化查询，防止SQL注入

### 5.4 代码示例

```go
// 批量插入示例
func BatchInsertUserActions(ctx context.Context, conn clickhouse.Conn, actions []UserAction) error {
    batch, err := conn.PrepareBatch(ctx, `
        INSERT INTO db_business.tbl_user_action_logs 
        (event_time, user_id, action_type, product_id, session_id, device_type, ip_address)
    `)
    if err != nil {
        return err
    }
    
    for _, action := range actions {
        err := batch.Append(
            action.EventTime,
            action.UserID,
            action.ActionType,
            action.ProductID,
            action.SessionID,
            action.DeviceType,
            action.IPAddress,
        )
        if err != nil {
            return err
        }
    }
    
    return batch.Send()
}

// 查询示例
func GetUserActionsByDateRange(ctx context.Context, conn clickhouse.Conn, userID uint64, startDate, endDate time.Time) ([]UserAction, error) {
    rows, err := conn.Query(ctx, `
        SELECT event_time, user_id, action_type, product_id, session_id, device_type, ip_address
        FROM db_business.tbl_user_action_logs
        WHERE user_id = ? AND log_date BETWEEN ? AND ?
        ORDER BY event_time DESC
    `, userID, startDate, endDate)
    if err != nil {
        return nil, err
    }
    defer rows.Close()
    
    var actions []UserAction
    for rows.Next() {
        var action UserAction
        err := rows.Scan(
            &action.EventTime,
            &action.UserID,
            &action.ActionType,
            &action.ProductID,
            &action.SessionID,
            &action.DeviceType,
            &action.IPAddress,
        )
        if err != nil {
            return nil, err
        }
        actions = append(actions, action)
    }
    
    return actions, rows.Err()
}
```

## 六、监控与维护

### 6.1 关键监控指标
- 查询延迟和吞吐量
- CPU和内存使用率
- 磁盘I/O性能
- Merge过程监控
- 分区数量和大小
- TTL执行状态

### 6.2 性能优化建议
- 定期监控和优化排序键
- 合理设置分区策略
- 适当使用跳数索引
- 优化压缩算法选择
- 监控物化视图性能影响

### 6.3 版本管理
- 保持ClickHouse服务和客户端驱动版本更新
- 了解版本间的兼容性变化
- 在测试环境验证新版本特性

---

> 📚 **参考资料**：
> - [ClickHouse官方文档](https://clickhouse.com/docs/)
> - [ClickHouse Go驱动文档](https://github.com/ClickHouse/clickhouse-go)
> - [ClickHouse最佳实践指南](https://clickhouse.com/docs/en/guides/best-practices/)
> - [ClickHouse性能优化指南](https://clickhouse.com/docs/en/operations/performance/)
