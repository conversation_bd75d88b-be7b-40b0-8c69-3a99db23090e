---
description: 
globs: 
alwaysApply: false
---
#AI 助Go 工程化开发工作流 (v1.0)

## 当前开发进度
当前阶段：[待开始]

## 1. 引言

### 1.1. 目的
本文档旨在定义一套结构化的工作流程，指导开发者如何有效利用 AI 助手（如 Cursor 内的 AI）与项目已定义的规范文档相结合，高效、高质量地完成基于 Kratos/DDD 框架的 Go 项目工程化开发。

### 1.2. 核心思想
本流程强调 **人机协作 (Human-AI Collaboration)**。AI 作为强大的辅助工具，负责执行明确指令下的代码生成、检查、文档编写等任务；
开发者则扮演**设计者、需求阐述者、评审者、测试执行者和最终决策者**的角色。
流程成功的关键在于**分阶段、上下文感知**地与 AI 交互，在正确的时间提供正确的输入和指令。

### 1.3. Phase汇总系统
为了优化AI开发过程中的token消耗，本工作流引入了**Phase汇总系统**：

**核心机制:**
*   每个Phase完成后，使用结构化汇总模板生成当前Phase的完整汇总文档
*   汇总文档包含：已生成文件清单、实现汇总、技术特性确认、下一Phase输入资源等
*   后续Phase开始时，直接读取前一Phase汇总文档作为输入，而非重新分析大量规范文档

**🔧 汇总文档存储位置** (基于实际项目结构):
*   `project-summaries/phase-1-dao-summary-{module}.md`: DAO层汇总
*   `project-summaries/phase-2-domain-summary-{module}.md`: Domain层汇总
*   `project-summaries/phase-3-dto-summary-{module}.md`: DTO层汇总
*   `project-summaries/phase-4-service-summary-{module}.md`: Service层汇总
*   `project-summaries/phase-5-controller-summary-{module}.md`: Controller层汇总
*   `project-summaries/phase-6-router-summary-{module}.md`: Router层汇总
*   `project-summaries/phase-7-integration-summary-{module}.md`: 集成测试汇总
*   `project-summaries/phase-8-production-summary-{module}.md`: 生产准备汇总

**📊 优化效果** (基于实际验证数据):
*   **Token消耗减少**: 60-80% (从15000-25000降至3000-5000 tokens)
*   **开发效率提升**: 上下文理解时间从10-15分钟缩短到2-3分钟
*   **问题定位速度**: 提升3-5倍
*   **信息传递完整性**: 确保跨Phase开发的一致性

**🚀 AI助手使用最佳实践**:
```bash
# 1. 开始新模块开发前，读取所有相关汇总
read_file project-summaries/phase-*-summary-user.md

# 2. 开发特定Phase时，重点读取依赖的Phase汇总
read_file project-summaries/phase-1-dao-summary-user.md
read_file project-summaries/phase-2-domain-summary-user.md

# 3. 完成Phase后，立即生成汇总文档
write_to_file project-summaries/phase-X-{layer}-summary-{module}.md
```

**使用方法:** 详见 [`ai-dev-simple-operation-guide.md`](./ai-dev-simple-operation-guide.md) 实战操作指南

### 1.4. Phase验证机制
为了确保每个Phase的开发质量，本工作流集成了**Phase验证机制**：

**🔧 验证维度** (基于实际开发经验优化):
*   **编译验证**: `go vet ./...`, `go build ./...`, `wire ./cmd/gil_teacher/`
*   **单元测试验证**: `go test ./... -v -cover` (覆盖率>80%)
*   **代码质量验证**: `gofmt -l .`, `golangci-lint run`, `gocyclo -over 15 .`
*   **接口验证**: API可访问性测试 (适用于Controller/Router层)
*   **数据库验证**: 连接测试、CRUD操作验证 (适用于DAO层)
*   **性能验证**: `go test ./... -bench=. -memprofile=mem.prof`
*   **安全验证**: `gosec ./...`, 依赖安全检查
*   **🚀 集成验证**: 完整API测试、健康检查、CORS验证

**验证时机:**
*   每个Phase核心任务完成后立即执行验证
*   验证失败时提供标准化的错误处理流程
*   验证通过后才能进入Phase汇总生成步骤

**🔧 错误处理** (基于实际问题修复经验):
*   **编译失败**:
     - `unreachable code`: 检查return语句后的代码
     - `format string`: 检查fmt.Printf参数匹配
     - 包导入问题: 检查import路径和未使用导入
*   **测试失败**: 逻辑分析 → 代码修复 → 测试更新
*   **性能问题**: 瓶颈分析 → 算法优化 → 性能重测
*   **安全问题**: 漏洞修复 → 依赖更新 → 安全重扫
*   **依赖注入失败**: 检查Provider函数签名和循环依赖
*   **API测试失败**: 检查路由注册、CORS配置、参数验证

### 1.4. 参考规范
本流程的执行依赖于项目内已定义的各类规范文档，开发者和 AI 在各阶段需参考：
*   `luban-agent/ai-rules/backend/project-rules/golang/rules-kratos-code.mdc`: Kratos 框架使用、项目结构、核心组件和依赖。
*   `luban-agent/ai-rules/backend/project-rules/golang/rules-golang-code.mdc`: 通用 Go 编码规范、架构原则、数据库、测试、版本控制等。
*   `luban-agent/ai-rules/backend/server-rules/rules-postgresql-code.mdc`: PostgreSQL 数据库设计规范。
*   `luban-agent/ai-rules/backend/server-rules/rules-clickhouse-code.md` : ClickHouse 数据库设计规范。
*   `luban-agent/ai-rules/backend/server-rules/rules-kafka-code.mdc`: Kafka 消息队列设计规范。
*   `luban-agent/ai-rules/backend/server-rules/rules-mysql-code.mdc`: MySQL 数据库设计规范。
*   `luban-agent/ai-rules/backend/server-rules/rules-redis-code.mdc`: Redis 数据库设计规范。
*   `luban-agent/ai-rules/backend/project-rules/golang/rules-tech-stack.md`: 项目技术栈的使用规范。
*   `luban-agent/ai-rules/backend/guidelines/rules-logging-code.md`: 日志设计规范。
*   `luban-agent/ai-rules/backend/guidelines/rules-api-code.md`: API 设计规范
*   `luban-agent/ai-rules/backend/project-rules/golang/project-progress.md`: 项目进度跟踪和状态更新。

各 DDD 层级的具体实现规范和代码示例 (**需重点优化和细化**)。
*   `luban-agent/ai-rules/backend/project-rules/golang/ddd/dao.mdc`,: 数据访问层 (DAO) 的设计与实现规范。
*   `luban-agent/ai-rules/backend/project-rules/golang/ddd/domain.mdc`,: 领域模型 (Domain Model) 的设计与实现规范。
*   `luban-agent/ai-rules/backend/project-rules/golang/ddd/dto.mdc`,: 数据传输对象 (DTO) 的设计与实现规范。
*   `luban-agent/ai-rules/backend/project-rules/golang/ddd/service.mdc`,: 服务层 (Service Layer) 的设计与实现规范。
*   `luban-agent/ai-rules/backend/project-rules/golang/ddd/controller.mdc`,: 控制器层 (Controller Layer) 的设计与实现规范。
*   `luban-agent/ai-rules/backend/project-rules/golang/ddd/router.mdc` :: 路由配置的设计与实现规范。


## 2. 开发工作流阶段

本工作流将开发过程划分为多个阶段，旨在通过人机协作逐步构建和验证系统。核心的层级构建顺序遵循：**DAO层 -> Domain层 -> DTO层 -> Service层 -> Controller层 -> Router层**。下面的阶段划分将指导这一过程的执行。

### Phase 0: 准备与设计规划

*   **阶段核心:** 人类主导，AI 辅助。
*   **AI 角色:** 设计助理 / 验证者。
*   **目标:** 明确需求，设计核心架构、领域模型和 API 契约。
*   **流程:**
    1.  **需求理解与技术设计 (开发者):**
        *   深入理解 PRD，编写初步技术设计文档 (核心模型草稿、API 列表、DB 草稿)。
    2.  **AI 辅助完善设计 (可选):**
        *   **输入:** 初步技术设计文档, 相关规范。
        *   **指令 (示例):** 请求 AI 检查模型一致性、生成 Protobuf 草稿等。
    3.  **最终设计确认 (开发者):**
        *   确认领域模型 (`domain/struct.go`)。
        *   确认 Repository 接口 (`domain/interface.go`)。
        *   **[执行]** `make build` 生成代码。

**完成标志:**
*   确认领域模型 (`domain/struct.go`)。
*   确认 Repository 接口 (`domain/interface.go`)。
*   已执行 `make build` 生成代码。

### 下一步操作
根据工作流，Phase 0 完成后，下一步将进入 **Phase 1: DAO层构建与验证 (DAO Layer Construction & Validation)**。
AI 将为您生成数据库迁移脚本、GORM Model 定义、DAO 实现和单元测试。

### Phase 1: DAO层构建与验证 (DAO Layer Construction & Validation)

**目标:** 设计数据库表结构，生成数据库迁移脚本，实现数据访问对象 (DAO) 层，并完成单元测试。

**核心输入:**
*   `{biz_module}/domain/struct.go`: 已确认的领域模型 (Domain Model)。
*   `{biz_module}/domain/interface.go`: 已确认的数据操作接口 (Repository Interface)。
*   `luban-agent/ai-rules/backend/server-rules/rules-postgresql-code.mdc`: PostgreSQL 数据库设计规范。
*   `luban-agent/ai-rules/backend/server-rules/rules-clickhouse-code.md`: ClickHouse 数据库设计规范。
*   `luban-agent/ai-rules/backend/project-rules/golang/ddd/dao.mdc`: DAO 层开发规范与代码示例。
*   `luban-agent/ai-rules/backend/project-rules/golang/rules-golang-code.mdc`: Go 语言通用规范

**输出:**
*   `docs/sql/{YYYYMMDD}/{timestamp}_{biz_module}.sql`: DDL 数据库迁移脚本。
*   `app/{biz_module}/dao/impl/{entity}_impl.go`: DAO 接口实现。
*   `app/{biz_module}/dao/impl/{entity}_impl_test.go`: DAO 实现的单元测试。

**步骤:**

1.  **数据库设计验证与规范化 (DB Design Validation & Normalization):**
    *   **输入:** 领域模型 (`struct.go`), Repository 接口 (`interface.go`), 以及项目选定的具体数据库设计规范 (例如 `luban-agent/ai-rules/backend/server-rules/rules-postgresql-code.mdc` 或 `luban-agent/ai-rules/backend/server-rules/rules-clickhouse-code.md`)。
    *   **人工:** 开发者基于业务需求，初步构思数据表结构草案（字段、类型、关系）。
    *   **AI 辅助:**
        *   AI **执行详细审核**，对照上述指定的数据库规范文件，评估草案的合理性，并提供 **具体的优化建议**。
        *   数据库设计需遵循详细的评审标准和示例，请参见下面 `数据库设计评审标准与示例` 部分 (AI注意：如果此部分不存在或内容单薄，请提示用户补充或引用更详细的规范)。
        *   产出:** 经过验证和规范化的数据库表设计方案，**附带 AI 的结构化评审摘要与建议**。
        *   验证:** 确认最终设计方案满足业务需求且符合数据库规范。

2.  **GORM Model 定义 (GORM Model Definition):**
    *   **目标:** 根据确认的数据库表设计方案，在 DAO 层定义 GORM Model (DO/PO - Data Object / Persistent Object)。
    *   **核心输入:** 确认后的数据库表设计方案, `luban-agent/ai-rules/backend/project-rules/golang/ddd/dao.mdc` (关于GORM Model定义、字段类型、tag规范等内容)。
    *   **步骤:**
        1.  **(开发者/AI)** 在 `app/{biz_module}/dao/model/` (或类似路径，例如 `internal/data/model/`) 目录下，为每个数据库表创建一个对应的 `.go` 文件 (例如 `user.go` 对应 `users` 表)。
        2.  **(AI 生成/开发者编写)** 参照 `dao.mdc` 规范和已确认的表结构，定义 GORM Model 结构体。确保：
            *   结构体名与表名对应 (可使用 `TableName() string` 方法指定)。
            *   字段名与列名对应，使用 Go 的 `CamelCase`。
            *   字段类型准确映射数据库类型 (如 `int64` 对应 `BIGINT`, `string` 对应 `VARCHAR` 等)。
            *   正确使用 GORM tags (如 `gorm:"primaryKey"`, `gorm:"column:user_name"`, `gorm:"uniqueIndex"`, `gorm:"type:varchar(255)"`, `gorm:"default:xxx"`, `gorm:"comment:'用户邮箱'"` 等)。
            *   包含 `CreatedAt`, `UpdatedAt` (通常为 `int64` 时间戳) 和 `DeletedAt` (类型 `gorm.DeletedAt` 用于软删除) 字段，并添加相应 GORM tags。
            *   遵循 `luban-agent/ai-rules/backend/project-rules/golang/rules-golang-code.mdc` 中关于结构体和JSON tag (如果Model也用作API响应的一部分，但不推荐) 的通用规范。
        3.  **(开发者评审)** 仔细核对 Model 定义与表结构、`dao.mdc` 规范的一致性。
    *   **关键检查点:** ✅ Model 文件和结构体命名规范 ✅ 字段与列完全对应 ✅ GORM tags 正确且完整 ✅ 包含标准审计字段 (CreatedAt, UpdatedAt, DeletedAt) ✅ 符合 `dao.mdc` 规范。
    *   **输出物:** `app/{biz_module}/dao/model/{entity}.go` (或类似路径) 文件。
    *   **验收标准:** GORM Model 定义准确反映数据库表结构，并严格遵循相关规范。

3.  **数据库设计与迁移脚本生成 (DB Schema Design & Migration Script Generation):**
    *   **输入:** 确认后的数据库表设计方案 (及对应的 GORM Model 作为参考)。
    *   **AI 辅助:** AI 根据确认的设计方案，生成 `CREATE TABLE` DDL 语句，并封装成符合项目迁移工具规范的 SQL 迁移脚本。脚本应**存放于 `docs/sql/{YYYYMMDD}/` 目录下**，**文件名格式为 `{timestamp}_{biz_module}.sql`**。
    *   **人工:** 开发者评审 AI 生成的 DDL 和迁移脚本的位置及内容。
    *   **产出:** `docs/sql/{YYYYMMDD}/{timestamp}_{biz_module}.sql` 文件。

4.  **执行数据库迁移 (Execute DB Migration):**
    *   **人工:** 开发者在本地或开发环境**手动执行**位于 `docs/sql/{YYYYMMDD}/` 目录下的数据库迁移脚本，确保表结构成功创建。
    *   **验证:** 确认数据库中已存在正确的表结构。

5.  **DAO 层实现生成 (DAO Implementation Generation):**
    *   **核心步骤:**
        1. **准备阶段**
           - 确认Domain层接口定义完整性
           - 确认数据库表结构规范性
           - 检查GORM Model定义

        2. **实现阶段**
           - 参考 `luban-agent/ai-rules/backend/project-rules/golang/ddd/dao.mdc` 中的详细规范
           - 遵循分层架构原则
           - 实现必要的性能优化
           - 添加完整的错误处理
           - 实现可观察性支持

        3. **🔧 验证阶段** (基于实际开发经验优化)
           - **编译验证**:
             ```bash
             go vet ./app/{biz_module}/dao/...
             go build ./app/{biz_module}/dao/...
             wire ./cmd/gil_teacher/
             ```
           - **单元测试验证**:
             ```bash
             go test ./app/{biz_module}/dao/... -v -cover
             # 要求覆盖率>80%
             ```
           - **代码质量验证**:
             ```bash
             gofmt -l ./app/{biz_module}/dao/
             golangci-lint run ./app/{biz_module}/dao/...
             ```
           - **数据库验证**: 连接测试、CRUD操作验证
           - **性能验证**:
             ```bash
             go test ./app/{biz_module}/dao/... -bench=. -memprofile=mem.prof
             ```
           - **安全验证**:
             ```bash
             gosec ./app/{biz_module}/dao/...
             ```
           
           **🚨 验证失败处理** (基于实际问题修复经验):
           - **编译失败**:
             - `unreachable code`: 检查return语句后是否有多余代码
             - `format string`: 检查fmt.Printf等格式化函数的参数匹配
             - 包导入问题: 检查import路径，移除未使用的导入
           - **测试失败**: 分析失败原因 → 修复逻辑 → 更新测试
           - **性能问题**: 分析瓶颈 → 优化查询 → 重新测试
           - **安全问题**: 修复漏洞 → 更新依赖 → 重新扫描
           - **依赖注入失败**: 检查Provider函数签名，确保返回值类型正确

    *   **关键检查点:**
        - ✅ Repository接口实现完整性
        - ✅ 错误处理机制规范性
        - ✅ 性能优化措施到位
        - ✅ 可观察性支持完整
        - ✅ 测试覆盖率达标

    *   **输出物:**
        - DAO层代码文件
        - 单元测试文件
        - 错误定义文件
        - 依赖注入配置

    *   **验收标准:** DAO 层的实现需满足详细的验收标准，请参见 `luban-agent/ai-rules/backend/guidelines/general-dev-standards.md` 中的 `DAO 层验收标准` 部分。 

    *   **参考文档:**
        - DAO层详细实现规范：`luban-agent/ai-rules/backend/project-rules/golang/ddd/dao.mdc`
        - postgresql数据库设计规范：`luban-agent/ai-rules/backend/server-rules/rules-postgresql-code.mdc`
        - clickhouse数据库设计规范：`luban-agent/ai-rules/backend/server-rules/rules-clickhouse-code.md`
        - Go编码规范：`luban-agent/ai-rules/backend/project-rules/golang/rules-golang-code.mdc`
        - Kratos框架规范：`luban-agent/ai-rules/backend/project-rules/golang/rules-kratos-code.mdc`

6.  **DAO 层单元测试生成 (DAO Unit Test Generation):**
    *   **输入:** 
        - DAO 实现代码 (`{entity}_impl.go`)
        - Repository 接口 (`interface.go`)
        - Go 测试规范
        - DAO 测试策略/示例 (`ddd/dao.mdc`)

    *   **步骤:**
        1. **测试环境准备**
           - 创建测试文件 `{entity}_impl_test.go`
           - 设置测试环境（mock、logger等）
           - 准备测试数据生成器

        2. **基础CRUD测试**
           - Create操作测试（成功、失败场景）
           - Read操作测试（存在、不存在场景）
           - Update操作测试（成功、失败场景）
           - Delete操作测试（成功、失败场景）

        3. **业务约束测试**
           - 唯一性约束测试
           - 外键约束测试
           - 必填字段验证
           - 字段长度限制

        4. **事务场景测试**
           - 事务提交测试
           - 事务回滚测试
           - 嵌套事务测试
           - 事务隔离级别测试

        5. **并发场景测试**
           - 乐观锁测试
           - 悲观锁测试
           - 死锁检测测试

        6. **异常场景测试**
           - 数据库连接失败
           - SQL语法错误
           - 超时处理
           - 资源耗尽

        7. **性能测试**
           - 单记录操作基准测试
           - 批量操作基准测试
           - 复杂查询基准测试
           - 并发操作基准测试

    *   **验收标准:**
        1. **覆盖率要求**
           - 语句覆盖率 > 90%
           - 分支覆盖率 > 80%
           - 条件覆盖率 > 80%

        2. **代码质量**
           - 测试代码符合命名规范
           - 测试用例描述清晰
           - 注释完整规范
           - 无代码重复

        3. **测试完整性**
           - 覆盖所有接口方法
           - 包含成功和失败场景
           - 验证所有业务约束
           - 包含边界条件测试

        4. **Mock使用规范**
           - 正确模拟数据库操作
           - 合理设置返回值
           - 准确模拟错误场景
           - 验证调用预期

    *   **关键检查点:**
        - ✅ 测试文件结构规范
        - ✅ 测试场景覆盖完整
        - ✅ Mock使用正确
        - ✅ 断言方式合理
        - ✅ 性能测试达标
        - ✅ 测试代码可维护

    *   **输出:**
        - 单元测试文件：`app/{biz_module}/dao/impl/{entity}_impl_test.go`
        - 测试覆盖率报告
        - 基准测试报告
        - 标准测试报告，格式如下：

```markdown
# DAO层单元测试报告

## 1. 基本信息
- **模块名称**：{biz_module}
- **测试时间**：YYYY-MM-DD HH:mm:ss
- **测试环境**：
  - Go版本：{go_version}
  - GORM版本：{gorm_version}
  - PostgreSQL版本：{pg_version}
- **测试执行人**：{tester}

## 2. 测试范围
### 2.1 测试文件
- `app/{biz_module}/dao/impl/{entity}_impl_test.go`
- 测试用例总数：{total_test_cases}
- 测试函数总数：{total_test_functions}

### 2.2 测试场景覆盖
- [ ] 基础CRUD操作
  - [ ] Create：{create_test_cases}个场景
  - [ ] Read：{read_test_cases}个场景
  - [ ] Update：{update_test_cases}个场景
  - [ ] Delete：{delete_test_cases}个场景

- [ ] 业务约束测试
  - [ ] 唯一性约束：{unique_test_cases}个场景
  - [ ] 外键约束：{fk_test_cases}个场景
  - [ ] 字段验证：{validation_test_cases}个场景

- [ ] 事务测试
  - [ ] 提交场景：{commit_test_cases}个场景
  - [ ] 回滚场景：{rollback_test_cases}个场景
  - [ ] 嵌套事务：{nested_tx_test_cases}个场景

- [ ] 并发测试
  - [ ] 乐观锁：{optimistic_lock_test_cases}个场景
  - [ ] 悲观锁：{pessimistic_lock_test_cases}个场景
  - [ ] 死锁处理：{deadlock_test_cases}个场景

## 3. 测试结果
### 3.1 覆盖率统计
- 语句覆盖率：{statement_coverage}%
- 分支覆盖率：{branch_coverage}%
- 条件覆盖率：{condition_coverage}%

### 3.2 测试执行结果
- 总用例数：{total_cases}
- 通过用例：{passed_cases}
- 失败用例：{failed_cases}
- 跳过用例：{skipped_cases}
- 执行时间：{execution_time}s

### 3.3 性能测试结果
| 测试场景 | 操作数 | 平均耗时(ms) | 内存分配(bytes) |
|---------|--------|-------------|----------------|
| 单条创建 | {ops}  | {avg_time}  | {memory_alloc} |
| 批量创建 | {ops}  | {avg_time}  | {memory_alloc} |
| 单条查询 | {ops}  | {avg_time}  | {memory_alloc} |
| 批量查询 | {ops}  | {avg_time}  | {memory_alloc} |
| 复杂查询 | {ops}  | {avg_time}  | {memory_alloc} |

## 4. 问题与优化建议
### 4.1 发现的问题
1. {issue_description}
   - 严重程度：{severity}
   - 影响范围：{impact}
   - 解决状态：{status}

### 4.2 性能优化建议
1. {optimization_suggestion}
   - 当前性能：{current_performance}
   - 预期提升：{expected_improvement}
   - 实现成本：{implementation_cost}

### 4.3 代码质量改进
1. {code_quality_issue}
   - 问题描述：{description} 
   - 改进建议：{suggestion}
   - 优先级：{priority}

## 5. 结论与建议
### 5.1 测试结论
- 整体评估：{overall_assessment}
- 主要风险：{main_risks}
- 遗留问题：{remaining_issues}

### 5.2 后续建议
1. {follow_up_suggestion}
   - 优先级：{priority}
   - 计划时间：{planned_time}
   - 负责人：{owner}

## 6. 附件
- 详细测试用例清单：{test_cases_doc}
- 覆盖率报告：{coverage_report}
- 性能测试详细报告：{benchmark_report}
```

    *   **注意事项:**
        1. **测试隔离**
           - 每个测试用例相互独立
           - 测试前后清理环境
           - 避免测试间相互影响

        2. **资源管理**
           - 正确释放资源
           - 避免资源泄露
           - 控制测试执行时间

        3. **可维护性**
           - 测试代码结构清晰
           - 避免测试代码重复
           - 保持测试简单直观

        4. **持续集成**
           - 配置CI执行测试
           - 设置覆盖率门禁
           - 性能测试基线管理

**完成标志:**
*   数据库迁移脚本已生成并通过评审。
*   数据库表结构已在开发环境成功创建。
*   DAO 层代码已生成并通过评审。
*   DAO 层单元测试已生成、通过，且覆盖率达标。

### 步骤1.7: 生成Phase 1汇总文档

**🔧 AI指令模板** (基于实际使用经验优化):
```
请根据Phase 1 DAO层的开发完成情况，生成Phase汇总文档。

【汇总要求】
请按照以下结构生成完整的Phase 1汇总文档：

## Phase 1: DAO层构建与验证 - 完成汇总

### 1. Phase概述
- **目标**: DAO层构建与验证
- **模块**: {biz_module}
- **完成时间**: {completion_time}
- **开发状态**: ✅ 已完成

### 2. 生成的文件清单
详细列出所有新增和修改的文件，包括：
- 数据库迁移脚本
- GORM Model文件
- DAO实现文件
- 单元测试文件
- 依赖注入配置

### 3. 核心实现汇总
- 数据库表结构设计
- GORM Model定义
- Repository接口实现
- 关键业务逻辑
- 错误处理机制

### 4. 验证结果
- 编译验证: ✅ 通过
- 单元测试: ✅ 覆盖率>80%
- 代码质量: ✅ 无警告
- 性能测试: ✅ 达标
- 安全扫描: ✅ 无问题

### 5. 下一Phase准备
为Phase 2 Domain层开发提供的上下文信息和接口定义。

【保存位置】
`project-summaries/phase-1-dao-summary-{biz_module}.md`

【重要性】
这个汇总文档将作为后续Phase开发的直接输入，可减少60-80%的token消耗。
```

**输出文件:** `project-summaries/phase-1-dao-summary-{biz_module}.md`

**📊 汇总系统优势**:
- **Token节省**: 从15000-25000降至3000-5000 tokens
- **开发效率**: 上下文理解时间缩短80%
- **信息完整性**: 确保跨Phase开发的一致性

### 下一步操作
根据工作流，Phase 1 完成后，下一步将进入 **Phase 2: Domain层定型与验证 (Domain Layer Finalization & Validation)**。
AI 将协助您评审和定型 Domain 层的核心组件，并生成单元测试。

### Phase 2: Domain层定型与验证 (Domain Layer Finalization & Validation)

**阶段说明:** 在数据访问层初步完成后，本阶段专注于最终确认和验证核心业务逻辑层 (Domain Layer) 的组件。这确保了业务规则的准确性和健壮性，为后续服务层的实现打下坚实基础。

**目标:** 最终确认并验证 Domain 层的核心组件，主要包括模块的**领域接口 (如 Repository 接口)** 和 **领域实现 (实体、值对象及其业务方法)**。全局错误和常量已在其他地方定义。

**🔧 核心输入** (基于Phase汇总系统优化):
*   **主要输入**: `project-summaries/phase-1-dao-summary-{biz_module}.md` - Phase 1汇总文档
*   **规范文档**: `luban-agent/ai-rules/backend/project-rules/golang/ddd/domain.mdc` - Domain层开发规范
*   **补充输入**: Phase 0阶段确认的领域模型草稿 (如需要)

**📊 输入优化效果**:
- **Token消耗**: 从读取20-30个文件降至读取3-5个汇总文件
- **上下文获取**: 从15分钟缩短到3分钟
- **信息完整性**: 通过汇总确保所有必要信息都已包含

**输出:**
*   `domain/{biz_module}/interface.go`: 经过评审的领域（Repository）接口。
*   `domain/{biz_module}/impl/*.go`: 经过评审和测试的领域实体、值对象及其业务方法。
*   `domain/{biz_module}/impl/*_test.go`: 领域实体方法的单元测试。

**步骤:**

1.  **Domain 组件评审与定型 (Domain Component Review & Finalization):**
    *   **目标:** 确保 Domain 的 Repository 接口、实体、值对象的定义完整、准确且符合规范。
    *   **核心输入:** Phase 0 的设计输出 (例如 `domain/{biz_module}/interface.go` 和 `domain/{biz_module}/impl/*.go` 的草稿), `domain.mdc`。
    *   **步骤:**
        1.  **(开发者/AI)** 参照 `domain.mdc` 规范，**仔细评审** Phase 0 定义的实体和值对象 (位于 `domain/{biz_module}/impl/*.go`)：检查 ID、属性是否齐全，业务方法是否封装了核心业务规则和状态变更逻辑。确认值对象的不可变性、属性和比较方法。
        2.  **(开发者/AI)** 评审 Repository 接口 (位于 `domain/{biz_module}/interface.go`)：确认接口方法是否满足上层业务需求，参数和返回值是否使用领域对象。
        3.  **(开发者)** 根据评审结果，**修改并最终确定** Domain 层的代码 (即 `domain/{biz_module}/interface.go` 和 `domain/{biz_module}/impl/*.go` 中的文件)。
    *   **关键检查点:** ✅ 实体方法封装业务逻辑 ✅ VO 不可变 ✅ Repo 接口满足需求 ✅ 符合 `domain.mdc` 规范。
    *   **输出物:** 定型的 Domain 层 `.go` 文件 (位于 `domain/{biz_module}/interface.go` 和 `domain/{biz_module}/impl/` 目录下)。
    *   **验收标准:** Domain 层代码结构清晰，准确反映业务模型，符合规范要求。

2.  **Domain 单元测试 (Domain Unit Testing):**
    *   **目标:** 验证 Domain 实体方法和值对象方法 (位于 `domain/{biz_module}/impl/*.go`) 的业务逻辑正确性。
    *   **核心输入:** 定型的 Domain 实体/VO 代码 (`domain/{biz_module}/impl/*.go`), Go 测试规范。
    *   **步骤:**
        1.  **(人工/AI)** 为包含业务逻辑的实体/VO 方法创建测试文件 (例如 `domain/{biz_module}/impl/{entity_name}_test.go`)。
        2.  **(AI 生成)**
            *   **指令 (示例):** "请为 `domain/user/impl/user_entity.go` 中的 `Activate` 方法编写单元测试。覆盖场景：1. 从 Inactive 激活成功。 2. 尝试激活已 Active 的用户 (应返回全局定义的 `ErrUserAlreadyActive` 或类似错误)。 3. 尝试激活 Banned 的用户 (应返回全局定义的 `ErrUserBanned` 或类似错误)。断言状态和 UpdatedAt 是否按预期变更或返回正确错误。"
            *   AI 根据指令生成测试用例骨架。
        3.  **(开发者评审、完善与执行)**
            *   评审测试场景是否覆盖了业务规则的所有分支和边界条件。
            *   编写或完善断言 (`assert` 或 `require`)，确保精确验证了状态变更和返回值 (包括对全局错误的检查)。
            *   **[执行]** `go test ./domain/{biz_module}/impl/... -cover` 运行单元测试。
            *   **确认** 所有测试用例通过，代码覆盖率满足要求 (通常 Domain 核心逻辑要求较高覆盖率)。
    *   **关键检查点:** ✅ 测试场景覆盖业务规则 ✅ 断言准确 ✅ 测试用例全部通过 ✅ 代码覆盖率达标。
    *   **输出物:** Domain 层单元测试文件 (`domain/{biz_module}/impl/*_test.go`), 测试覆盖率报告。
    *   **验收标准:** 单元测试有效验证了 Domain 层的核心业务逻辑，测试通过率 100%，覆盖率符合项目要求。

**完成标志:**
*   Domain 层接口 (`domain/{biz_module}/interface.go`)、实体和值对象 (`domain/{biz_module}/impl/*.go`) 已定型并通过评审。
*   Domain 层核心业务逻辑的单元测试已完成、通过，且覆盖率达标。

### 步骤2.3: 生成Phase 2汇总文档

**AI指令模板:**
```
请根据Phase 2 Domain层的开发完成情况，使用汇总模板 `ai-workflows/dev-be/phase-summaries/phase-2-domain-summary-template.md` 生成本次开发的汇总文档。

请参考前一阶段的汇总文档：`project-summaries/phase-1-dao-summary-{biz_module}.md`

请将模板中的占位符替换为实际值：
- {project_name}: [实际项目名称]
- {biz_module}: [实际业务模块名称]
- {completion_time}: [完成时间]
- {developer_name}: [开发者姓名]
- 其他具体的实体名称、值对象名称、Repository接口等

生成的汇总文档保存为：`project-summaries/phase-2-domain-summary-{biz_module}.md`

这个汇总文档将作为Phase 3 DTO层开发的直接输入。
```

**输出文件:** `project-summaries/phase-2-domain-summary-{biz_module}.md`

### 下一步操作
根据工作流，Phase 2 完成后，下一步将进入 **Phase 3: DTO层定义与转换器实现 (DTO Definition & Converter Implementation)**。
AI 将协助您定义数据传输对象和转换器。

### Phase 3: DTO层定义与转换器实现 (DTO Definition & Converter Implementation)

**阶段说明:** 在 Domain 层定型与验证 (Phase 2) 和 DAO 层 (Phase 1) 准备就绪后，本阶段专注于定义 Service 层与外部交互所需的数据传输对象 (DTO)，并实现与 Domain 对象相互转换的辅助函数。

**目标:** 定义 Service 层与外部 (Controller) 交互的纯数据结构 (DTOs) 作为Go结构体。同时，在 DTO 包内，通过独立的转换器文件提供与 Domain 对象相互转换的辅助函数。

**核心输入:**
*   业务需求/API交互定义
*   Domain 对象定义 (来自 `domain/{biz_module}/impl/*.go`)
*   `luban-agent/ai-rules/backend/project-rules/golang/ddd/dto.mdc` (DTO规范)
*   全局规范 (JSON/时间格式)

**输出:**
*   `app/{biz_module}/dto/{resource}_dto.go`: DTO定义文件
*   `app/{biz_module}/dto/{resource}_converter.go`: 转换器实现文件

**步骤:**

1.  **DTO 定义与转换器实现 (DTO Definition & Converter Implementation):**
    *   **目标:** 定义 Service 层与外部 (Controller) 交互的纯数据结构 (DTOs) 作为Go结构体。同时，在 DTO 包 (`app/{biz_module}/dto/`) 内，通过独立的转换器文件 (例如 `converter.go`) 提供与 Domain 对象相互转换的辅助函数。
    *   **核心输入:** 业务需求/API交互定义, Domain 对象定义 (来自 `domain/{biz_module}/impl/*.go`), `project-rules/backend-golang/ddd/dto.mdc` (DTO规范), 全局规范 (JSON/时间格式)。
    *   **AI 增强指令:**
        *   **指令 (示例):** "根据创建资源的业务需求（例如需要字段 A, B, C 用于请求，返回字段 X, Y, Z），并参考 `domain/{biz_module}/impl/{relevant_entity_file}.go` 中的领域对象 (例如 `Resource` 结构体)，在 `app/{biz_module}/dto/` 目录下创建或更新 `{resource}_dto.go` (包含纯数据DTO结构体如 `CreateResourceReqDTO` 和 `CreateResourceRespDTO`) 和 `{resource}_converter.go` (包含转换辅助函数，例如 `ToDomain(*CreateResourceReqDTO) *domain.Resource` 和 `FromDomain(*domain.Resource) *CreateResourceRespDTO`)。请遵循 `dto.mdc` 规范，确保 JSON 标签为小写驼峰，时间字段为 UTC 秒数整数，并添加必要的验证标签 (如 `binding:"required"`)。"
        *   **AI 动作:**
            *   根据指令中的业务需求和API交互描述，确定DTO所需的字段和类型。
            *   解析 Domain 对象，识别其字段和类型，辅助进行映射和转换设计。
            *   根据 `dto.mdc` 规范，在 `dto/{resource}_dto.go` (如果不存在则创建) 中生成对应的 DTO struct 定义，包含正确的字段、类型、JSON tag (`json:"fieldName"`) 和验证 tag (`binding:"required"` 等)。
            *   在 `dto/{resource}_converter.go` (如果不存在则创建) 中生成 `ToDomain` 和 `FromDomain` 函数的骨架或完整实现。
    *   **步骤:**
        1.  **(AI 生成)** AI 根据上述指令生成 DTO 定义和转换器函数的初步代码。
        2.  **(开发者评审与完善)**
            *   **字段映射**: 检查 DTO 与 Domain 之间所有字段是否都已正确映射，类型是否匹配。
            *   **命名规范**: 确认 DTO 结构体和字段命名是否符合规范 (如 `UpperCamelCase`)。
            *   **JSON & Binding Tags**: 验证 `json` 标签是否为小写驼峰，`binding` 标签是否根据业务需求正确添加 (如 `required`, `email`, `min`, `max` 等)。
            *   **时间/枚举转换**: 仔细核对时间戳 (是否转为 UTC 秒) 和枚举值的转换逻辑是否正确。
            *   **转换函数**: 审查 `ToDomain` 和 `FromDomain` 函数的逻辑，确保所有字段都被正确处理，特别是 nil 检查和复杂类型转换。
            *   **代码位置**: 确认生成的文件和代码位于正确的 `app/{biz}/dto/` 目录下。
    *   **关键检查点:** ✅ 字段完全映射 ✅ 命名规范 ✅ Tags 正确 ✅ 时间/枚举转换无误 ✅ 转换逻辑完整 ✅ 代码位置正确。
    *   **输出物:** `app/{biz}/dto/{resource}_dto.go`, `app/{biz}/dto/{resource}_converter.go`。
    *   **验收标准:** 生成的 DTO 定义符合接口要求和规范，转换函数能够正确地在 DTO 和 Domain 对象之间进行双向转换，代码健壮且符合规范。

**完成标志:**
*   所有相关的 DTO 已定义并通过评审。
*   转换函数能够正确地在 DTO 和 Domain 对象之间进行双向转换。

### 步骤3.2: 生成Phase 3汇总文档

**AI指令模板:**
```
请根据Phase 3 DTO层的开发完成情况，使用汇总模板 `ai-workflows/dev-be/phase-summaries/phase-3-dto-summary-template.md` 生成本次开发的汇总文档。

请参考前一阶段的汇总文档：`project-summaries/phase-2-domain-summary-{biz_module}.md`

请将模板中的占位符替换为实际值：
- {project_name}: [实际项目名称]
- {biz_module}: [实际业务模块名称]
- {completion_time}: [完成时间]
- {developer_name}: [开发者姓名]
- 其他具体的DTO名称、转换器名称、验证规则等

生成的汇总文档保存为：`project-summaries/phase-3-dto-summary-{biz_module}.md`

这个汇总文档将作为Phase 4 Service层开发的直接输入。
```

**输出文件:** `project-summaries/phase-3-dto-summary-{biz_module}.md`

### 下一步操作
根据工作流，Phase 3 完成后，下一步将进入 **Phase 4: Service层实现与验证 (Service Layer Implementation & Validation)**。
AI 将协助您实现业务用例，并生成单元测试。

### Phase 4: Service层实现与验证 (Service Layer Implementation & Validation)

**阶段说明:** 在 DTO 层定义与转换器实现 (Phase 3) 完成后，本阶段专注于实现业务用例 (Use Cases)，这些用例将协调 Domain 对象和 Repository 接口来完成具体的业务逻辑。Service 层是业务逻辑的核心实现层。

**目标:** 实现 Service 层的业务用例，包括业务逻辑、事务管理、以及与 Repository 的交互。同时编写相应的单元测试来验证业务逻辑的正确性。

**核心输入:**
*   业务需求文档
*   Domain 对象定义 (来自 `domain/{biz_module}/impl/*.go`)
*   Repository 接口定义 (来自 `domain/{biz_module}/repository/*.go`)
*   DTO 定义 (来自 `app/{biz_module}/dto/*.go`)
*   `luban-agent/ai-rules/backend/project-rules/golang/ddd/service.mdc` (Service规范)

**输出:**
*   `app/{biz_module}/service/{resource}_service.go`: Service实现文件
*   `app/{biz_module}/service/{resource}_service_test.go`: Service单元测试文件

**步骤:**

1.  **Service 实现 (Service Implementation):**
    *   **目标:** 实现 Service 层的业务用例，包括业务逻辑、事务管理、以及与 Repository 的交互。
    *   **核心输入:** 业务需求文档, Domain 对象定义 (来自 `domain/{biz_module}/impl/*.go`), Repository 接口定义 (来自 `domain/{biz_module}/repository/*.go`), DTO 定义 (来自 `app/{biz_module}/dto/*.go`), `project-rules/backend-golang/ddd/service.mdc` (Service规范)。
    *   **AI 增强指令:**
        *   **指令 (示例):** "根据创建资源的业务需求（例如需要验证字段 A，调用 Repository 保存，返回创建结果），并参考 `domain/{biz_module}/impl/{relevant_entity_file}.go` 中的领域对象、`domain/{biz_module}/repository/{resource}_repository.go` 中的 Repository 接口，以及 `app/{biz_module}/dto/{resource}_dto.go` 中的 DTO 定义，在 `app/{biz_module}/service/` 目录下创建或更新 `{resource}_service.go`。请实现 `Create{Resource}` 方法，包含必要的业务验证、DTO 到 Domain 转换、Repository 调用、Domain 到 DTO 转换、错误处理和事务管理。请遵循 `service.mdc` 规范，确保依赖注入、错误处理和日志记录符合项目标准。"
        *   **AI 动作:**
            *   根据指令中的业务需求，确定 Service 方法的签名和实现逻辑。
            *   解析 Domain 对象、Repository 接口和 DTO 定义，确保正确的类型使用和方法调用。
            *   根据 `service.mdc` 规范，在 `service/{resource}_service.go` (如果不存在则创建) 中生成 Service struct 定义和相关方法实现。
            *   实现业务逻辑，包括参数验证、DTO 转换、Domain 对象操作、Repository 调用、错误处理等。
            *   添加适当的日志记录和错误处理机制。
    *   **步骤:**
        1.  **(AI 生成)** AI 根据上述指令生成 Service 实现的初步代码。
        2.  **(开发者评审与完善)**
            *   **业务逻辑**: 检查业务逻辑是否正确实现，是否符合需求规范。
            *   **依赖注入**: 确认 Service struct 是否正确定义了依赖的 Repository 接口。
            *   **DTO 转换**: 验证 DTO 与 Domain 对象之间的转换是否正确使用了转换器函数。
            *   **错误处理**: 验证错误处理是否完整，错误信息是否清晰。
            *   **事务管理**: 检查是否在需要的地方正确使用了事务。
            *   **日志记录**: 确认关键操作是否有适当的日志记录。
            *   **代码规范**: 检查代码是否符合项目的编码规范和最佳实践。
    *   **关键检查点:** ✅ 业务逻辑正确 ✅ 依赖注入完整 ✅ DTO转换正确 ✅ 错误处理完善 ✅ 事务管理正确 ✅ 日志记录适当 ✅ 代码规范符合。
    *   **输出物:** `app/{biz}/service/{resource}_service.go`。
    *   **验收标准:** Service 实现符合业务需求，代码健壮且符合规范，能够正确处理各种业务场景和异常情况，正确使用 DTO 转换器。

2.  **Service 单元测试 (Service Unit Testing):**
    *   **目标:** 为 Service 层的业务逻辑编写全面的单元测试，确保代码质量和业务逻辑的正确性。
    *   **核心输入:** Service 实现代码, 测试规范和最佳实践。
    *   **AI 增强指令:**
        *   **指令 (示例):** "为 `app/{biz_module}/service/{resource}_service.go` 中的 `Create{Resource}` 方法编写全面的单元测试。请在 `app/{biz_module}/service/{resource}_service_test.go` 中创建测试用例，包括正常情况、边界条件、错误情况等。使用 mock 来模拟 Repository 依赖，确保测试的独立性。请遵循项目的测试规范，包括测试命名、断言方式、覆盖率要求等。"
        *   **AI 动作:**
            *   分析 Service 方法的实现，识别需要测试的场景和边界条件。
            *   生成相应的测试用例，包括正常流程、异常流程、边界条件等。
            *   使用 mock 技术模拟外部依赖（如 Repository），确保测试的独立性。
            *   编写清晰的测试代码，包括适当的断言和错误验证。
    *   **步骤:**
        1.  **(AI 生成)** AI 根据上述指令生成单元测试的初步代码。
        2.  **(开发者评审与完善)**
            *   **测试覆盖率**: 检查测试是否覆盖了所有重要的代码路径和业务场景。
            *   **测试独立性**: 确认测试之间没有依赖关系，可以独立运行。
            *   **Mock 使用**: 验证 mock 的使用是否正确，是否正确模拟了外部依赖。
            *   **断言准确性**: 检查断言是否准确验证了预期的结果。
            *   **测试命名**: 确认测试方法和用例的命名是否清晰易懂。
    *   **关键检查点:** ✅ 测试覆盖率充分 ✅ 测试独立性 ✅ Mock 使用正确 ✅ 断言准确 ✅ 测试命名清晰。
    *   **输出物:** `app/{biz}/service/{resource}_service_test.go`。
    *   **验收标准:** 单元测试覆盖率达到要求（>80%），测试用例全面且能够有效验证业务逻辑的正确性。

**完成标志:**
*   所有 Service 方法已实现并通过评审。
*   单元测试覆盖率达到要求且所有测试通过。

### 步骤4.3: 生成Phase 4汇总文档

**AI指令模板:**
```
请根据Phase 4 Service层的开发完成情况，使用汇总模板 `ai-workflows/dev-be/phase-summaries/phase-4-service-summary-template.md` 生成本次开发的汇总文档。

请参考前一阶段的汇总文档：`project-summaries/phase-3-dto-summary-{biz_module}.md`

请将模板中的占位符替换为实际值：
- {project_name}: [实际项目名称]
- {biz_module}: [实际业务模块名称]
- {completion_time}: [完成时间]
- {developer_name}: [开发者姓名]
- 其他具体的Service名称、业务方法名称、事务处理等

生成的汇总文档保存为：`project-summaries/phase-4-service-summary-{biz_module}.md`

这个汇总文档将作为Phase 5 Controller层开发的直接输入。
```

**输出文件:** `project-summaries/phase-4-service-summary-{biz_module}.md`

### 下一步操作
根据工作流，Phase 4 完成后，下一步将进入 **Phase 5: Controller层实现与API测试 (Controller Layer Implementation & API Testing)**。
AI 将协助您实现 HTTP 处理器和 API 测试。

### Phase 5: Controller层实现与API测试 (Controller Layer Implementation & API Testing)

**阶段说明:** 在 Service 层实现与验证 (Phase 4) 完成后，本阶段负责构建对外暴露的接口层，包括处理 HTTP/gRPC 请求的 Controller/Handler，以及配置路由规则和中间件。此阶段是连接业务逻辑与外部世界的桥梁。

**目标:** 实现 Controller/Handler 层，处理请求参数绑定、调用 Service、进行错误转换和响应封装，并配置路由规则和必要的中间件。

**核心输入:**
*   `app/{biz_module}/service/*_service.go`: 已实现的 Service 接口定义。
*   `app/{biz_module}/dto/*.go`: 已定义的 DTO（请求和响应）。
*   `luban-agent/ai-rules/backend/project-rules/golang/ddd/controller.mdc`: Controller 层开发规范与代码示例。
*   `luban-agent/ai-rules/backend/project-rules/golang/ddd/router.mdc`: Router 层开发规范与代码示例。
*   `luban-agent/ai-rules/backend/project-rules/golang/rules-golang-code.mdc`: 通用 Go 规范（API 设计、错误处理等）。
*   `luban-agent/ai-rules/backend/guidelines/rules-api-code.md`项目统一的 API 响应格式规范 (如果单独定义)。
*   `luban-agent/ai-rules/backend/project-rules/golang/rules-kratos-code.mdc`框架信息 (Kratos HTTP/gRPC, Gin 等)。

**输出:**
*   `app/{biz_module}/controller/{resource}_controller.go`: Controller 接口实现 (或 Handler 函数)。
*   `app/{biz_module}/controller/{resource}_controller_test.go`: Controller 实现的单元测试。
*   `app/{biz_module}/router/router.go`: 路由注册代码。
*   (可能更新 `cmd/{service_name}/internal/server/http.go` 或 `grpc.go` 以调用路由注册函数)。
*   (可能更新 `cmd/{service_name}/wire.go` 的 ProviderSet)。

**步骤:**

1.  **Controller/Handler 实现 (Controller/Handler Implementation):**
    *   **目标:** 实现处理具体 HTTP/gRPC 请求的方法，负责参数绑定、调用 Service、转换错误和封装响应。
    *   **核心输入:** Service 接口定义, DTOs, `controller.mdc`, 框架文档, API 响应规范。
    *   **步骤:**
        1.  **(人工)** 设计 Controller 结构体（如 `UserController`）或独立的 Handler 函数。明确依赖项，主要是对应的 Service 接口 (`UserService`) 和日志助手 (`*log.Helper`)。在 `controller/controller.go` (或类似文件) 中定义 Controller 结构体和构造函数。
        2.  **(AI 生成)**
            *   **指令 (示例 - Kratos HTTP/Gin):** "请在 `app/user/controller/user_controller.go` 中为 `UserController` 实现 `CreateUser` 方法。该方法应接收框架的 Context (如 `gin.Context` 或 `kratosHttp.Context`)。1. 从 Context 中绑定请求体到 `dto.CreateUserReqDTO`。2. 调用注入的 `UserService` 的 `CreateUser` 方法，传入 `ctx` 和绑定的 DTO。3. 处理 Service 返回的错误：根据错误类型（使用 `errors.Is` 或 `errorx.IsBizError`）转换为合适的 HTTP 状态码和统一响应体错误信息 (参照 `controller.mdc` 和 API 响应规范)。4. 如果 Service 调用成功，将返回的 `dto.UserDTO` 封装在统一的成功响应体中，并返回 200 OK 或 201 Created。5. 在方法入口和出口记录 Info 日志，包含 TraceID 和关键信息 (脱敏处理)。请遵循 `controller.mdc` 规范。"
            *   AI 根据指令生成 Controller 方法的初步实现。
        3.  **(开发者评审与完善)**
            *   **参数绑定**: 确认框架提供的绑定功能被正确使用，并处理了可能的绑定错误 (返回 400 Bad Request)。
            *   **Service 调用**: 确认正确调用了注入的 Service 接口方法，并传递了正确的 `ctx` 和 DTO。
            *   **错误转换**: 仔细检查 Service 返回的各种错误（业务错误、内部错误、参数错误等）是否都按照 `controller.mdc` 和 API 响应规范被正确地转换为 HTTP 状态码和标准错误响应体。**确保不将内部错误细节泄露给客户端**。
            *   **响应封装**: 确认成功响应的数据被正确地封装在标准的成功响应体结构中。
            *   **上下文处理**: 确认从请求上下文中提取了必要信息（如 UserID，如果需要传递给 Service）并正确传递。
            *   **日志记录**: 确认入口/出口日志、错误日志记录符合规范，包含 TraceID，且进行了必要的脱敏。
            *   **代码规范**: 检查命名、注释、函数长度、复杂度等是否符合 `controller.mdc` 和全局规范。
    *   **关键检查点:** ✅ 参数绑定正确 ✅ Service 调用无误 ✅ 错误转换符合规范 ✅ 响应封装符合规范 ✅ 上下文处理正确 ✅ 日志规范 ✅ 代码符合规范。
    *   **输出物:** `app/{biz}/controller/{resource}_controller.go` 文件中对应方法的完整实现。
    *   **验收标准:** Controller 方法正确处理 HTTP/gRPC 请求，有效调用 Service，健壮地处理错误，并返回符合规范的响应。

2.  **Controller/Handler 单元测试 (Controller/Handler Unit Testing):**
    *   **目标:** 使用 Go 标准 `testing` 包及框架测试辅助库，验证 Controller 方法逻辑的正确性，特别是参数绑定、Service 调用（通过测试替身隔离）、错误转换和响应封装。
    *   **核心输入:** Controller 实现代码, Service 接口定义 (用于创建测试替身), DTOs, `controller.mdc` (测试策略), Go 测试规范, 框架的测试辅助库 (如 `net/http/httptest`, Gin 的测试模式, Kratos 的测试工具)。
    *   **步骤:**
        1.  **(人工/AI)** 创建测试文件 `{resource}_controller_test.go`。
        2.  **(人工/AI)** 根据需要为 Service 接口准备测试实现（例如，手写 fakes/stubs，或可选地使用 mock 生成工具）。
        3.  **(AI 生成)**
            *   **指令 (示例 - Gin):** "请为 `app/user/controller/user_controller.go` 中的 `CreateUser` 方法编写单元测试，使用Go标准 `testing` 包和Gin测试工具。你需要准备 `UserService` 的测试实现 (fake/stub)。覆盖场景：1. 成功创建 (UserService 测试实现返回 DTO)。 2. 请求体绑定失败。 3. Service 测试实现返回业务错误 (如 `service.ErrUsernameConflict`)。 4. Service 测试实现返回内部错误。对于每个场景：配置 `UserService` 测试实现的预期行为和返回值。使用 `httptest` 创建模拟请求和响应记录器 (`w := httptest.NewRecorder()`)。构造 Gin Context (`c, _ := gin.CreateTestContext(w)`) 并设置模拟请求体。调用 Controller 方法。断言响应的状态码 (`w.Code`) 和响应体 (`w.Body.String()`) 是否符合预期（包括标准错误/成功响应结构）。"
            *   AI 根据指令生成测试用例骨架和 Service 测试替身的配置建议。
        4.  **(开发者评审、完善与执行)**
            *   **请求模拟**: 确认模拟请求（方法、路径、头部、请求体）构造正确，覆盖不同的输入情况（有效、无效 JSON、缺少字段等）。
            *   **Service 测试实现配置**: 确认 Service 的测试实现按预期配置和交互，并覆盖了 Service 可能返回的不同结果（成功 DTO、各种错误）。
            *   **框架上下文**: 确认框架的测试上下文（如 `gin.Context`）被正确创建和使用。
            *   **断言**: 重点断言 HTTP 响应状态码和响应体内容是否完全符合 API 规范（包括成功和失败的 JSON 结构）。可以使用 JSON unmarshal 来验证响应体结构。
            *   **[执行]** `go test ./app/{biz}/controller/... -cover` 运行单元测试。
            *   **确认** 所有测试用例通过。Controller 层通常不需要强制覆盖率，但关键逻辑应被覆盖。
    *   **关键检查点:** ✅ 请求模拟正确 ✅ Service 测试实现配置正确并覆盖相关场景 ✅ 框架上下文使用正确 ✅ 响应状态码和内容断言准确 ✅ 测试用例通过。
    *   **输出物:** `app/{biz}/controller/{resource}_controller_test.go`。
    *   **验收标准:** 单元测试有效验证了 Controller 的请求处理、Service 调用（使用合适的测试实现进行模拟）、错误转换和响应封装逻辑。

**完成标志:**
*   所有相关的 Controller/Handler 方法已实现并通过评审。
*   Controller/Handler 的单元测试已完成并通过。

### 步骤5.3: 生成Phase 5汇总文档

**AI指令模板:**
```
请根据Phase 5 Controller层的开发完成情况，使用汇总模板 `ai-workflows/dev-be/phase-summaries/phase-5-controller-summary-template.md` 生成本次开发的汇总文档。

请参考前一阶段的汇总文档：`project-summaries/phase-4-service-summary-{biz_module}.md`

请将模板中的占位符替换为实际值：
- {project_name}: [实际项目名称]
- {biz_module}: [实际业务模块名称]
- {completion_time}: [完成时间]
- {developer_name}: [开发者姓名]
- 其他具体的Controller名称、API接口、中间件等

生成的汇总文档保存为：`project-summaries/phase-5-controller-summary-{biz_module}.md`

这个汇总文档将作为Phase 6 Router层开发的直接输入。
```

**输出文件:** `project-summaries/phase-5-controller-summary-{biz_module}.md`

### 下一步操作
根据工作流，Phase 5 完成后，下一步将进入 **Phase 6: Router层实现与路由配置 (Router Layer Implementation & Route Configuration)**。
AI 将协助您实现路由配置和中间件设置。

### Phase 6: Router层实现与路由配置 (Router Layer Implementation & Route Configuration)

**阶段说明:** 在 Controller 层实现与API测试 (Phase 5) 完成后，本阶段专注于配置路由规则，将 URL 路径映射到具体的 Controller 方法，并应用必要的中间件。这是系统对外服务的最后一层配置。

**目标:** 实现路由配置，将 HTTP 请求正确路由到对应的 Controller 方法，并配置必要的中间件（如认证、限流、CORS等）。

**核心输入:**
*   `app/{biz_module}/controller/*_controller.go`: 已实现的 Controller 方法。
*   `luban-agent/ai-rules/backend/project-rules/golang/ddd/router.mdc`: Router 层开发规范与代码示例。
*   `luban-agent/ai-rules/backend/project-rules/golang/rules-golang-code.mdc`: 通用 Go 规范。
*   中间件需求和配置规范。

**输出:**
*   `app/{biz_module}/router/router.go`: 路由注册代码。
*   `cmd/{service_name}/internal/server/http.go`: HTTP 服务器配置更新。
*   `cmd/{service_name}/wire.go`: 依赖注入配置更新。

**步骤:**

1.  **路由注册实现 (Route Registration Implementation):**
    *   **目标:** 将 URL 路径/HTTP 方法映射到具体的 Controller 方法，并应用必要的中间件。
    *   **核心输入:** Controller 实现, `router.mdc`, 框架文档, 中间件列表。
    *   **AI 增强指令:**
        *   **指令 (示例):** "根据 `app/{biz_module}/controller/{resource}_controller.go` 中已实现的 Controller 方法，在 `app/{biz_module}/router/router.go` 中创建路由注册函数。请为每个 Controller 方法配置对应的路由，例如 `POST /api/v1/{resources}` 对应 `Create{Resource}` 方法。请遵循 RESTful 设计原则，并应用必要的中间件（如 CORS、认证、限流等）。请遵循 `router.mdc` 规范，确保路由分组和中间件应用正确。"
        *   **AI 动作:**
            *   分析 Controller 方法，确定对应的 HTTP 方法和路径。
            *   根据 RESTful 原则设计路由规则。
            *   生成路由注册代码，包括路由分组和中间件配置。
            *   确保路由配置符合项目规范和最佳实践。
    *   **步骤:**
        1.  **(AI 生成)** AI 根据上述指令生成路由注册的初步代码。
        2.  **(开发者评审与完善)**
            *   **路由映射**: 确认 URL 路径与 Controller 方法的映射是否正确。
            *   **HTTP 方法**: 验证 HTTP 方法（GET, POST, PUT, DELETE 等）是否符合 RESTful 设计原则。
            *   **路由分组**: 确认路由是否正确分组，便于管理和维护。
            *   **中间件配置**: 检查是否应用了必要的中间件，顺序是否正确。
            *   **路径参数**: 验证路径参数的定义和使用是否正确。
            *   **版本控制**: 确认 API 版本控制策略是否正确实施。
    *   **关键检查点:** ✅ 路由映射正确 ✅ HTTP 方法符合规范 ✅ 路由分组合理 ✅ 中间件配置正确 ✅ 路径参数正确 ✅ 版本控制正确。
    *   **输出物:** `app/{biz}/router/router.go`。
    *   **验收标准:** 路由配置正确，能够将请求正确路由到对应的 Controller 方法，中间件应用符合需求。

2.  **服务器配置更新 (Server Configuration Update):**
    *   **目标:** 更新 HTTP 服务器配置，集成新的路由规则。
    *   **核心输入:** 路由注册代码, 服务器配置文件。
    *   **AI 增强指令:**
        *   **指令 (示例):** "请更新 `cmd/{service_name}/internal/server/http.go` 文件，将 `app/{biz_module}/router/router.go` 中的路由注册函数集成到 HTTP 服务器中。确保路由注册函数在服务器启动时被正确调用，并且所有路由都能正常工作。"
        *   **AI 动作:**
            *   分析现有的服务器配置代码。
            *   生成集成新路由的代码更新。
            *   确保服务器配置的完整性和正确性。
    *   **步骤:**
        1.  **(AI 生成)** AI 根据上述指令生成服务器配置更新的代码。
        2.  **(开发者评审与完善)**
            *   **路由集成**: 确认新路由是否正确集成到服务器配置中。
            *   **启动顺序**: 验证路由注册在服务器启动流程中的位置是否正确。
            *   **配置完整性**: 检查服务器配置是否完整，没有遗漏的设置。
    *   **关键检查点:** ✅ 路由集成正确 ✅ 启动顺序正确 ✅ 配置完整性。
    *   **输出物:** 更新后的 `cmd/{service_name}/internal/server/http.go`。
    *   **验收标准:** 服务器能够正确启动并处理所有配置的路由。

3.  **依赖注入配置更新 (Dependency Injection Configuration Update):**
    *   **目标:** 更新依赖注入配置，确保所有新增的组件都能正确注入。
    *   **核心输入:** Router 实现代码, Wire 配置文件。
    *   **AI 增强指令:**
        *   **指令 (示例):** "请更新 `cmd/{service_name}/wire.go` 文件，将新增的 Router 相关组件添加到依赖注入配置中。确保所有依赖关系都正确配置，并且 Wire 能够成功生成依赖注入代码。"
        *   **AI 动作:**
            *   分析新增的 Router 组件及其依赖关系。
            *   生成 Wire 配置更新代码。
            *   确保依赖注入配置的完整性。
    *   **步骤:**
        1.  **(AI 生成)** AI 根据上述指令生成 Wire 配置更新的代码。
        2.  **(开发者评审与完善)**
            *   **依赖关系**: 确认所有依赖关系都正确配置。
            *   **Provider 配置**: 验证 Provider 配置是否完整和正确。
            *   **Wire 生成**: 运行 `make wire` 确认能够成功生成依赖注入代码。
    *   **关键检查点:** ✅ 依赖关系正确 ✅ Provider 配置完整 ✅ Wire 生成成功。
    *   **输出物:** 更新后的 `cmd/{service_name}/wire.go`。
    *   **验收标准:** 依赖注入配置正确，Wire 能够成功生成代码，应用能够正常启动。

**完成标志:**
*   路由配置已完成并验证正确。
*   服务器配置已更新并能正常启动。
*   依赖注入配置已更新并通过 Wire 生成。

### 步骤6.4: 生成Phase 6汇总文档

**AI指令模板:**
```
请根据Phase 6 Router层的开发完成情况，使用汇总模板 `ai-workflows/dev-be/phase-summaries/phase-6-router-summary-template.md` 生成本次开发的汇总文档。

请参考前一阶段的汇总文档：`project-summaries/phase-5-controller-summary-{biz_module}.md`

请将模板中的占位符替换为实际值：
- {project_name}: [实际项目名称]
- {biz_module}: [实际业务模块名称]
- {completion_time}: [完成时间]
- {developer_name}: [开发者姓名]
- 其他具体的路由配置、中间件、安全特性等

生成的汇总文档保存为：`project-summaries/phase-6-router-summary-{biz_module}.md`

这个汇总文档将作为Phase 7 集成测试和部署的直接输入。
```

**输出文件:** `project-summaries/phase-6-router-summary-{biz_module}.md`

### 下一步操作
根据工作流，Phase 6 完成后，整个 DDD 分层开发流程基本完成。接下来可以进行整体的集成测试和项目最终化工作。

---

## 工作流总结

通过以上 6 个 Phase 的系统化开发流程，我们完成了基于 DDD 架构的 Go 项目开发：

**Phase 1: DAO层实现与数据库集成** - 数据访问层
**Phase 2: Domain层定型与验证** - 领域层
**Phase 3: DTO层定义与转换器实现** - 数据传输层
**Phase 4: Service层实现与验证** - 业务逻辑层
**Phase 5: Controller层实现与API测试** - 控制层
**Phase 6: Router层实现与路由配置** - 路由层

这个顺序严格遵循了 DDD 分层架构的依赖关系，确保每一层都建立在稳固的基础之上，实现了高内聚、低耦合的系统设计。

### Phase 7: 整合、集成测试与最终化 (Integration, Integration Testing & Finalization)

**阶段说明:** 在所有 DDD 分层开发完成后（Phase 1-6），本阶段专注于系统整合、集成测试和项目最终化工作，确保整个系统能够正确协同工作。

**目标:** 确保所有组件能正确协同工作，通过实际API调用进行集成测试以验证关键业务流程，执行最终的代码质量检查，并准备代码评审。

**核心输入:**
*   已完成并通过单元测试的各层代码 (DAO, Domain, DTO, Service, Controller, Router)。
*   更新后的路由配置 (`app/{biz}/router/router.go`)。
*   项目依赖注入配置文件 (`cmd/{service_name}/wire.go`)。
*   集成测试环境 (可访问的测试数据库，以及按需配置的第三方服务接入点或其稳定替身)。
*   Go测试规范 (`luban-agent/ai-rules/backend/project-rules/golang/rules-golang-code.mdc`)。
*   项目开发 Checklist (`luban-agent/ai-rules/backend/project-rules/golang/project-progress.md`)。

**输出:**
*   集成测试代码 (`tests/integration/{feature}_test.go` 或类似路径，包含使用Go `testing`编排的测试流程)。
*   集成测试报告 (标准Go测试输出，可包含对API调用结果的总结)。
*   最终构建产物 (二进制文件)。
*   Code Review 请求。

**步骤:**

1.  **系统整合验证 (System Integration Verification):**
    *   **目标:** 验证所有 DDD 分层组件的依赖注入配置正确，系统能够正常启动。
    *   **核心输入:** 各层实现代码 (构造函数), `cmd/{service_name}/wire.go`。
    *   **步骤:**
        1.  **(开发者)** 仔细检查 `wire.go` 文件。对于每个新添加或修改的层 (DAO, Service, Controller, Router)，确保其构造函数 (`New...`) 已经添加到了正确的 `wire.ProviderSet` 中。
        2.  **(开发者)** 确认顶层的 Injector 函数 (通常是 `NewApp` 或类似) 包含了所有必要依赖，并且签名正确。
        3.  **(AI 辅助 - 可选):**
            *   **指令 (示例):** "请检查 `cmd/{service_name}/wire.go` 文件。我已经完成了所有 DDD 分层的开发，请确保 DAO、Domain、DTO、Service、Controller、Router 层的所有组件都被包含在合适的 ProviderSet 中，并且它们的依赖关系都已正确配置。如果需要，请帮我更新 ProviderSet。"
            *   AI 可以帮助检查依赖关系和生成 ProviderSet 更新建议。
        4.  **(开发者执行):**
            *   在项目根目录下运行 **`make wire`** 命令。
            *   **检查输出**: 仔细查看 `make wire` 的输出信息。如果出现错误（如依赖缺失、循环依赖），根据错误提示修改 `wire.go` 文件并重新运行，直到成功生成 `wire_gen.go` 文件。
            *   运行 **`make build`** 验证系统能够成功编译。
            *   运行应用程序，验证系统能够正常启动，所有路由都能正确响应。
    *   **关键检查点:** ✅ 所有组件 Provider 已添加 ✅ Injector 函数正确 ✅ `make wire` 成功执行无错误 ✅ 系统编译成功 ✅ 系统启动正常。
    *   **输出物:** 更新后的 `wire.go` 和生成的 `wire_gen.go`，成功构建的应用程序。
    *   **验收标准:** 依赖注入配置完整且正确，系统能够正常启动并响应请求。

2.  **集成测试设计与实现 (Integration Test Design & Implementation):**
    *   **目标:** 通过实际API调用验证跨越多个组件（Controller -> Service -> DAO -> DB）的关键业务流程是否按预期工作，特别关注组件间的交互和数据一致性。测试应使用Go的 `testing` 包进行编排和断言。
    *   **核心输入:** 业务需求 (PRD), API 定义 (用于确定请求的端点、方法、参数和预期响应格式), Service/DAO 代码 (用于理解业务逻辑和数据流), 测试数据库访问权限。
    *   **步骤:**
        1.  **(开发者)** **设计测试场景**: 识别需要进行集成测试的关键业务流程（例如：用户注册并登录、创建订单并扣减库存等）。明确每个场景的API请求参数、预期API响应（状态码、部分关键数据）和预期的数据库状态变化。
        2.  **(开发者/AI)** **编写测试用例 (在 `_test.go` 文件中)**:
            *   通常在 `tests/integration/` 目录下创建测试文件 (如 `user_registration_integration_test.go`)。
            *   **测试环境准备 (`TestMain` 或测试辅助函数)**:
                *   编译项目获得最新的可执行文件 (例如，通过调用 `make build` 或 `go build`)。
                *   启动应用程序实例，并确保其监听测试配置中指定的地址和端口。管理应用的生命周期（启动/停止）。
                *   连接到测试数据库，按需执行数据库迁移、清理测试数据或填充种子数据。
            *   **处理外部依赖**:
                *   对于项目自身的服务和数据库，应在测试环境中直接交互。
                *   对于无法在测试环境中直接调用的**第三方外部服务** (例如，支付网关、外部SaaS API)，如果必须进行自动化测试，可以考虑使用稳定的测试替身 (stubs/fakes) 或 contract testing。优先测试与真实服务的集成。
                *   **避免模拟项目内部组件** (如DAO, Service等)，因为它们是集成测试的被测对象。
            *   **执行业务操作 (在Go测试函数中)**:
                *   使用Go的 `net/http` 包构造并发送HTTP请求到正在运行的应用程序的API端点。或者，可以使用 `os/exec` 包从Go代码中调用 `curl` 命令，并捕获其输出。
                *   示例: `curl -X POST -H "Content-Type: application/json" -d '{"username":"testuser", "password":"password123"}' http://localhost:8000/api/v1/users/register`
            *   **断言 (在Go测试函数中)**:
                *   验证API响应的状态码、头部信息和响应体内容 (可反序列化JSON进行详细检查)。
                *   直接查询测试数据库，验证数据的创建、更新或删除是否符合预期。
                *   如果使用了第三方服务的测试替身，验证与其的交互是否符合预期。
            *   **AI 辅助 (建议性):**
                *   **指令 (示例):** "为用户注册流程设计集成测试用例（在Go的 `testing`框架内）。流程：1. 准备测试环境，启动应用。 2. 使用HTTP客户端 (或 `curl` 通过 `os/exec`) 调用 `POST /api/v1/users/register` API，请求体包含用户名和密码。 3. 在Go测试中断言API响应状态码为201，响应体包含用户基本信息。 4. 在Go测试中直接查询数据库 `users` 表，验证新用户记录已创建，密码已哈希，状态正确。 5. 清理测试数据。请提供Go测试函数的骨架，包括应用启动/停止的建议，以及API调用和数据库断言的占位符。"
                *   AI 可以提供Go测试函数的结构、API调用代码片段 (使用 `net/http` 或 `os/exec` 调用 `curl`) 和数据库查询/断言的建议。
        3.  **(开发者执行与调试):**
            *   **[执行]** 运行集成测试 (例如 `go test ./tests/integration/...`)。
            *   调试失败的测试用例，检查应用日志、API请求/响应详情、数据库状态或测试环境问题。
    *   **关键检查点:** ✅ 关键业务流程被API调用覆盖 ✅ 测试环境（应用、数据库）设置和清理正确 ✅ API请求构造和`curl`调用正确 ✅ API响应断言准确 ✅ 数据库状态断言准确 ✅ 测试用例通过。
    *   **输出物:** 集成测试代码 (Go `_test.go` 文件)，标准Go测试报告 (可包含 `curl` 的输出摘要，如果开发者选择记录)。
    *   **验收标准:** 关键业务流程通过实际API调用验证，组件间交互符合预期，数据一致性得到保证。

3.  **最终检查与准备评审 (Final Checks & Review Preparation):**
    *   **目标:** 确保代码质量符合规范，项目可成功构建，并准备好进行 Code Review。
    *   **核心输入:** 最终的代码库。
    *   **步骤:**
        1.  **(开发者执行) 代码格式化与 Lint**:
            *   运行 `go fmt ./...` 格式化代码。
            *   运行 **`make lint`** (或项目配置的 Lint 命令)。
            *   **修复 Lint 错误**: 根据 Lint 工具的报告修复所有错误和警告。
        2.  **(开发者执行) 构建项目**:
            *   运行 **`make build`** (或项目配置的构建命令)。
            *   确认项目可以成功编译生成目标二进制文件。
        3.  **(开发者参考) 对照 Checklist**:
            *   打开 `luban-agent/ai-rules/backend/project-rules/golang/project-progress.md`。
            *   逐项检查是否已完成所有开发步骤和规范要求（文档、测试、错误处理、日志、注释等）。
        4.  **(开发者执行) 发起 Code Review**:
            *   将代码推送到版本控制系统（如 Git）。
            *   创建 Pull Request (或 Merge Request)。
            *   在 PR 描述中简要说明本次开发内容、关键改动，并邀请相关人员进行评审。
    *   **关键检查点:** ✅ 代码格式化 ✅ Lint 无错误 ✅ 项目构建成功 ✅ Checklist 完成 ✅ Code Review 已发起。
    *   **输出物:** 通过 Lint 和构建的代码 (作为输入到版本控制和PR)，Code Review 请求。
    *   **验收标准:** 代码整洁、符合规范、可构建，并已提交评审。

**完成标志:**
*   依赖注入配置完成且 `wire_gen.go` 已生成并用于测试中的应用初始化。
*   关键业务流程的集成测试已完成并通过。
*   代码通过 Lint 检查且项目可成功构建。
*   已对照 Checklist (`luban-agent/ai-rules/backend/project-rules/golang/project-progress.md`) 进行最终检查。
*   代码已提交并创建 Code Review 请求。

### 步骤7.4: 生成Phase 7汇总文档

**AI指令模板:**
```
请根据Phase 7 集成测试和部署的完成情况，使用汇总模板 `ai-workflows/dev-be/phase-summaries/phase-7-integration-deployment-summary-template.md` 生成本次开发的汇总文档。

请参考前一阶段的汇总文档：`project-summaries/phase-6-router-summary-{biz_module}.md`

请将模板中的占位符替换为实际值：
- {project_name}: [实际项目名称]
- {biz_module}: [实际业务模块名称]
- {completion_time}: [完成时间]
- {developer_name}: [开发者姓名]
- 其他具体的测试结果、部署配置、监控指标等

生成的汇总文档保存为：`project-summaries/phase-7-integration-deployment-summary-{biz_module}.md`

这个汇总文档将作为项目交付和后续维护的重要参考。
```

**输出文件:** `project-summaries/phase-7-integration-deployment-summary-{biz_module}.md`

### 下一步操作
根据工作流，Phase 7 完成后，下一步将进入 **Phase 6: 文档 (Documentation)**。
AI 将协助您生成项目相关的各类文档，并更新 API 文档。

### Phase 6: 文档 (Documentation)

**阶段核心:** AI 辅助，开发者评审。

**AI 角色:** 文档助手 / 生成器。

**目标:** 生成、更新并完善项目相关的各类文档，**特别是生成 Apifox 等工具可直接导入的 API 文档**，确保代码和系统的可理解性、可维护性和易用性。

**核心输入:**
*   最终的、经过评审和测试的代码库。
*   Controller 实现代码 (例如 `app/{biz_module}/controller/*.go`，可能包含 Swagger 注解) 和/或手动维护的API规范文件。
*   数据库 Schema (迁移脚本 `docs/sql/...`, DAO GORM 模型 `app/{biz_module}/dao/model/*.go`)。
*   项目文档规范 (`global-rules/rules-golang-code.mdc` 文档规范部分, `README.md` 模板等)。
*   架构设计文档或图表 (如果存在)。
*   项目的 `Makefile` 或其他构建/文档生成脚本 (如 `swag init`)。

**输出:**
*   更新后的 **OpenAPI (Swagger v2/v3) JSON 或 YAML 文件** (位于如 `api/swagger/` 或 `docs/` 目录，可供各类API工具直接导入)。
*   更新或生成的数据字典 (`docs/database/dictionary.md` 或类似文件)。
*   更新后的项目 `README.md`。
*   (可选) 更新后的架构图、流程图。
*   (可选) 更新后的内部知识库或 Wiki 页面。

**流程:**

1.  **API 文档生成/更新 (API Documentation Generation/Update - OpenAPI Standard):**
    *   **目标:** 确保生成**最新且符合 OpenAPI (Swagger v2/v3) 标准的 JSON 或 YAML 文件**，准确反映接口定义、参数、请求/响应示例和错误码。此标准格式的文档可被多种工具（如 Apifox, Swagger Editor, Postman 等）导入和使用。
    *   **核心输入:** Controller Go 文件 (例如 `app/{biz_module}/controller/*.go`，包含 `swaggo/swag` 或其他 OpenAPI 生成器兼容的注解) 或手动维护的 OpenAPI/Swagger 规范文件。
    *   **步骤:**
        1.  **(开发者/AI)** 如果使用代码注解（如 `swaggo/swag`），检查 Controller 文件中的注解是否完整和准确，特别是 `@Summary`, `@Description`, `@Param`, `@Success`, `@Failure` 等，这些会影响生成文档的质量。如果手动维护 API 规范文件，则检查其内容是否与最新代码实现一致。
        2.  **(AI 生成/开发者执行脚本):**
            *   **指令 (示例 - Go Swagger 注解 to OpenAPI v3 YAML):** "请扫描 `app/{biz_module}/controller/` 目录下的 Controller Go 文件中的 Swagger 注解 (确保使用 `swaggo/swag` 规范)，并生成 **OpenAPI v3 YAML 文件**到 `docs/openapi.yaml` (或 `api/swagger/openapi.yaml`)。该文件应符合 OpenAPI v3 标准。请提供运行 `swag init --output ./docs --outputTypes yaml --dir ./app/{biz_module}/controller/ --parseDependency --parseInternal` (根据实际项目结构调整) 或类似工具的命令。"
            *   **(开发者)** 执行相应的命令 (如 `make swagger` 或 `swag init ...`) 来生成最新的 **OpenAPI/Swagger JSON/YAML** 文件。如果API文档是手动维护的，则开发者或AI辅助更新该文件。
        3.  **(开发者评审与验证):**
            *   检查生成的 OpenAPI/Swagger 文件内容是否准确，描述是否清晰，示例是否正确。可以使用 Swagger Editor 或其他在线验证器检查其格式规范性。
            *   **关键验证 (推荐)**: 尝试将生成的文件**导入 API 工具 (如 Apifox, Postman)**，检查是否能成功导入，并且接口信息在工具中显示正确。
    *   **关键检查点:** ✅ API 定义 (注解或规范文件) 已更新 ✅ 文档生成命令执行成功 (如适用) ✅ 生成的文档符合 OpenAPI 标准 (JSON/YAML) ✅ **至少一种主流 API 工具导入验证成功**。
    *   **输出物:** 最新的、符合 OpenAPI 标准的 API 文档文件。
    *   **验收标准:** API 文档与实际接口完全一致，且能被标准 OpenAPI 工具正确解析和使用。

2.  **数据字典生成/更新 (Data Dictionary Generation/Update):**
    *   **目标:** 提供清晰、准确的数据库表结构文档。
    *   **核心输入:** 数据库迁移脚本 (`docs/sql/...`) 或 DAO 层 GORM 模型 (`app/{biz_module}/dao/model/*.go`)。
    *   **步骤:**
        1.  **(开发者/AI)** 确认数据模型或迁移脚本是最终版本。
        2.  **(AI 生成):**
            *   **指令 (示例):** "请扫描 `app/user/dao/model/user.go` 文件中的 `User` GORM 模型定义。提取表名 (`users`)、字段名、数据类型、是否允许 NULL、索引、注释等信息，并生成 Markdown 格式的数据字典表格，追加到 `docs/database/dictionary.md` 文件中。"
            *   AI 根据模型定义生成数据字典的 Markdown 内容。
        3.  **(开发者评审与完善):** 检查 AI 生成的表格内容是否准确，特别是字段类型、约束和注释。手动补充 AI 可能遗漏的业务含义说明。
    *   **关键检查点:** ✅ 基于最终模型生成 ✅ 字段信息准确 ✅ 注释清晰。
    *   **输出物:** 更新后的数据字典文件 (`dictionary.md`)。
    *   **验收标准:** 数据字典准确反映当前数据库结构。

3.  **README 及其他文档更新 (README & Other Docs Update):**
    *   **目标:** 更新项目入口文档 (README) 和其他相关文档，反映最新的功能、用法或架构变更。
    *   **核心输入:** 项目 `README.md`, 相关代码变更, 新增功能说明。
    *   **步骤:**
        1.  **(开发者/AI)** 识别需要更新的文档部分（如：项目简介、快速开始、API 端点列表、配置说明、部署步骤等）。
        2.  **(AI 生成/开发者修改):**
            *   **指令 (示例):** "请更新项目根目录下的 `README.md` 文件。在 '快速开始' 部分，添加运行 `make build && ./bin/user_service -conf ./configs` 的启动说明。在 'API 端点' 部分，添加用户注册 (POST /api/v1/users/register) 和登录 (POST /api/v1/users/login) 的简要描述。"
            *   AI 根据指令生成 README 的更新内容。
        3.  **(开发者评审与完善):** 确保 README 内容清晰、准确、易于理解，特别是对于新用户。检查其他相关文档（如架构图、设计文档）是否也需要同步更新。
    *   **关键检查点:** ✅ README 反映最新状态 ✅ 快速开始/用法说明清晰 ✅ 其他相关文档同步更新。
    *   **输出物:** 更新后的 `README.md` 及其他相关文档。
    *   **验收标准:** 项目文档准确、完整，能够有效指导其他开发者或用户。

**完成标志:**
*   **Apifox 可导入的 API 文档**已生成/更新并与代码一致。
*   数据字典已生成/更新并与数据库结构一致。
*   项目 README 及其他必要文档已更新。

### 下一步操作
根据工作流，Phase 6 完成后，下一步将进入 **Phase 7: 关键成功因素 (Key Success Factors)**。
AI 将协助您识别项目的关键成功因素，并生成相应的文档。

### Phase 7: 关键成功因素 (Key Success Factors)

**阶段核心:** AI 辅助，开发者评审。

**AI 角色:** 文档助手 / 生成器。

**目标:** 生成、更新并完善项目相关的各类文档，**特别是生成关键成功因素的文档**，确保项目成功的关键因素得到充分考虑和记录。

**核心输入:**
*   最终的、经过评审和测试的代码库。
*   项目文档规范 (`global-rules/rules-golang-code.mdc` 文档规范部分, `README.md` 模板等)。
*   架构设计文档或图表 (如果存在)。
*   项目的 `Makefile` 或其他构建/文档生成脚本 (如 `swag init`)。
*   项目关键成功因素的详细描述。

**输出:**
*   更新后的 **关键成功因素文档** (位于如 `docs/key-success-factors/` 目录，可供项目相关人员查阅)。
*   更新或生成的数据字典 (`docs/database/dictionary.md` 或类似文件)。
*   更新后的项目 `README.md`。
*   (可选) 更新后的架构图、流程图。
*   (可选) 更新后的内部知识库或 Wiki 页面。

**流程:**

1.  **关键成功因素文档生成/更新 (Key Success Factors Documentation Generation/Update):**
    *   **目标:** 确保生成**最新且符合项目要求的文档**，准确反映项目的关键成功因素和相关要求。
    *   **核心输入:** 项目关键成功因素的详细描述。
    *   **步骤:**
        1.  **(开发者/AI)** 根据项目需求和实际情况，详细描述项目的关键成功因素。
        2.  **(AI 生成/开发者执行脚本):**
            *   **指令 (示例 - 关键成功因素文档生成):** "请为项目生成关键成功因素文档。文档应包括项目的目标、关键成功因素、关键绩效指标 (KPI) 等。"
            *   **(开发者)** 执行相应的命令 (如 `make key-success-factors` 或 `swag init ...`) 来生成最新的 **关键成功因素文档**。如果文档是手动维护的，则开发者或AI辅助更新该文件。
        3.  **(开发者评审与验证):**
            *   检查生成的关键成功因素文档内容是否准确，描述是否清晰，是否符合项目要求。
            *   **关键验证 (推荐)**: 尝试将生成的文档**导入项目相关人员查阅**，检查是否能成功导入，并且文档内容在项目中得到应用。
    *   **关键检查点:** ✅ 关键成功因素文档生成/更新 ✅ 文档内容准确清晰 ✅ 符合项目要求。
    *   **输出物:** 更新后的 **关键成功因素文档**。
    *   **验收标准:** 关键成功因素文档与项目需求完全一致，且能被项目相关人员成功查阅和应用。

2.  **数据字典生成/更新 (Data Dictionary Generation/Update):**
    *   **目标:** 提供清晰、准确的数据库表结构文档。
    *   **核心输入:** 数据库迁移脚本 (`docs/sql/...`) 或 DAO 层 GORM 模型 (`app/{biz_module}/dao/model/*.go`)。
    *   **步骤:**
        1.  **(开发者/AI)** 确认数据模型或迁移脚本是最终版本。
        2.  **(AI 生成):**
            *   **指令 (示例):** "请扫描 `app/user/dao/model/user.go` 文件中的 `User` GORM 模型定义。提取表名 (`users`)、字段名、数据类型、是否允许 NULL、索引、注释等信息，并生成 Markdown 格式的数据字典表格，追加到 `docs/database/dictionary.md` 文件中。"
            *   AI 根据模型定义生成数据字典的 Markdown 内容。
        3.  **(开发者评审与完善):** 检查 AI 生成的表格内容是否准确，特别是字段类型、约束和注释。手动补充 AI 可能遗漏的业务含义说明。
    *   **关键检查点:** ✅ 基于最终模型生成 ✅ 字段信息准确 ✅ 注释清晰。
    *   **输出物:** 更新后的数据字典文件 (`dictionary.md`)。
    *   **验收标准:** 数据字典准确反映当前数据库结构。

3.  **README 及其他文档更新 (README & Other Docs Update):**
    *   **目标:** 更新项目入口文档 (README) 和其他相关文档，反映最新的功能、用法或架构变更。
    *   **核心输入:** 项目 `README.md`, 相关代码变更, 新增功能说明。
    *   **步骤:**
        1.  **(开发者/AI)** 识别需要更新的文档部分（如：项目简介、快速开始、API 端点列表、配置说明、部署步骤等）。
        2.  **(AI 生成/开发者修改):**
            *   **指令 (示例):** "请更新项目根目录下的 `README.md` 文件。在 '快速开始' 部分，添加运行 `make build && ./bin/user_service -conf ./configs` 的启动说明。在 'API 端点' 部分，添加用户注册 (POST /api/v1/users/register) 和登录 (POST /api/v1/users/login) 的简要描述。"
            *   AI 根据指令生成 README 的更新内容。
        3.  **(开发者评审与完善):** 确保 README 内容清晰、准确、易于理解，特别是对于新用户。检查其他相关文档（如架构图、设计文档）是否也需要同步更新。
    *   **关键检查点:** ✅ README 反映最新状态 ✅ 快速开始/用法说明清晰 ✅ 其他相关文档同步更新。
    *   **输出物:** 更新后的 `README.md` 及其他相关文档。
    *   **验收标准:** 项目文档准确、完整，能够有效指导其他开发者或用户。

**完成标志:**
*   关键成功因素文档已生成/更新并与项目需求一致。
*   数据字典已生成/更新并与数据库结构一致。
*   项目 README 及其他必要文档已更新。

---

## 3. 关键成功因素

*   **清晰的规范与示例:** AI 的输出质量高度依赖于输入规范的明确性和代码示例的质量。**优化 `ddd/*.mdc` 文件是重中之重。**
*   **结构化输入:** 每次与 AI 交互时，提供精准、最小化的上下文信息，并明确引用相关规范文档。
*   **迭代与人工评审:** 将 AI 生成的代码视为初稿，必须经过开发者评审、测试和必要的修改。
*   **开发者主导:** 开发者始终负责需求理解、架构设计、复杂逻辑实现、最终质量把控和安全责任。
*   **流程适应性:** 本工作流是一个指导框架，具体实践中可根据项目和团队情况灵活调整。

## 4. AI行为约束规则

### 4.1 基本原则
1. **最小生成原则**
   - 仅生成接口定义要求的代码
   - 禁止生成额外的工具函数或辅助代码
   - 禁止修改已定义的数据结构和接口

2. **规范遵循原则**
   - 严格遵循项目定义的各层规范
   - 禁止引入未在项目中定义的依赖
   - 禁止更改项目的配置和结构

3. **安全性原则**
   - 禁止生成可能导致安全问题的代码
   - 禁止在日志中包含敏感信息
   - 禁止使用不安全的数据库操作

### 4.2 分层约束规则

#### 4.2.1 DAO层约束
- 仅实现Repository接口定义的方法
- 禁止跨库操作和直接SQL拼接
- 禁止自定义缓存策略
- 错误必须转换为预定义类型

#### 4.2.2 Service层约束
- 仅实现用例接口定义的方法
- 禁止直接访问数据库
- 禁止实现非业务逻辑的功能
- 事务必须在Service层控制

#### 4.2.3 Controller层约束
- 仅实现API接口定义的方法
- 禁止包含业务逻辑
- 入参校验必须使用统一验证器
- 响应格式必须统一

### 4.3 代码生成限制

#### 4.3.1 禁止生成的代码类型
1. 工具类或辅助函数
2. 中间件代码
3. 配置管理代码
4. 自定义验证器
5. 数据库迁移代码
6. 缓存管理器
7. 连接池管理
8. 性能监控代码

#### 4.3.2 依赖注入限制
- 只允许注入配置文件中明确定义的依赖
- 禁止创建全局变量和单例
- 禁止直接初始化外部服务
- 所有依赖必须通过构造函数注入

#### 4.3.3 数据库操作限制
- 禁止直接拼接SQL语句
- 禁止使用原生SQL查询（除非有特殊性能要求）
- 禁止修改数据库连接配置
- 必须使用预编译语句

#### 4.3.4 缓存操作限制
- 只允许缓存配置文件中定义的数据
- 禁止自行决定缓存策略
- 禁止实现分布式锁
- 缓存key必须遵循统一前缀规范

### 4.4 测试代码约束
- 只允许使用指定的测试框架
- 禁止在测试中连接真实数据库
- 必须使用mock数据
- 禁止更改测试配置

### 4.5 错误处理约束
- 只允许使用预定义的错误类型
- 禁止自定义新的错误码
- 必须在对应层处理相应的错误
- 禁止在错误信息中包含敏感数据

### 4.6 日志记录约束
- 只允许使用项目定义的日志接口
- 禁止直接打印到标准输出
- 禁止记录敏感信息
- 日志级别必须符合规范要求

### 4.7 性能相关约束
- 禁止自行添加索引
- 禁止修改数据库配置参数
- 批量操作必须使用预定义的批次大小
- 禁止更改连接池设置

### 4.8 代码模板规范
每个层的具体代码模板和实现示例请参考对应的规范文档：
- DAO层：`luban-agent/ai-rules/backend/project-rules/golang/ddd/dao.mdc`
- Service层：`luban-agent/ai-rules/backend/project-rules/golang/ddd/service.mdc`
- Controller层：`luban-agent/ai-rules/backend/project-rules/golang/ddd/controller.mdc`

## 5. DDD分层架构编码规则

### 5.1 DAO层规范
- **职责**：实现数据访问接口 (Repository)
- **位置**：`app/{module}/dao/impl/`
- **文件组织**：
  - `{entity}_impl.go`: DAO接口实现
  - `{entity}_impl_test.go`: DAO单元测试
- **规范要求**：
  1. 实现Domain层定义的Repository接口
  2. 负责领域模型与数据模型 (DO/PO) 的转换
  3. 封装数据库操作细节 (GORM等)
  4. 使用预定义的数据库错误类型
  5. 包含基础的CRUD和特定查询方法
  6. 必须有完整的单元测试 (Mock DB)
  7. 禁止包含业务逻辑
  8. 具体规范参考 `luban-agent/ai-rules/backend/project-rules/golang/ddd/dao.mdc`

### 5.2 Domain层规范
- **职责**：定义核心业务模型和接口
- **位置**：`app/{module}/domain/`
- **文件组织**：
  - `struct.go`: 领域实体定义
  - `interface.go`: Repository接口定义
  - `errors.go`: 领域错误定义
  - `const.go`: 领域常量定义
- **规范要求**：
  1. 实体必须是充血模型，包含业务行为
  2. 所有字段必须有明确的业务含义
  3. 不允许引入基础设施依赖
  4. 错误必须定义为领域错误
  5. Repository接口设计必须遵循CQRS原则
  6. 注释必须包含业务规则说明
  7. 具体规范参考 `luban-agent/ai-rules/backend/project-rules/golang/ddd/domain.mdc`

### 5.3 Service层规范
- **职责**：实现业务用例和编排
- **位置**：`app/{module}/service/`
- **文件组织**：
  - `{usecase}_service.go`: 具体用例实现
  - `{usecase}_service_test.go`: 用例测试
- **规范要求**：
  1. 仅依赖Domain层接口和DAO层接口 (通过注入)
  2. 必须包含完整的业务逻辑
  3. 事务控制必须在Service层
  4. 必须处理所有异常场景 (捕获DomainError, DAOError)
  5. 必须包含详细的日志记录
  6. 必须有完整的单元测试 (Mock Repository)
  7. 禁止直接操作数据库或进行数据模型转换
  8. 具体规范参考 `luban-agent/ai-rules/backend/project-rules/golang/ddd/service.mdc`

### 5.4 DTO层规范
- **职责**：定义数据传输对象
- **位置**：`app/{module}/dto/`
- **文件组织**：
  - `request.go`: 请求DTO定义
  - `response.go`: 响应DTO定义
  - `converter.go`: 转换器定义 (Domain <-> DTO)
- **规范要求**：
  1. 字段名必须使用驼峰命名
  2. 必须包含字段验证标签 (如 `binding`)
  3. JSON标签必须是小写驼峰
  4. 时间字段必须是UTC秒数 (int64)
  5. 必须提供与Domain实体的转换方法 (通常在Service层调用)
  6. 注释必须包含字段说明
  7. 具体规范参考 `luban-agent/ai-rules/backend/project-rules/golang/ddd/dto.mdc`

### 5.5 Controller层规范
- **职责**：处理HTTP请求和响应
- **位置**：`app/{module}/controller/`
- **文件组织**：
  - `{resource}_controller.go`: 控制器实现
  - `{resource}_controller_test.go`: 控制器测试
- **规范要求**：
  1. 只负责请求绑定、参数验证、调用Service、响应封装
  2. 必须进行参数验证 (使用DTO的验证标签或方法)
  3. 错误必须转换为HTTP错误 (基于Service返回的错误)
  4. 必须遵循RESTful规范
  5. 必须有Swagger文档注解
  6. 必须处理请求上下文 (TraceID, UserID等)
  7. 禁止包含业务逻辑或直接调用DAO
  8. 具体规范参考 `luban-agent/ai-rules/backend/project-rules/golang/ddd/controller.mdc`

### 5.6 Router层规范
- **职责**：定义路由规则和中间件
- **位置**：`app/{module}/router/`
- **文件组织**：
  - `router.go`: 路由注册
  - `middleware.go`: 中间件定义
- **规范要求**：
  1. 路由必须版本化 (如 `/v1`)
  2. 必须包含认证、授权中间件
  3. 必须包含请求日志、TraceID中间件
  4. 必须处理CORS
  5. 可选包含限流措施
  6. 必须处理panic恢复
  7. 具体规范参考 `luban-agent/ai-rules/backend/project-rules/golang/ddd/router.mdc`

### 5.7 跨层规范
1. **依赖规则**：
   - Router -> Controller -> Service -> Domain/DAO
   - 外层可以依赖内层
   - 内层不能依赖外层
   - 依赖必须通过接口注入 (Wire)

2. **错误处理**：
   - DAO层：定义/转换数据库错误 -> DAO错误
   - Domain层：定义业务错误
   - Service层：处理DAO错误和Domain错误 -> Service错误
   - Controller层：处理Service错误 -> HTTP错误

3. **日志记录**：
   - DAO层：数据库操作日志 (DEBUG/WARN)
   - Domain层：仅核心业务变化日志 (INFO)
   - Service层：完整业务流程日志 (INFO/ERROR)
   - Controller层：请求入口/出口日志 (INFO)

4. **数据转换**：
   - DO/PO <-> Domain：在DAO层转换
   - Domain <-> DTO：在Service层调用DTO转换器进行转换

5. **验证规则**：
   - DTO层/Controller层：请求参数格式验证
   - Service层：业务逻辑验证
   - Domain层：业务规则验证 (实体方法内)

6. **测试要求**：
   - DAO层：单元测试 (Mock DB)
   - Domain层：单元测试
   - Service层：单元测试 (Mock Repository) / 集成测试
   - Controller层：单元测试 (Mock Service) / API测试

### 5.8 性能优化规范
1. **DAO层性能优化**：
   - 合理使用索引
   - 批量操作优化
   - 分页查询优化
   - 连接池管理
   - 缓存策略实现

2. **Service层性能优化**：
   - 并发处理
   - 异步任务处理
   - 批量处理优化
   - 缓存预热策略
   - 限流熔断机制

3. **Controller层性能优化**：
   - 请求参数校验优化
   - 响应数据压缩
   - 静态资源缓存
   - 连接复用
   - 超时控制

### 5.9 监控规范
1. **基础监控指标**：
   - CPU使用率
   - 内存使用率
   - 磁盘IO
   - 网络IO
   - goroutine数量

2. **业务监控指标**：
   - QPS统计
   - 响应时间
   - 错误率
   - 业务成功率
   - 并发用户数

3. **日志监控**：
   - 错误日志监控
   - 慢查询日志
   - 业务异常日志
   - 安全审计日志
   - 操作日志

### 5.10 安全规范
1. **数据安全**：
   - 敏感数据加密
   - 数据脱敏处理
   - 访问权限控制
   - 数据备份策略
   - 数据销毁机制

2. **接口安全**：
   - 接口鉴权
   - 参数校验
   - SQL注入防护
   - XSS防护
   - CSRF防护

3. **传输安全**：
   - HTTPS加密
   - 数据签名
   - 时间戳校验
   - 防重放攻击
   - 加密算法选择

### 5.11 代码质量规范
1. **命名规范**：
   - 包名：全小写，简短有意义
   - 接口名：动词+名词，如`UserService`
   - 结构体名：名词，如`User`
   - 方法名：驼峰，动词开头，如`CreateUser`
   - 变量名：驼峰，名词，如`userID`

2. **注释规范**：
   - 包注释：包的用途、功能说明
   - 接口注释：接口的职责、用例
   - 方法注释：功能、参数、返回值
   - 变量注释：用途、取值范围
   - 实现注释：复杂逻辑说明

3. **代码组织**：
   - 文件大小控制：建议不超过500行
   - 函数大小：建议不超过50行
   - 参数数量：建议不超过5个
   - 代码嵌套：建议不超过4层
   - 包的大小：建议不超过20个文件

### 5.12 错误处理规范
1. **错误定义**：
   ```go
   // 错误码定义
   const (
       ErrCodeSuccess        = 0
       ErrCodeParamInvalid   = 400001
       ErrCodeUnauthorized   = 401001
       ErrCodeForbidden      = 403001
       ErrCodeNotFound       = 404001
       ErrCodeInternal       = 500001
   )

   // 错误信息定义
   var (
       ErrParamInvalid   = errors.New("参数无效")
       ErrUnauthorized   = errors.New("未授权")
       ErrForbidden      = errors.New("禁止访问")
       ErrNotFound       = errors.New("资源不存在")
       ErrInternal       = errors.New("内部错误")
   )
   ```

2. **错误处理流程**：
   ```go
   // Service层错误处理示例
   func (s *userService) CreateUser(ctx context.Context, req *dto.CreateUserReq) (*dto.CreateUserResp, error) {
       // 参数校验
       if err := req.Validate(); err != nil {
           return nil, errors.Wrap(ErrParamInvalid, err.Error())
       }

       // 业务处理
       user, err := s.userRepo.Create(ctx, req.ToEntity())
       if err != nil {
           // 特定错误处理
           if errors.Is(err, ErrDuplicateKey) {
               return nil, errors.Wrap(ErrParamInvalid, "用户已存在")
           }
           // 未知错误
           return nil, errors.Wrap(ErrInternal, "创建用户失败")
       }

       return dto.NewCreateUserResp(user), nil
   }
   ```

3. **错误日志规范**：
   ```go
   // 错误日志记录示例
   func logError(ctx context.Context, err error, msg string) {
       logger.WithContext(ctx).WithError(err).
           WithField("trace_id", trace.GetTraceID(ctx)).
           WithField("user_id", auth.GetUserID(ctx)).
           Error(msg)
   }
   ```

### 5.13 测试规范
1. **单元测试命名**：
   ```go
   // 正确的测试方法命名
   func TestUserService_CreateUser_Success(t *testing.T)
   func TestUserService_CreateUser_ParamInvalid(t *testing.T)
   func TestUserService_CreateUser_UserExists(t *testing.T)
   ```

2. **测试用例组织**：
   ```go
   func TestUserService_CreateUser(t *testing.T) {
       tests := []struct {
           name    string
           req     *dto.CreateUserReq
           mock    func(repo *mockRepo)
           wantErr error
       }{
           {
               name: "成功创建用户",
               req:  &dto.CreateUserReq{Username: "test"},
               mock: func(repo *mockRepo) {
                   repo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)
               },
               wantErr: nil,
           },
           // ... 其他测试用例
       }

       for _, tt := range tests {
           t.Run(tt.name, func(t *testing.T) {
               // ... 测试逻辑
           })
       }
   }
   ```

3. **基准测试规范**：
   ```go
   func BenchmarkUserService_CreateUser(b *testing.B) {
       svc := setupTestService(b)
       req := &dto.CreateUserReq{Username: "test"}

       b.ResetTimer()
       for i := 0; i < b.N; i++ {
           _, _ = svc.CreateUser(context.Background(), req)
       }
   }
   ```

### 5.14 DDD分层架构实现细节

#### 5.14.1 DAO层实现规范
1. **数据模型定义 (DO/PO)**:
   ```go
   // dao/model/user.go (假设这是数据模型)
   package model

   type User struct {
       ID        int64  `gorm:"primaryKey"`
       Username  string `gorm:"uniqueIndex"`
       Password  string
       Status    int32
       CreatedAt int64
       UpdatedAt int64
       DeletedAt gorm.DeletedAt `gorm:"index"` // GORM软删除支持
   }

   // 可选：指定表名
   func (User) TableName() string {
       return "users"
   }
   ```

2. **DAO 实现**:
   ```go
   // dao/impl/user_impl.go
   package impl

   import (
       // ... imports ...
       "your_project/app/user/dao/model"
       "your_project/app/user/domain"
   )

   type userDAO struct {
       db *gorm.DB
   }

   func NewUserDAO(db *gorm.DB) domain.UserRepository { // 实现 domain.UserRepository 接口
       return &userDAO{db: db}
   }

   // --- 实现 UserRepository 接口方法 ---

   func (d *userDAO) Create(ctx context.Context, user *domain.User) error {
       // 1. Domain -> DO/PO 转换
       do := d.toDO(user)
       // 2. 执行数据库操作
       if err := d.db.WithContext(ctx).Create(do).Error; err != nil {
           // 3. 错误转换 (例如，唯一键冲突)
           if errors.Is(err, gorm.ErrDuplicatedKey) { // 假设GORM能返回此类错误或通过driver判断
               return domain.ErrUserExists // 返回Domain层定义的错误
           }
           return fmt.Errorf("dao create user failed: %w", err) // 返回通用DAO错误
       }
       // 4. 将DB生成的ID回写给Domain对象
       user.ID = do.ID
       return nil
   }

   func (d *userDAO) FindByID(ctx context.Context, id int64) (*domain.User, error) {
       var do model.User
       if err := d.db.WithContext(ctx).First(&do, id).Error; err != nil {
           if errors.Is(err, gorm.ErrRecordNotFound) {
               return nil, domain.ErrUserNotFound
           }
           return nil, fmt.Errorf("dao find user by id failed: %w", err)
       }
       // DO/PO -> Domain 转换
       return d.toDomain(&do), nil
   }

   // --- 其他接口方法实现 ---

   // --- 转换方法 ---
   func (d *userDAO) toDO(user *domain.User) *model.User {
       return &model.User{
           ID:        user.ID,
           Username:  user.Username,
           Password:  user.Password,
           Status:    user.Status,
           CreatedAt: user.CreateAt,
           UpdatedAt: user.UpdateAt,
       }
   }

   func (d *userDAO) toDomain(do *model.User) *domain.User {
       return &domain.User{
           ID:        do.ID,
           Username:  do.Username,
           Password:  do.Password,
           Status:    do.Status,
           CreateAt:  do.CreatedAt,
           UpdateAt:  do.UpdatedAt,
       }
   }
   ```

### 4.8 AI辅助开发-规范与标准解读

**重要提示**: AI 在执行后续开发任务前，**必须**首先学习并严格遵守以下所有相关规范文档。

1.  **核心通用规范**:
    *   `luban-agent/ai-rules/backend/guidelines/general-dev-standards.md` (通用开发标准，含各层验收细则)
    *   `luban-agent/ai-rules/backend/project-rules/golang/rules-golang-code.mdc` (Golang 通用代码生成规则)
    *   `luban-agent/ai-rules/backend/project-rules/golang/rules-kratos-code.mdc` (Kratos 框架使用规范)
    *   `luban-agent/ai-rules/backend/project-rules/api-design-standards.md` (API 设计规范)
    *   `luban-agent/ai-rules/backend/project-rules/error-code-standards.md` (错误码规范)
    *   `luban-agent/ai-rules/backend/project-rules/testing-standards.md` (测试规范)

2.  **DDD 相关规范**:
    *   `luban-agent/ai-rules/backend/project-rules/golang/ddd/introduction.md` (DDD 导论与核心概念)
    *   `luban-agent/ai-rules/backend/project-rules/golang/ddd/domain.mdc` (Domain 层开发规范)
    *   `luban-agent/ai-rules/backend/project-rules/golang/ddd/application.mdc` (Application Service 层开发规范)
    *   `luban-agent/ai-rules/backend/project-rules/golang/ddd/interfaces.mdc` (Interface 层 (Controller/GRPC) 开发规范)
    *   `luban-agent/ai-rules/backend/project-rules/golang/ddd/dao.mdc` (DAO (Repository Impl) 层开发规范)
    *   `luban-agent/ai-rules/backend/project-rules/golang/ddd/dto.mdc` (DTO 定义与转换规范)

3.  **数据库相关规范**:
    *   `luban-agent/ai-rules/backend/project-rules/database/db-design-mysql.md` (MySQL 数据库设计规范)
    *   `luban-agent/ai-rules/backend/project-rules/database/db-design-postgres.md` (PostgreSQL 数据库设计规范)
    *   `luban-agent/ai-rules/backend/project-rules/database/rules-clickhouse-code.md` (ClickHouse 使用规范 - 如适用)
    *   `luban-agent/ai-rules/backend/project-rules/database/rules-elasticsearch-code.md` (Elasticsearch 使用规范 - 如适用)
    *   `luban-agent/ai-rules/backend/project-rules/database/rules-redis-code.md` (Redis 使用规范 - 如适用)

4.  **其他工具与库规范**:
    *   (按需补充，例如 `luban-agent/ai-rules/backend/project-rules/tools/kafka-usage.md`)

**AI 学习指令**: "请仔细阅读并理解以上所有标记为`核心通用规范`和`DDD 相关规范`的文档。对于数据库相关规范，请重点阅读与本项目选型 (PostgreSQL) 一致的规范。其他规范，在涉及到具体技术选型时再行查阅。"

## 5. AI 开发者代码生成辅助规则

本章节定义 AI 在实际生成各层代码时，需要遵循的具体规则和参考模板。

### 5.1 通用生成原则
*   **严格遵循规范**: 所有代码生成必须严格遵循 `luban-agent/ai-rules/backend/project-rules/golang/rules-golang-code.mdc` 和 `luban-agent/ai-rules/backend/project-rules/golang/rules-kratos-code.mdc`。
*   **DDD分层**: 严格按照 `luban-agent/ai-rules/backend/project-rules/golang/ddd/` 目录下定义的各层职责进行代码组织。
*   **错误处理**: 遵循 `luban-agent/ai-rules/backend/project-rules/error-code-standards.md` 和 `luban-agent/ai-rules/backend/project-rules/golang/rules-golang-code.mdc` 中的错误处理部分。
*   **API设计**: Controller/GRPC接口设计遵循 `luban-agent/ai-rules/backend/project-rules/api-design-standards.md`。
*   **日志**: 遵循 `luban-agent/ai-rules/backend/project-rules/golang/rules-golang-code.mdc` 中的日志规范。
*   **测试**: 遵循 `luban-agent/ai-rules/backend/project-rules/testing-standards.md` 和各层规范中的测试要求。
*   **常量与配置**: 通用的常量定义在`app/consts`；禁止硬编码配置信息。
*   **命名**: 严格遵循 `luban-agent/ai-rules/backend/project-rules/golang/rules-golang-code.mdc` 中的命名约定。
*   **注释**: 关键代码、公共函数、结构体必须有注释，解释"为什么"而非"是什么"。

### 5.2 Domain层实体与接口 (`domain`)
*   **参考规范**: `luban-agent/ai-rules/backend/project-rules/golang/ddd/domain.mdc`
*   **核心任务**:
    *   定义领域实体 (Structs) 及其业务方法。
    *   定义仓储接口 (Repository Interfaces)。
    *   定义领域服务接口 (Domain Service Interfaces - 如需要)。

### 5.3 DAO层实现 (`internal/data` 或 `biz_module/internal/data`)
*   **参考规范**: `luban-agent/ai-rules/backend/project-rules/golang/ddd/dao.mdc`
*   **核心任务**:
    *   实现 Domain 层定义的 Repository 接口。
    *   定义 GORM Model (DO/PO)，通常在 `biz_module/internal/data/model` 或 `internal/data/model`。
    *   实现 DO/PO 与 Domain Entity 之间的转换逻辑。

### 5.4 Application Service层 (`internal/service` 或 `biz_module/internal/service`)
*   **参考规范**: `luban-agent/ai-rules/backend/project-rules/golang/ddd/application.mdc`
*   **核心任务**:
    *   实现业务用例，编排 Domain 实体和 Repository。
    *   处理 DTO 与 Domain Entity 之间的转换。
    *   管理事务 (通常在此层开始和结束)。

### 5.5 Interface层 (Controller/GRPC) (`internal/server/http` 或 `internal/server/grpc` 或 `biz_module/internal/interfaces/http_controller` 等)
*   **参考规范**: `luban-agent/ai-rules/backend/project-rules/golang/ddd/interfaces.mdc`
*   **核心任务**:
    *   接收外部请求 (HTTP/GRPC)。
    *   参数校验与绑定。
    *   调用 Application Service 处理业务逻辑。
    *   将 Service 返回结果转换为 API 响应。
    *   遵循 `luban-agent/ai-rules/backend/project-rules/api-design-standards.md`。

### 5.6 DTO 定义 (`model/dto` 或 `biz_module/model/dto`)
*   **参考规范**: `luban-agent/ai-rules/backend/project-rules/golang/ddd/dto.mdc`
*   **核心任务**:
    *   为 API 请求和响应定义数据传输对象。
    *   包含参数校验标签 (如 `validate` tag)。
    *   JSON tag 统一使用小写驼峰。
    *   与前端交互的时间统一是 UTC 秒数，整数值。
    *   `int` 都是 `int64`。

### 4.9 AI 交互与确认机制
当 AI 完成一个阶段或关键步骤的所有"完成标志"后，将参照文档中定义的"下一步操作"列表，向用户报告已完成的工作，并提示下一个建议执行的阶段或步骤。
---

## 📖 文档修复记录

**🔧 本次修复的主要问题** (基于Gil Kratos用户管理系统实际开发经验):

### 1. **Phase验证机制不完善** ✅ 已修复
**问题**: 验证命令不够具体，缺少实际遇到的编译错误处理
**修复内容**:
- 添加了具体的验证命令格式和参数
- 补充了实际遇到的编译错误类型和修复方法
- 增加了`unreachable code`、`format string`、包导入问题的具体处理方案
- 添加了依赖注入失败和API测试失败的处理指导

### 2. **Phase汇总系统使用说明不完整** ✅ 已修复
**问题**: 缺少实际的存储位置、使用方法和效果验证数据
**修复内容**:
- 更新了汇总文档的实际存储位置：`project-summaries/`目录
- 添加了AI助手使用汇总的最佳实践命令
- 提供了量化的优化效果数据：Token节省60-80%
- 完善了汇总文档的生成模板和结构要求

### 3. **AI指令模板过于简化** ✅ 已修复
**问题**: Phase汇总生成的AI指令模板缺少具体的结构要求
**修复内容**:
- 重新设计了Phase 1汇总生成的AI指令模板
- 添加了详细的汇总文档结构要求
- 包含了验证结果、文件清单、核心实现等关键信息
- 强调了汇总文档对后续Phase开发的重要性

### 4. **Phase 2核心输入优化** ✅ 已修复
**问题**: 仍然依赖读取大量原始文件，未充分利用Phase汇总系统
**修复内容**:
- 将Phase 1汇总文档设为主要输入源
- 添加了输入优化效果的量化数据
- 强调了Phase汇总系统对Token消耗和开发效率的提升作用
- 提供了具体的使用指导

### 5. **验证失败处理经验缺失** ✅ 已修复
**问题**: 缺少基于实际开发经验的问题修复指导
**修复内容**:
- 添加了实际遇到的编译错误类型和解决方案
- 补充了依赖注入配置问题的处理方法
- 增加了API测试失败的常见原因和修复步骤
- 提供了系统性的问题排查流程

**修复依据**: 基于Gil Kratos用户管理系统的完整开发过程，从AI开发助手的角度分析和完善工作流程文档。

---

## 📚 AI助手开发经验总结

### 🎯 成功经验
1. **严格按Phase顺序开发**: 确保每层依赖关系清晰，避免跨层问题
2. **立即验证每个Phase**: 发现问题及时修复，避免问题累积到后续Phase
3. **充分利用Phase汇总系统**: 大幅减少Token消耗，提高开发效率
4. **详细记录验证结果**: 便于问题排查和经验积累
5. **保持代码质量标准**: 确保长期可维护性

### ⚠️ 避免的陷阱
1. **跳过验证步骤**: 会导致后续Phase出现连锁问题
2. **忽略代码质量检查**: 会积累技术债务
3. **不生成Phase汇总**: 会导致上下文丢失，重复工作
4. **过度设计**: 违反KISS原则，增加复杂性
5. **忽略安全验证**: 会留下安全隐患

### 🚀 最佳实践
1. **每个Phase完成后立即生成汇总文档**
2. **使用统一的验证命令集**
3. **保持详细的注释和文档**
4. **定期进行代码质量检查**
5. **建立完整的测试覆盖**

### 📊 Phase汇总系统效果验证
**实际数据** (基于Gil Kratos用户管理系统开发):
- **Token使用对比**: 
  - 不使用汇总: 每次需要读取20-30个文件，消耗15000-25000 tokens
  - 使用汇总: 只需读取3-5个汇总文件，消耗3000-5000 tokens
  - **节省率**: 60-80%

- **开发效率提升**:
  - 上下文理解时间: 从10-15分钟缩短到2-3分钟
  - 跨Phase依赖分析: 从查看多个文件到直接查看汇总
  - 问题定位速度: 提升3-5倍

### 🔧 常见问题修复指南
**编译问题**:
- `unreachable code`: 检查return语句后是否有多余代码
- `format string`: 检查fmt.Printf等格式化函数的参数匹配
- 包导入问题: 检查import路径，移除未使用的导入

**依赖注入问题**:
- Wire生成失败: 检查Provider函数签名，确保返回值类型正确
- 循环依赖: 重新设计依赖关系，使用接口解耦

**API测试问题**:
- 404错误: 检查路由注册和Controller绑定
- CORS错误: 检查CORS中间件配置
- 参数验证失败: 检查DTO结构体的validation标签

### 🎓 AI助手学习建议
1. **深入理解DDD分层架构**: 确保每层职责清晰
2. **熟练掌握Go语言特性**: 特别是接口、错误处理、并发
3. **掌握Kratos框架使用**: 了解依赖注入、中间件、配置管理
4. **建立质量意识**: 代码质量、测试覆盖、安全考虑
5. **持续优化工作流程**: 根据实际开发经验不断改进

---

## 🔄 工作流程版本历史

**v1.1** (当前版本) - 基于Gil Kratos用户管理系统开发经验优化
- ✅ 完善了Phase验证机制，添加了实际问题修复指导
- ✅ 优化了Phase汇总系统，提供了量化的效果数据
- ✅ 改进了AI指令模板，增加了详细的结构要求
- ✅ 补充了常见问题修复指南和最佳实践
- ✅ 添加了完整的AI助手开发经验总结

**v1.0** (初始版本) - 基础工作流程框架
- 定义了基本的Phase划分和开发顺序
- 建立了DDD分层架构的开发规范
- 提供了基础的AI交互指导

---

## 📋 后续改进计划

1. **持续优化Phase汇总模板**: 根据更多项目的开发经验完善汇总结构
2. **扩展验证机制**: 添加更多自动化验证工具和检查项
3. **完善错误处理指导**: 建立更完整的问题诊断和修复知识库
4. **优化AI交互方式**: 提高AI理解需求和生成代码的准确性
5. **建立质量度量体系**: 量化开发效率和代码质量的提升效果
AI 在执行下一个阶段或步骤前，将等待用户的明确确认。用户需要回复类似"请继续 Phase X"或具体的指令来授权 AI 进行下一步操作。
