# @repo/core 业务核心库

## 简介

`@repo/core` 是 monorepo 中的共享业务核心库，为学生端和教师端应用提供核心业务逻辑、数据模型、服务以及可重用的业务组件。它封装了复杂的教育场景功能，如课程播放、互动练习、学习引导和数据管理等，旨在提升开发效率和代码复用性。

## 特性

- **业务封装**: 提供高度封装的业务模块，如课程、练习、引导等。
- **状态管理**: 内置基于 Signals 和 React Context 的状态管理方案，简化复杂状态的维护。
- **数据驱动**: 定义了清晰的数据模型和类型，支持后端数据交互。
- **可重用组件**: 包含一系列与业务逻辑紧密相关的 React 组件。
- **Remotion 集成**: 支持使用 Remotion 创建和管理动态视频内容。
- **模块化设计**: 各业务模块职责清晰，易于维护和扩展。

## 安装与设置

本库作为 monorepo 的内部包使用，无需单独安装。具体模块的导出路径为 `@repo/core`。

引入模块示例：
```tsx
// 示例: 引入课程主组件
import { Course } from "@repo/core/course";

// 示例: 引入课程相关的 Hook (通常从其上下文模块导出)
import { useCourse } from "@repo/core/course"; // 假设 useCourse 从 @repo/core/course/contexts/course-context.tsx 导出并通过 @repo/core/course 暴露

// 示例: 引入类型定义
import { VideoWidgetData } from "@repo/core/types/course-data/widget-video";
```

## 模块分类

---

### 一、核心业务模块

#### 1. 课程系统 (`/src/course`)

##### 1.1. `Course` 组件
- **文件路径**: `src/course/index.tsx`
- **基本说明**: 课程的主入口和容器组件。它负责整合课程的各个部分，包括章节内容、小组件的渲染、导航逻辑以及全局课程状态的管理。
- **场景说明**: 当用户进入一个具体的课程学习界面时，`Course` 组件会被加载。它是整个在线课程体验的顶层封装。
- **输入信息 (Props)**:
    - `id (string)`: **必需**. 课程的唯一标识符，用于获取该课程的所有数据和配置。
- **输出信息 (渲染/行为)**:
    - 渲染完整的课程学习界面，包括当前章节的知识点或开场动画、导航栏、可能的播放器控制条等。
    - 内部会初始化并提供 `CourseContext`，使得子组件可以访问课程数据、控制播放、导航、保存进度等。

##### 1.2. `KnowledgePointSection` 组件
- **文件路径**: `src/course/sections/knowledge-point-section.tsx`
- **基本说明**: 代表课程中的一个“知识点”学习章节。它负责按顺序展示该知识点下定义的所有学习小组件 (Widgets)。
- **场景说明**: 在 `Course` 组件内部，当课程流程进行到某个具体的知识点时，此组件会被渲染以展示该知识点的学习内容。
- **输入信息 (Props)**:
    - `data (SectionData<"knowledgePoint">)`: **必需**. 包含该知识点章节的详细数据，如章节标题、以及一个由多个 `WidgetData` 组成的小组件列表。
- **输出信息 (渲染)**:
    - 渲染知识点章节的结构，并根据 `data.widgets` 列表，依次渲染对应的 `VideoWidget`, `ExerciseWidget`, `BanshuWidget` 等小组件。

##### 1.3. `OpeningSection` 组件
- **文件路径**: `src/course/sections/opening-section.tsx`
- **基本说明**: 代表课程的“开场”部分。通常包含一个 `TransitionWidget` 用于展示课程标题、简介或引导动画。
- **场景说明**: 在整个课程开始时最先展示，用于吸引学生注意力、介绍课程概览或进行情感带入。
- **输入信息 (Props)**:
    - `data (SectionData<"opening">)`: **必需**. 包含开场章节的数据，其 `content.widget` 通常是一个 `transition` 类型的 `WidgetData`。
- **输出信息 (渲染)**:
    - 渲染开场动画或引导内容，通常是一个全屏的过渡效果。

---
#### 2. 小组件系统 (`/src/course/widgets`)

小组件是构成课程内容的原子单元，每个小组件负责一种特定的学习交互或内容展示。

##### 2.1. `VideoWidget`
- **文件路径**: `src/course/widgets/video/index.tsx`
- **基本说明**: 视频播放组件，核心功能是加载和播放教学视频。集成了 Remotion 播放器，可以支持复杂的动态视频内容，如数字人讲解、同步字幕、动态白板演示等。
- **场景说明**: 当课程内容需要通过视频形式传达时使用，例如概念讲解、案例分析、操作演示等。
- **输入信息 (Props)**:
    - `data (WidgetData<"video">)`: **必需**. 包含视频的详细配置数据 (`VideoWidgetData`)。关键属性包括：
        - `avatar (Avatar)`: 数字人视频的URL、尺寸、帧率、总帧数。
        - `subtitles (Subtitle[])`: 字幕数据数组，每条字幕包含文本及起止帧。
        - `whiteboards (Whiteboard[])`: 同步白板数据数组，定义了在视频特定帧显示的文本、图片或小视频。
        - `qa (object)`: 可选，答疑入口配置，如触发答疑按钮的关键帧范围。
    - `id (string)`: (通过 `WidgetData` 传入) 小组件的唯一ID。
- **输出信息 (渲染/行为)**:
    - 渲染一个集成的视频播放界面。
    - 使用 Remotion 的 `Player` 来控制视频播放、暂停、进度条、全屏等。
    - 根据时间轴同步显示数字人、字幕和白板内容。
    - 若配置了 `qa`，则在特定时间点显示答疑入口。
    - 与 `CourseContext` 交互，上报播放进度、响应全局播放控制。

##### 2.2. `BanshuWidget`
- **文件路径**: `src/course/widgets/banshu/banshu-widget.tsx`
- **基本说明**: 板书展示组件，用于模拟教师在黑板上书写和讲解的过程。它使用 Remotion 实现动态的、逐步展示的板书效果，可以包含文本、数学公式、图片和手绘风格标记。
- **场景说明**: 适用于需要详细步骤演示、公式推导、概念梳理、图文并茂讲解知识点的教学场景。
- **输入信息 (Props)**:
    - `data (WidgetData<"banshu">)`: **必需**. 包含板书的详细配置数据 (`BanshuWidgetData`)。关键属性包括：
        - `avatar (Avatar)`: 可选，同步播放的数字人视频。
        - `subtitles (Subtitle[])`: 可选，同步字幕。
        - `title (string)`: 板书的标题。
        - `whiteboards (ParagraphLine[])`: 板书的核心内容，一个由 `ParagraphLine` 对象组成的数组，每行定义了其类型（文本、列表、标题）、内容（`ParagraphTexture`数组，支持分段样式和标记）、出现帧等。
- **输出信息 (渲染/行为)**:
    - 渲染一个动态板书播放界面。
    - 板书内容根据 `whiteboards` 数据中的帧信息逐步、动态地在屏幕上“书写”出来。
    - 支持通过 `ModeSwitcher` 切换“跟随模式”（自动按帧播放）和“自由模式”（用户手动滚动查看）。
    - 提供 `ProgressControl` 来控制播放进度。
    - 可集成 `RoughNotion` 实现手绘标记效果。
    - 与 `BanshuContext` 和 `CourseContext` 交互。

##### 2.3. `BanshuVideoWidget`
- **文件路径**: `src/course/widgets/banshu-video/banshu-video-widget.tsx`
- **基本说明**: 板书录播视频组件，用于播放预先录制好的、包含完整板书讲解过程的视频文件。
- **场景说明**: 当动态板书制作成本较高，或已有现成的优质板书讲解视频时，可作为 `BanshuWidget` 的替代方案。
- **输入信息 (Props)**:
    - `data (WidgetData<"banshu-video">)`: **必需**. 包含板书视频的配置数据 (`BanshuVideoWidgetData`)。关键属性：
        - `title (string)`: 视频标题。
        - `url (string)`: 视频文件的URL。
        - `durationInFrames (number)`: 视频总帧数。
        - `fps (number)`: 视频帧率。
- **输出信息 (渲染/行为)**:
    - 渲染一个 Remotion 视频播放器，加载并播放指定的板书视频。
    - 提供标准的视频播放控制（播放、暂停、进度、音量等）。

##### 2.4. `ExerciseWidget`
- **文件路径**: `src/course/widgets/exercise/exercise-widget.tsx`
- **基本说明**: 互动练习题组件。它支持展示多种题型（目前主要为单选/多选），允许用户作答，并能提供即时反馈、答案解析。
- **场景说明**: 用于课程中知识点学习之后的巩固练习、自我检测环节，或者作为阶段性的测验。
- **输入信息 (Props)**:
    - `data (WidgetData<"exercise">)`: **必需**. 包含练习题的配置数据 (`ExerciseWidgetContent`)。关键属性：
        - `title (string)`: 可选，整个练习部分的标题。
        - `questions (QuestionData[])`: **必需**. 一个或多个问题对象组成的数组。每个 `QuestionData` 包含：
            - `id (string)`: 问题ID。
            - `type (QuestionType)`: 题目类型 (e.g., `SINGLE_CHOICE`)。
            - `topic ({ text: string; image?: string })`: 题干信息，包含文本和可选图片。
            - `options (QuestionOption[])`: 选项列表。
            - `answer (string[])`: 正确答案的标识符数组。
            - `analysis (string)`: 题目解析文本。
            - `point (string)`: 关联的知识点。
            - `qa (string[])`: 可选，与此题相关的“猜你想问”列表。
- **输出信息 (渲染/行为)**:
    - 渲染练习题界面，逐题显示。包括：
        - `QuestionTopic`: 显示题干。
        - `QuestionOptions`: 显示选项供用户选择。
        - `Timer`: (可选) 计时器。
    - 用户选择答案并提交后，会判断对错。
    - `Popup`: 弹出反馈提示（正确、错误、提示）。
    - 显示答案解析和关联知识点。
    - 提供“下一题”或“完成练习”的导航。
    - 与 `ExerciseContext` 和 `CourseContext` 交互，管理答题状态和上报进度。

##### 2.5. `ChatWidget`
- **文件路径**: `src/course/widgets/chat/chat.tsx` (导出默认组件 `Chat`)
- **基本说明**: AI 辅助答疑组件。提供一个聊天界面，允许学生在学习过程中就当前内容向 AI 助教提问，并获得解答。
- **场景说明**: 当学生在观看视频、阅读板书或做练习遇到疑问时，可以主动唤出此组件进行提问，以获得即时帮助。
- **输入信息 (Props)**:
    - `isOpen (boolean)`: 由 `CourseContext` 控制，决定聊天面板是否显示。
    - `setIsOpen ((isOpen: boolean) => void)`: 由 `CourseContext` 控制，用于改变聊天面板显示状态。
    - (内部通过 `useCourse` Hook 获取课程ID、当前知识点ID、小组件ID等上下文信息，用于与后端 AI 服务通信。)
- **输出信息 (渲染/行为)**:
    - 渲染一个聊天窗口/面板。包含：
        - `MessageList`: 显示用户与 AI 的对话历史。
        - `SendMessage`: 供用户输入文本或语音提问。
        - `GuessQA`: (可选) 显示与当前内容相关的推荐问题。
    - 用户发送消息后，调用后端 AI 服务获取回答，并将回答追加到 `MessageList`。
    - 支持消息的打字效果和语音播放（如果回复包含音频）。

##### 2.6. `TransitionWidget`
- **文件路径**: `src/course/widgets/transition/transition-widget.tsx`
- **基本说明**: 场景过渡动画组件。用于在不同学习单元、章节之间或课程的开始/结束时，提供一个平滑且富有视觉吸引力的过渡效果。
- **场景说明**: 常用于课程开场（展示课程标题和简介）、知识点章节开始前（预告章节主题）、或课程总结等。
- **输入信息 (Props)**:
    - `data (WidgetData<"transition">)`: **必需**. 包含过渡动画内容的配置数据 (`TransitionWidgetContent`)。关键属性：
        - `title (string)`: 可选，过渡动画的主标题。
        - `description (string)`: 可选，过渡动画的描述文本。
        - `speech (string)`: **必需**. 核心的旁白文本，会使用 `TypewriterText` 效果展示。
- **输出信息 (渲染/行为)**:
    - 渲染一个全屏或大区域的动态过渡效果。
    - 使用 `TypewriterText` 组件将 `speech` 文本以打字机效果逐字显示。
    - 通常伴有背景图片/动画和音效（通过 Remotion 实现）。
    - 动画播放完毕后，通过 `useCourse().nextWidget()` 自动进入下一个学习单元。

---
#### 3. 学习引导系统 (`/src/guide`)

##### 3.1. `GuideView` 组件
- **文件路径**: `src/guide/guide-view.tsx`
- **基本说明**: 学习引导或交互式教程的视图主组件。它结合 Remotion 播放器，用于展示分步骤的引导内容，可以包含数字人讲解、同步字幕和动态高亮操作区域等。
- **场景说明**: 用于创建产品功能介绍、操作流程演示、特定任务的步骤式引导等交互式教程内容。
- **输入信息 (Props)**:
    - `data (GuideWidgetData)`: **必需**. 包含整个引导内容的详细数据。关键属性：
        - `avatar (Video)`: 数字人讲解视频的配置。
        - `subtitles (Subtitle[])`: 同步字幕数据。
        - `title (string)`: 引导教程的标题。
        - `header (Line)`: 可选，引导头部的固定内容。
        - `content (Line[])`: **必需**. 引导的核心步骤内容，一个由 `Line` 对象组成的数组，每行定义了其类型（文本、列表、标题、图片等）、内容和出现的帧范围。
        - `footer (Line)`: 可选，引导底部的固定内容。
- **输出信息 (渲染/行为)**:
    - 渲染一个集成的引导播放界面。
    - 使用 Remotion `Player` 控制引导内容的逐步播放。
    - `GuideCore` 组件负责根据 `data.content` 和当前播放帧渲染对应的引导步骤文本、图片、高亮等。
    - 同步播放数字人视频和字幕。
    - 提供播放控制（若 Remotion Player 控件可见）。
    - 内部会建立 `GuideViewProvider` 上下文。

##### 3.2. `GuideCore` 组件
- **文件路径**: `src/guide/components/guide-core.tsx`
- **基本说明**: 引导内容的核心渲染逻辑组件。它消费 `GuideViewContext` 中的数据，并根据 Remotion 的当前播放帧，动态渲染 `GuideWidgetData` 中定义的引导步骤。
- **场景说明**: 作为 `GuideView` 的内部核心组件，不直接单独使用。它负责将引导数据（`Line[]`）转换为实际的、随时间变化的视觉元素。
- **输入信息**:
    - 无直接 Props，通过 `useGuideViewContext()` Hook 从 `GuideViewProvider` 获取 `GuideWidgetData`。
    - 依赖 Remotion 的 `useCurrentFrame()` Hook 获取当前帧。
- **输出信息 (渲染)**:
    - 根据当前帧，渲染 `GuideWidgetData.content` 中在该帧范围内应该显示的引导行 (`GuideLine` 组件)。
    - `GuideLine` 内部会进一步处理文本、图片、列表、标题等元素的显示，并应用 `RoughNotion` 标记（如果配置）。

---

### 二、UI 组件 (业务相关)

这些组件通常与核心业务模块紧密集成，或提供业务场景下的特定UI功能。

#### 1. Notion 风格标记 (`/src/components/notion`)

##### 1.1. `RoughNotion` 组件
- **文件路径**: `src/components/notion/rough-notion.tsx`
- **基本说明**: 一个 React 组件，用于在其子内容上应用类似 Notion 应用中的手绘风格动画标记，如高亮、下划线、删除线、方框、圆圈、括号等。
- **场景说明**: 在需要突出显示文本、模拟手写笔记或增加视觉趣味性的地方使用。常见于板书 (`BanshuWidget`) 和引导 (`GuideView`) 内容中，用于动态标记关键词或重要段落。
- **输入信息 (Props) (`RoughNotionProps`)**:
    - `type (NotionTypes)`: **必需**. 标记的类型，例如 `"underline"`, `"box"`, `"circle"`, `"highlight"`, `"strike-through"`, `"crossed-off"`, `"bracket"`。
    - `show (boolean)`: 控制标记是否显示（并播放动画，如果 `animate` 为 true）。默认为 `false`。
    - `children (React.ReactNode)`: **必需**. 需要被标记的 React 子节点。
    - `animate (boolean)`: 是否开启动画效果。默认为 `true`。
    - `animationDelay (number)`: 动画开始前的延迟时间（毫秒）。默认为 `0`。
    - `animationDuration (number)`: 动画的持续时间（毫秒）。默认为 `800`。
    - `color (string)`: 标记的颜色。默认为当前文本颜色。
    - `strokeWidth (number)`: 标记线条的宽度。默认为 `1`。
    - `iterations (number)`: 动画的迭代次数（对于某些类型的动画，如手绘效果的抖动）。默认为 `2`。
    - `padding (number | [number, number] | [number, number, number, number])`: 标记与内容之间的内边距。默认为 `5`。
    - `brackets ("left" | "right" | "top" | "bottom" | ("left" | "right" | "top" | "bottom")[])`: 当 `type` 为 `"bracket"` 时，指定括号的方向或一组方向。
    - `customElement (string)`: 包裹 `children` 的HTML元素标签名。默认为 `"span"`。
    - `getAnnotationObject ((annotation: Annotation) => void)`: 可选，获取内部 `rough-notation` 库的 annotation 对象实例的回调。
- **输出信息 (渲染)**:
    - 渲染其 `children` 内容。
    - 当 `show` 为 `true` 时，根据指定的 `type` 和其他样式属性，在 `children` 周围或上方绘制动态的手绘风格标记。

---
#### 2. 课程通用组件 (`/src/course/common`)

##### 2.1. `BorderButton` 组件
- **文件路径**: `src/course/common/border-button.tsx`
- **基本说明**: 一个带有自定义边框和背景色的按钮组件。
- **场景说明**: 用于课程界面中需要视觉突出或具有特定品牌风格的按钮，例如“开始学习”、“进入下一章”等行动号召按钮。
- **输入信息 (Props)**:
    - `children (React.ReactNode)`: **必需**. 按钮内显示的文本或图标。
    - `bgColor (string)`: 可选，按钮的背景颜色。
    - `className (string)`: 可选，应用于按钮的额外 Tailwind CSS 类名。
    - `onClick (() => void)`: 可选，按钮点击事件的回调函数。
    - `href (string)`: 可选，如果按钮用作链接，则提供此URL。
- **输出信息 (渲染)**:
    - 渲染一个可点击的按钮或链接。其样式特点是具有可配置的背景色和通常较为明显的边框。

##### 2.2. `KetexMathContent` 组件
- **文件路径**: `src/course/common/ketex-math-content.tsx`
- **基本说明**: 用于渲染包含 LaTeX 数学公式的文本内容。它能自动识别文本中的数学公式（通常由 `$$...$$` 或 `\\(...\\)` 包裹），并使用 KaTeX库将其渲染为美观的数学排版。
- **场景说明**: 在需要展示数学公式的任何地方，如练习题的题干、板书内容、课程讲义等。
- **输入信息 (Props)**:
    - `children (string)`: **必需**. 包含普通文本和 LaTeX 数学公式的字符串。
    - `options (katex.KatexOptions)`: 可选，传递给 KaTeX 的配置选项，例如 `displayMode`, `throwOnError` 等。
- **输出信息 (渲染)**:
    - 渲染处理后的文本内容。文本中的 LaTeX 公式部分会被 KaTeX 引擎转换为 HTML 和 CSS，以正确的数学排版样式显示。普通文本部分则按原样显示。

##### 2.3. `MessageList` 组件
- **文件路径**: `src/course/common/message-list.tsx`
- **基本说明**: 用于展示一个对话消息列表，通常用于聊天或问答界面。能够区分不同发送者（如用户、AI角色）的消息，并支持打字动画效果。
- **场景说明**: 主要在 `ChatWidget` 中使用，用于显示用户与 AI 助教之间的对话历史记录。
- **输入信息 (Props) (`messageListProps`)**:
    - `conversations (Conversation[])`: **必需**. 一个对话消息对象数组。每个 `Conversation` 对象通常包含：
        - `type ("user" | "dolli" | "bron")`: 消息发送者类型。
        - `message (string)`: 消息文本内容。
        - `time (string)`: 消息发送时间。
        - `isTyping (boolean)`: 可选，指示此消息是否正在“输入中”（用于AI回复的打字效果）。
        - `isComplete (boolean)`: 可选，指示打字效果是否完成。
    - `setMessageList ((list: Conversation[]) => void)`: **必需**. 更新消息列表状态的回调函数（例如，在打字动画完成后更新消息状态）。
- **输出信息 (渲染)**:
    - 渲染一个垂直排列的消息列表。
    - 每条消息根据其 `type` 会有不同的样式（例如，用户消息居右，AI消息居左，并配有相应的 `RoleAvatar`）。
    - 如果消息的 `isTyping` 为 true 且 `isComplete` 为 false，则会使用 `TypedMessage` 组件展示打字动画效果。

##### 2.4. `RoleAvatar` 组件
- **文件路径**: `src/course/common/role-avatar.tsx`
- **基本说明**: 显示特定角色（如 AI 助教 "Dolli" 或 "Bron"）的头像图片。
- **场景说明**: 在聊天界面 (`MessageList`) 中，用于标识 AI 角色的发言。
- **输入信息 (Props)**:
    - `type ("dolli" | "bron")`: **必需**. 要显示的头像角色类型。
- **输出信息 (渲染)**:
    - 渲染一个对应角色的图片头像。图片资源通常来自 `/src/assets/icons/`。

##### 2.5. `SendMessage` 组件
- **文件路径**: `src/course/common/send-message.tsx`
- **基本说明**: 一个消息输入组件，允许用户输入文本或通过麦克风进行语音输入，并发送消息。
- **场景说明**: 主要在 `ChatWidget` 中作为用户的提问输入区域。
- **输入信息 (Props)**:
    - `initialMessage (string)`: 可选，文本输入框的初始内容。
    - `initialInputStatus (InputStatus: "voice" | "text")`: 可选，初始的输入模式，默认为 "text"。
    - `onSendMessage ((message: string, inputStatus: InputStatus) => void)`: **必需**. 用户点击发送或语音识别完成后触发的回调，参数为消息内容和输入方式。
    - `emit ((event: string, data: object) => void)`: 可选，用于触发如 Socket.IO 事件（在当前代码中似乎未直接使用，但保留了接口）。
- **输出信息 (渲染/行为)**:
    - 根据当前 `inputStatus` 渲染文本输入框或语音输入界面。
    - **文本模式**: 显示一个文本域和发送按钮。
    - **语音模式**: 显示录音开始/停止按钮、语音识别结果预览、以及切换回文本模式的按钮。
    - 用户输入文本或语音识别出文本后，点击发送按钮将触发 `onSendMessage` 回调。

##### 2.6. `TypeMessage` 组件 (实际应为 `TypedMessage`)
- **文件路径**: `src/course/common/type-message.tsx`
- **基本说明**: 实现文本打字机效果的组件。它会逐字显示传入的文本字符串，模拟打字的过程。支持渲染文本中嵌套的 KaTeX 数学公式。
- **场景说明**: 用于 AI 回复、课程旁白、引导文本等，使文本显示更生动，吸引用户注意力。在 `MessageList` 中用于 AI 的回复消息。
- **输入信息 (Props)**:
    - `message (string)`: **必需**. 需要以打字效果显示的完整文本字符串。
    - `onComplete (() => void)`: 可选，打字效果完成时的回调函数。
    - `mathOptions (object)`: 可选，传递给 KaTeX 渲染数学公式的配置。
- **输出信息 (渲染/行为)**:
    - 逐步将 `message` 文本渲染到屏幕上，形成打字动画。
    - 如果文本中包含 LaTeX 数学公式，会正确调用 KaTeX 进行渲染。
    - 动画完成后，调用 `onComplete` 回调。

---
#### 3. 播放器控制组件 (`/src/course/widgets/player-control/`)

这组组件通常共同构成视频或 Remotion 播放器的控制条。

##### 3.1. `PlayControl` 组件 (实际组件名 `PlayControl` 内部实现逻辑, 导出为 `PlayControl`)
- **文件路径**: `src/course/widgets/player-control/player-control.tsx`
- **基本说明**: 提供播放和暂停控制按钮。
- **场景说明**: 用于课程中的视频或 Remotion 动态内容播放器，让用户可以控制播放状态。
- **输入信息 (Props) (`PlayerControlsProps`)**:
    - `playerRef (React.RefObject<PlayerRef>)`: **必需**. Remotion 播放器实例的引用，用于控制播放和获取状态。
- **输出信息 (渲染/行为)**:
    - 渲染一个播放/暂停图标按钮。
    - 根据 `playerRef` 的当前播放状态（通过 `usePlayerState` Hook 获取）显示对应的图标。
    - 点击按钮时，调用 `playerRef.current?.play()` 或 `playerRef.current?.pause()` 来切换播放状态。

##### 3.2. `SpeedControl` 组件
- **文件路径**: `src/course/widgets/player-control/speed-control.tsx`
- **基本说明**: 允许用户选择视频或 Remotion 内容的播放倍速。
- **场景说明**: 用于需要提供不同播放速率选项的播放器，以适应不同用户的学习习惯。
- **输入信息 (Props) (`PlayerControlsProps`)**:
    - `playerRef (React.RefObject<PlayerRef>)`: **必需**. Remotion 播放器实例的引用。
    - `onChange ((rate: number) => void)`: 可选，当倍速改变时触发的回调函数。 (在当前实现中，也通过 `useCourse` 更新全局倍速)
- **输出信息 (渲染/行为)**:
    - 渲染一个显示当前播放倍速的按钮 (例如 "1.0x")。
    - 点击该按钮会弹出一个包含预设倍速选项 (如 0.75x, 1.0x, 1.25x, 1.5x, 2.0x) 的列表或菜单。
    - 用户选择新的倍速后，会更新播放器的 `playbackRate` 并可能调用 `onChange` 回调和更新 `CourseContext` 中的 `kpSpeed`。

##### 3.3. `SubtitleControl` 组件
- **文件路径**: `src/course/widgets/player-control/subtitle-control.tsx`
- **基本说明**: 提供字幕的开关控制。
- **场景说明**: 用于带字幕的视频或 Remotion 内容播放器，允许用户显示或隐藏字幕。
- **输入信息**:
    - 无直接 Props。它通过 `useCourse()` Hook 访问和修改全局的 `subtitleVisible` 状态。
- **输出信息 (渲染/行为)**:
    - 渲染一个字幕图标按钮。
    - 根据 `subtitleVisible` 状态，按钮可能呈现不同激活样式。
    - 点击按钮会切换 `subtitleVisible` 的布尔值，从而控制字幕的显示与隐藏。

##### 3.4. `TimeDisplay` 组件
- **文件路径**: `src/course/widgets/player-control/time-player.tsx` (导出 `TimeDisplay`)
- **基本说明**: 显示播放器当前的播放时间和内容总时长。
- **场景说明**: 用于视频或 Remotion 播放器，帮助用户了解当前的播放进度和内容的整体长度。
- **输入信息 (Props)**:
    - `durationInFrames (number)`: **必需**. 内容的总帧数。
    - `fps (number)`: **必需**. 内容的帧率 (frames per second)。
    - `playerRef (React.RefObject<PlayerRef | null>)`: **必需**. Remotion 播放器实例的引用，用于获取当前帧。
- **输出信息 (渲染)**:
    - 渲染格式化的时间文本，如 "MM:SS / MM:SS" (例如 "00:35 / 02:10")。
    - 当前时间根据 `playerRef.current?.getCurrentFrame()` 动态更新。
    - 总时长根据 `durationInFrames` 和 `fps` 计算。

##### 3.5. `WidgetsProgress` 组件 (实际组件名 `ProgressBar`)
- **文件路径**: `src/course/widgets/player-control/widgets-progress.tsx` (导出 `ProgressBar`)
- **基本说明**: 显示课程中各个小组件 (Widgets) 的进度概览，并允许用户点击跳转到特定小组件。
- **场景说明**: 作为播放器控制条的一部分或课程导航的一部分，提供一个可视化的课程结构和进度指示器。
- **输入信息 (Props) (`CourseProgressProps`)**:
    - `onChapterClick ((index?: number) => void)`: 可选，当用户点击某个小组件节点时的回调函数，参数是该小组件在当前章节内的索引。通常用于触发跳转逻辑。
    - `resetTimer (() => void)`: 可选，当发生跳转或其他交互时，可能需要重置外部计时器的回调。
- **输出信息 (渲染/行为)**:
    - 渲染一个水平的进度条，上面排列着代表课程各个小组件的节点。
    - 节点会根据其状态（已完成、已解锁、未解锁）显示不同样式。
    - 当前正在播放的小组件节点会被高亮显示。
    - 用户可以点击节点，触发 `onChapterClick` 回调以跳转到对应的小组件。
    - 内部通过 `useCourse()` Hook 获取课程结构 (`courseSections`)、当前进度 (`currentWidgetIndex`, `currentSectionIndex`) 和播放器引用。

---

### 三、Hooks (业务逻辑钩子)

这些 Hooks 封装了与特定业务模块相关的状态管理和逻辑。

#### 1. `useCourse`
- **导出路径**: `src/course/contexts/course-context.tsx` (通过 `CourseProvider` 提供)
- **基本说明**: 一个 React Hook，用于访问和操作当前课程的全局状态和上下文信息。它必须在 `CourseProvider` 组件（通常由 `Course` 组件包裹）的子树中使用。
- **场景说明**: 在课程相关的任何子组件中，当需要获取课程数据（如章节列表、当前进度）、控制课程行为（如跳转章节、保存进度）或共享状态（如聊天窗口是否打开、播放器引用、板书模式）时使用。
- **输入信息**: 无直接参数 (依赖 React Context API 从上层 `CourseProvider` 获取值)。
- **输出信息 (返回 `CourseContextType` 对象)**:
    - `id (string)`: 当前课程的 ID。
    - `title (string)`: 课程标题。
    - `courseSections (SectionData[])`: 课程所有章节的数据。
    - `currentSectionIndex (number)`: 当前激活章节的索引。
    - `currentWidgetIndex (number)`: 当前激活小组件在当前章节中的索引。
    - `goto (sectionIndex: number, widgetIndex?: number) => void`: 跳转到指定章节和（可选的）小组件的方法。
    - `nextSection () => void`: 跳转到课程的下一个章节。
    - `nextWidget () => void`: 跳转到当前章节的下一个小组件。
    - `saveProgress <T extends WidgetType>(data: WidgetProgress<T>) => void`: 保存指定小组件的进度到本地存储。
    - `reportProgress (widgetId: string, type: WidgetType, data: object, status?: WidgetStatus) => Promise<void>`: 上报学习进度到服务器（通常是异步操作）。
    - `lastProgress (CourseProgressData | null)`: 上次从本地存储加载的课程整体进度。
    - `videoPlayerRef (RefObject<PlayerRef | null>)`: 全局 Remotion 播放器实例的引用。
    - `isOpenChat (boolean)`: 聊天窗口是否打开的状态。
    - `setIsOpenChat ((isOpen: boolean) => void)`: 设置聊天窗口打开状态的方法。
    - `openChat (type: "video" | "exercise", qa?: string) => void`: 打开聊天窗口，并指定关联的组件类型和问题。
    - `closeChat () => void`: 关闭聊天窗口。
    - `toggleChat () => void`: 切换聊天窗口的显示状态。
    - `currentComponentType ("video" | "exercise")`: 当前与聊天关联的组件类型。
    - `currentQA (string | undefined)`: 当前聊天窗口预设的提问。
    - `controlsVisible (boolean)`: 播放器控制条是否可见的状态。
    - `setControlsVisible ((visible: boolean) => void)`: 设置播放器控制条可见性的方法。
    - `playerControlVisible (boolean)`: (似乎与 `controlsVisible` 功能相似或为其一部分) 播放器控制是否可见。
    - `togglePlayerControlVisible ((visible?: boolean) => void)`: 切换播放器控制的可见性。
    - `subtitleVisible (boolean)`: 字幕是否可见的状态。
    - `toggleSubtitle (() => void)`: 切换字幕可见性的方法。
    - `banshuMode (BanshuMode)`: 当前板书模式 ("follow" 或 "free")。
    - `setBanshuMode ((mode: BanshuMode) => void)`: 设置板书模式的方法。
    - `kpSpeed (number)`: 知识点（通常指视频/板书）的播放倍速。
    - `setKpSpeed ((speed: number) => void)`: 设置播放倍速的方法。

#### 2. `useExerciseContext`
- **导出路径**: `src/course/contexts/exercise-context.tsx` (通过 `ExerciseContextProvider` 提供)
- **基本说明**: 一个 React Hook，用于管理当前练习题模块的状态和交互逻辑。它必须在 `ExerciseContextProvider` 组件（通常由 `ExerciseWidget` 包裹）的子树中使用。
- **场景说明**: 在 `ExerciseWidget` 及其子组件（如 `Question`, `QuestionOptions`, `Popup`）中使用，用于获取当前题目数据、处理用户答案选择、答案判断、切换题目、显示答题反馈弹窗等。
- **输入信息**: 无直接参数。
- **输出信息 (返回 `ExerciseContextType` 对象)**:
    - `id (string)`: 当前练习小组件的 ID。
    - `questions (QuestionData[])`: 当前练习的所有题目数据列表。
    - `currentQuestionIndex (number)`: 当前显示题目的索引（从0开始）。
    - `nextQuestion () => void`: 导航到下一题。如果已是最后一题，可能会触发练习结束的逻辑。
    - `correctCount (number)`: 用户在本练习中已答对的题目数量。
    - `setCorrectCount ((count: number) => void)`: （通常内部调用）设置答对题目数量。
    - `sumCorrectCount () => void`: 答对题目数加一。
    - `isCorrect (option: QuestionOption, answer: string[]) => boolean`: 根据用户选择的选项和正确答案，判断用户是否答对。
    - `progress (ExerciseProgress | undefined)`: 当前练习的进度数据，包含用户对当前题目的作答等。
    - `saveUserAnswer (answer: QuestionOption, costTime: number) => void`: 保存用户对当前题目的答案和所用时间到练习进度中。
    - `popupOpen (boolean)`: 答题反馈弹窗（正确/错误/提示）是否显示的状态。
    - `popupType (PopupType)`: 当前显示的弹窗类型 (`PopupType.Correct`, `PopupType.Wrong`, `PopupType.Hint`, `PopupType.None`)。
    - `showPopup ((type: PopupType) => void)`: 显示指定类型的反馈弹窗。
    - `hidePopup (() => void)`: 关闭反馈弹窗。

#### 3. `useBanshu`
- **导出路径**: `src/course/contexts/banshu-context.tsx` (通过 `BanshuProvider` 提供)
- **基本说明**: 一个 React Hook，用于管理板书 (`BanshuWidget`) 的播放、交互状态和数据。它必须在 `BanshuProvider` 组件（通常由 `BanshuWidget` 包裹）的子树中使用。
- **场景说明**: 在 `BanshuWidget` 及其内部组件（如 `BanshuInner`, `ProgressControl`, `ModeSwitcher`）中使用，以获取板书数据、控制板书播放进度（跳转到特定帧）、响应用户在自由模式下的滚动、管理当前选中的板书行等。
- **输入信息**: 无直接参数。
- **输出信息 (返回 `BanshuContextType` 对象)**:
    - `id (string)`: 当前板书小组件的 ID。
    - `banshuData (BanshuWidgetData | null)`: 当前板书的详细数据。
    - `refView (RefObject<HTMLDivElement | null>)`: 对板书内容滚动容器 DOM 元素的引用，用于自由模式下的滚动定位。
    - `selectedLineId (string)`: 在自由模式下，当前用户交互（如点击）选中的板书行的 ID。
    - `setSelectedLineId ((id: string) => void)`: 设置当前选中的板书行 ID。
    - `goto ((frame: number) => void)`: 控制 Remotion 播放器跳转到板书的指定帧。
    - `progress (RefObject<WidgetProgress<"banshu"> | null>)`: 对当前板书进度数据对象的引用。
    - `next () => void`: (可能用于触发板书按步骤或动画序列的下一个动作，具体行为需看实现)。

#### 4. `useCaptcha`
- **文件路径**: `src/hooks/use-captcha.tsx`
- **基本说明**: 封装阿里云滑动验证码功能的 React Hook。它负责加载验证码脚本、初始化验证码实例、显示验证码弹窗，并在用户完成验证后执行回调。
- **场景说明**: 在需要进行用户行为验证以防止机器人操作的场景下使用，例如登录、注册、发送重要请求前。
- **输入信息 (`CaptchaProps<T>`)**:
    - `captchaVerifyCallback ((captchaVerifyParam: string) => Promise<{ captchaResult: boolean; bizResult?: T }>)`: **必需**. 当用户成功通过滑动验证后，此回调被触发。参数 `captchaVerifyParam` 是阿里云验证码服务返回的验证参数，业务方需将其发送到自己的后端服务进行二次校验。后端校验成功后，应返回 `{ captchaResult: true, bizResult: <业务处理结果> }`。
    - `onBizResultCallback ((bizResult?: T) => void)`: **必需**. 在 `captchaVerifyCallback` 成功执行（即后端确认验证通过并返回业务结果）后，此回调被触发，参数为 `captchaVerifyCallback` 返回的 `bizResult`。用于执行后续的业务逻辑。
    - `getInstance ((instance: unknown) => void)`: 可选，获取阿里云验证码实例对象的回调。
- **输出信息 (返回一个对象)**:
    - `showCaptcha (): void`: 调用此方法来主动显示滑动验证码弹窗。
    - `CaptchaHolder ({ children: React.ReactNode })`: 一个必须在页面中渲染的 React 组件。此组件用于承载阿里云验证码的UI（通常是一个隐藏的div，验证码弹窗会挂载到它上面）。

#### 5. `useSpeed`
- **文件路径**: `src/hooks/use-speed.ts`
- **基本说明**: 一个 React Hook，用于方便地管理和控制 Remotion 播放器的播放倍速。
- **场景说明**: 用于任何集成了 Remotion 播放器并希望提供播放倍速调整功能的组件，例如 `VideoWidget` 或 `BanshuWidget` 的控制条。
- **输入信息 (`UseSpeedOptions`)**:
    - `playerRef (React.RefObject<PlayerRef | null>)`: **必需**. Remotion 播放器实例的引用。
    - `defaultRate (number)`: 可选，初始播放倍速。默认为 `1.0`。
    - `steps (number[])`: 可选，预设的倍速档位数组。默认为 `[0.75, 1.0, 1.25, 1.5, 2.0]`。
    - `onChange ((rate: number) => void)`: 可选，当播放倍速成功改变时触发的回调函数。
- **输出信息 (返回一个对象)**:
    - `currentRate (number)`: 当前播放器的实际播放倍速。
    - `setRate ((rate: number) => void)`: 一个函数，用于直接设置播放器的播放倍速到指定值。
    - `currentRateLabel (string)`: 当前播放倍速的格式化文本标签（例如 `"1.0x"`, `"1.5x"`）。
    - `nextRate (): void`: 一个函数，调用它会将播放倍速切换到 `steps` 数组中的下一个档位（循环切换）。

#### 6. `useTypewriter`
- **文件路径**: `src/hooks/use-typewriter.ts` (当前文件内容为空，假设这是一个待实现的或已废弃的Hook，若要实现，其描述如下)
- **基本说明**: (假设) 一个 React Hook，用于实现文本的打字机动画效果。接收一个目标文本字符串，并逐步在状态中更新显示的文本。
- **场景说明**: (假设) 当需要在 UI 中动态地、逐字地展示一段文本时，例如 AI 助手的回复、引导提示、标题动画等。`TypeMessage` 组件是其具体应用。
- **输入信息 (`TypewriterOptions`)**:
    - `textToType (string)`: **必需**. 完整的待显示文本。
    - `speed (number)`: 可选，打字速度（例如每秒字符数或每个字符的间隔毫秒数）。
    - `onComplete (() => void)`: 可选，当所有文本都显示完毕时的回调函数。
- **输出信息 (返回一个对象或字符串)**:
    - `displayedText (string)`: 当前已“打出”的文本部分，可以绑定到 UI 上显示。
    - `isTyping (boolean)`: 指示打字动画是否仍在进行中。
    - `start (): void`: (可能) 开始打字动画的方法。
    - `reset (): void`: (可能) 重置动画到初始状态的方法。

---
### 四、服务与配置

#### 1. 服务 (`/src/services`)

##### 1.1. `LocalCourseProgress` 类
- **文件路径**: `src/services/local-progress.ts`
- **基本说明**: 一个用于管理课程学习进度在浏览器本地存储（localStorage）的工具类。它提供了加载、保存、更新和清除特定课程进度的功能。
- **场景说明**: 在 `CourseProvider` 中使用，以实现课程进度的本地持久化。当用户在同一设备上再次打开课程时，可以恢复到上次的学习位置。
- **输入信息**:
    - **构造函数 `constructor(courseId: string)`**:
        - `courseId (string)`: **必需**. 课程的唯一标识符。实例化时传入，用于生成 localStorage 中存储进度的键名，确保不同课程的进度隔离。
- **输出信息 (类实例的方法)**:
    - `load(): CourseProgressData | null`: 从 localStorage 中读取并返回指定 `courseId` 的课程进度数据 (`CourseProgressData`)。如果未找到或数据格式不正确，则返回 `null`。
    - `save(progress: CourseProgressData): boolean`: 将传入的 `progress` 对象（类型为 `CourseProgressData`）序列化并保存到 localStorage。成功返回 `true`，失败（如 localStorage 不可用）返回 `false`。
    - `clear(): void`: 从 localStorage 中移除当前 `courseId` 的课程进度数据。
    - `getWidgetProgress<T extends WidgetType>(widgetId: string, progress: CourseProgressData | null): WidgetProgress<T> | null`: 一个辅助方法，从给定的课程进度对象中提取特定小组件的进度。
    - `update<T extends WidgetType>(params: { sectionIndex: number; widgetIndex: number; data?: WidgetProgress<T> }): boolean`: 更新课程进度中特定小组件的进度数据。它会先加载当前总进度，修改对应小组件的部分，然后重新保存整个课程进度。

#### 2. 配置 (`/src/configs`)

##### 2.1. `captcha.ts`
- **文件路径**: `src/configs/captcha.ts`
- **基本说明**: (当前文件内容为空) 预留的验证码相关配置文件。
- **场景说明**: (假设) 可能用于存放与阿里云验证码或其他验证码服务相关的配置参数，如App Key、场景标识等，方便统一管理和修改。
- **输入信息**: 无（作为配置文件）。
- **输出信息**: (假设) 导出一个包含验证码配置项的对象。

---
### 五、数据类型 (`/src/types`)

`@repo/core` 定义了详尽的 TypeScript 类型和接口，以确保整个业务核心库的数据一致性、代码可读性和可维护性。这些类型分布在 `/src/types` 目录下的多个文件中。

#### 1. 课程数据 (`/src/types/course-data/`)
   - **`course.ts`**: 定义核心的课程 (`Course`) 和章节 (`Section`) 结构，以及课程模式 (`CourseMode`) 和章节类型 (`SectionType`) 枚举。
   - **`widget.ts`**: 定义小组件 (`Widget`) 的通用结构、小组件类型枚举 (`WidgetType`)、小组件状态枚举 (`WidgetStatus`)，以及各种小组件内容的数据映射 (`WidgetContentMap`)。
   - **`widget-*.ts` (如 `widget-video.ts`, `widget-banshu.ts`, `widget-exercise.ts`)**: 分别定义了视频、板书、练习题等具体类型小组件的详细数据结构 (e.g., `VideoWidgetData`, `BanshuWidgetData`, `ExerciseWidgetContent`, `QuestionData`)。这些文件通常包含与该小组件相关的特定属性，如视频URL、字幕、板书的段落行、练习题的选项和答案等。

#### 2. 学习进度数据 (`/src/types/progress.ts`)
   - **`CourseProgressData`**: 定义了整个课程的学习进度的数据结构，包括当前所在的章节和小组件索引，以及一个存储了所有已交互过的小组件具体进度的映射表 (`widgetProgressMap`)。
   - **`WidgetProgress<T extends WidgetType>`**: 定义了单个小组件的学习进度数据的通用结构，它包含小组件的ID、类型以及一个与类型对应的内容进度对象 (`ProgressContent[T]`)。
   - **`ProgressContent`**: 一个映射表，定义了每种小组件类型其进度内容具体包含哪些字段（例如，视频进度包含当前帧 `frame` 和观看耗时 `costTime`；练习进度包含当前题目索引 `currentQuestionIndex`、用户答案 `answer` 等）。

#### 3. 板书与引导UI相关类型 (`/src/types/banshu.ts`, `/src/types/ui/guide.ts`)
   - **`ViewData` (in `banshu.ts`)**: 板书在渲染时使用的视图模型，将原始板书数据处理成层级结构，方便动态渲染。
   - **`BanshuMode` (in `banshu.ts`)**: 板书的播放模式枚举（跟随模式 `Follow`、自由模式 `Free`）。
   - **`LineView` (in `ui/guide.ts`)**: 引导内容行在渲染时使用的视图模型。

#### 4. 基础枚举与类型 (`/src/types/base.ts` 和 `data/base.ts`)
   - 定义了一些在多个模块中通用的基础枚举和类型，如题目类型 (`QuestionType`)、白板位置 (`WhiteboardPosition`)、白板类型 (`WhiteboardType`)，以及图片 (`Picture`)、视频 (`Video`)、字幕 (`Subtitle`) 等基础媒体对象的结构。

这些类型是理解 `@repo/core` 模块间数据流和组件 Props 的关键。在开发过程中，应充分利用 TypeScript 的类型提示和检查。

---

## 设计原则

1.  **单一职责**: 每个模块、组件和 Hook 都应专注于一个明确定义的业务功能或 UI 任务。
2.  **高内聚低耦合**: 相关逻辑应内聚在各自的模块中，模块间的依赖关系应尽可能清晰和松散。
3.  **状态管理与分离**:
    - 使用 React Context (如 `CourseContext`, `ExerciseContext`, `BanshuContext`) 进行相关业务模块的局部状态管理和共享。
    - 复杂或跨模块的状态逻辑考虑使用 Signals (如 `@preact-signals/safe-react`) 或其他更专业的状态管理库（如果项目引入）。
    - UI 组件应尽可能作为受控组件或纯展示组件，其状态由父组件或 Context 管理。
4.  **可测试性**: 业务逻辑（尤其是在 Hooks 和 Services 中）应易于进行单元测试。组件应设计为可测试的，例如通过 Props 注入依赖。
5.  **可扩展性**: 核心模块和组件的设计应考虑到未来的功能扩展，例如通过组合、继承（谨慎使用）或清晰的接口来添加新行为。
6.  **类型安全**: 严格使用 TypeScript，为所有 Props、函数参数、返回值和共享状态定义明确的类型。

## 贡献指南

1.  **遵循现有模式**: 新增或修改业务逻辑时，请参考并遵循项目中已有的设计模式和代码风格。
2.  **清晰的类型定义**: 为所有新的业务模块、组件 Props、Hooks 的参数和返回值、以及 Services 的方法提供完整且准确的 TypeScript 类型定义。
3.  **封装业务逻辑**:
    - 复杂的、可重用的业务逻辑应封装在自定义 Hooks (`/src/hooks` 或相关模块的 `contexts`) 中。
    - 与后端 API 交互或处理纯数据逻辑的服务应放在 `/src/services` 目录下。
4.  **文档完整性**:
    对于每一个新增或显著修改的UI组件、Hook或核心模块，其代码注释或配套的文档（如本README的更新）应至少包含以下信息：
    -   **基本说明**：清晰描述其功能和用途（是什么东西）。
    -   **场景说明**：列举适合使用该组件/钩子/模块的具体场景（什么时候可以用）。
    -   **输入信息**:
        -   对于组件：详细说明其接收的核心 Props，包括 Props 名称、TypeScript 类型、是否必需、默认值（如有）以及其作用。
        -   对于 Hooks：说明其接收的参数，包括参数名称、类型、是否必需以及作用。如果 Hook 依赖 Context，请指明。
        -   对于类/模块：说明构造函数参数或模块初始化所需的配置。
    -   **输出信息**:
        -   对于组件：描述其主要的渲染输出（UI结构和视觉表现）以及关键的交互行为。
        -   对于 Hooks：描述其返回值（通常是一个对象或数组），并说明返回值的各个属性/元素的类型和含义。
        -   对于类/模块：描述其公共方法提供的功能或其管理的状态如何影响系统。
5.  **代码质量**:
    -   提交代码前，确保通过项目的 ESLint 和 TypeScript 检查 (`pnpm lint`, `pnpm typecheck`)。
    -   编写可读性高、易于维护的代码。添加必要的注释来解释复杂的逻辑。
6.  **测试**: (理想情况下) 为核心业务逻辑和关键组件添加单元测试或集成测试。
7.  **Code Review**:
    -   所有代码变更（尤其是新功能或重大重构）都应通过 Pull Request 提交。
    -   在 PR 描述中清晰说明变更的目的、内容和潜在影响。
    -   积极参与 Code Review 过程，回应审查意见。
8.  **命名约定**: 遵循项目中已建立的文件、变量、函数、类和组件的命名约定。
