---
description: 
globs: 
alwaysApply: false
---
【Cursor开发中rules配置文件】Android版（适配Java/Kotlin场景）

### 通用规则
1. 默认情况下，所有回复都必须是中文，而且需要在开头称呼用户为"帅哥："
2. 复杂需求拆解成小任务，分步实现，每完成一个小任务后再继续
3. 代码实现前后要仔细检查，确保类型安全、空安全处理完整、生命周期管理正确
4. 在已有功能基础上添加新功能时，必须确保：
   - 不影响原有功能和组件复用性
   - 不添加其他功能、代码、逻辑、文件、配置、依赖
5. 遵循项目架构设计，保持代码风格与 Android 编码规范一致（如 Kotlin 风格指南）
6. 组件设计遵循单一职责原则，不混合多个变更
7. 在进行组件设计规划时，符合"第一性原理"
8. 在代码实现时，符合"KISS原则"和"SOLID原则"
9. 优先使用 Android Jetpack 组件库和现有工具类，避免重复代码
10. 不引入不必要的依赖，优先使用项目已有库
11. 确保代码可读性，复杂逻辑添加注释，类和接口参数详细定义
12. 代码变更范围最小化，避免修改公共组件、全局状态
13. 实现后进行基本逻辑自检，确保生命周期管理和内存泄漏处理正确
14. 如有疑问，先询问再修改，不要擅自改变组件 API 设计

### 自动化执行与安全策略
15. 自动执行无需严格确认的操作，提高效率：
   - 自动执行 Kotlin 空安全检查、Android Lint 验证
   - 文件操作（创建 Activity、Fragment、修改布局文件）无需额外确认
   - 常规命令（如 Gradle 依赖安装、运行模拟器）可直接执行
   - 涉及 Manifest 配置、权限修改等重要变更仍需确认
16. 重要操作（修改 Application 类、AndroidManifest.xml）应先保留副本
17. 涉及 API 接口变更，优先修改数据模型类和接口定义
18. 执行影响较大的修改前，自动检测组件依赖关系，分析影响范围

### 代码质量优化
19. 代码生成后，自动优化（移除未使用导入、合并重复资源文件）
20. 对可能影响性能的代码（如主线程阻塞、过度绘制、内存泄漏风险）提供优化建议
21. 确保异常处理和加载状态管理，防止应用崩溃和 ANR

### 架构感知
22. 优先分析现有架构模式（MVC/MVP/MVVM/Clean Architecture）与依赖注入方式，避免创建冗余组件
23. 添加功能时，优先考虑复用 ViewModel、Repository 或现有组件
24. 如遇架构不清晰，先梳理组件层次与数据流，再执行修改

### 代码变更的可追溯性
25. 提供清晰的 commit 信息，描述组件变更和影响范围
26. 对于 UI 组件重大调整，生成变更文档与截图对比
27. API 或接口变更时，提供向下兼容方案或迁移指南
28. 执行任务前，先分析项目结构和组件关系文档
29. 每次修改后，生成任务总结，说明组件变更和状态管理调整
30. 手动维护组件文档与架构说明，确保长期可维护性

### Android 开发规则
31. 严格遵循 Android 生命周期管理，避免内存泄漏和崩溃
32. 处理好 Activity/Fragment 之间的数据传递，优先使用 ViewModel 共享数据
33. UI 操作必须在主线程执行，耗时操作放在工作线程
34. 合理使用协程（Kotlin）或 RxJava（Java）进行异步操作
35. 注意适配不同屏幕尺寸和系统版本的兼容性问题
36. 使用 Android Jetpack 组件（如 Navigation、Room、WorkManager）提高开发效率
37. 遵循 Material Design 设计规范，保持 UI 一致性
38. 注意权限管理和安全性，特别是涉及敏感数据的操作
39. 优化应用启动速度和 UI 渲染性能
40. 合理使用资源文件（strings.xml、colors.xml、styles.xml）提高可维护性