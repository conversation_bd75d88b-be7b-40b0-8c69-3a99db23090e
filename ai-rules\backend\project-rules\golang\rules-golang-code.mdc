---
description: 
globs: 
alwaysApply: false
---
# Golang 开发规范与示例 (增强版)

**核心原则：**

*   **简洁优先 (KISS)**：保持代码简单明了，避免不必要复杂性。选择最简方案解决问题。优先使用标准库和简单方法。避免过早优化和过度工程化。函数只做一件事并做好。
*   **第一性原理**：从基本原理出发设计功能和模块。避免过度设计，专注解决当前问题。设计决策必须有明确理由。实现方案基于问题本质特性。记录架构决策原因和考虑因素。
*   **SOLID原则**:
    *   **单一职责**：每个模块只有一个变化原因。代码修改遵循单一职责原则，不混合多个变更。
    *   **开放封闭**：对扩展开放，对修改封闭。
    *   **里氏替换**：子类必须能替换父类。
    *   **接口隔离**：不强制实现不需要的方法。
    *   **依赖倒置**：依赖抽象而非具体实现。

**通用编码规则：**

1.  **沟通与文档**：使用中文进行沟通与文档编写。
2.  **任务拆解**：复杂需求拆解成小任务，分步实现，每完成一个小任务后再继续。
3.  **代码一致性**：遵循架构设计，保持代码风格一致。
4.  **增量开发**：在已有功能基础上添加新功能时，必须确保：
    *   不影响原有功能。
    *   不添加其他功能、代码、逻辑、文件、配置、依赖。
5.  **代码复用**：尽量复用已有代码，避免重复代码。
6.  **依赖管理**：不引入不必要的依赖，避免增加维护成本。
7.  **可读性与可维护性**：确保代码可读性与可维护性。注释说明"为什么"而非"是什么"。
8.  **变更范围**：代码变更范围最小化，避免大范围修改。
9.  **自检**：实现后进行基本逻辑自检，确保无错误。
10. **疑问处理**：如果有疑问，先询问再修改，不要擅自做决定。
11. **日志记录**：
    *   日志不记录`logger.Info`；默认是`logger.debug`。
    *   记录日志一般调用`app/core/loger/logger_new`目录的日志框架。
    *   日志包含请求ID、用户ID等上下文。
    *   区分日志级别：DEBUG、INFO、WARN、ERROR、FATAL。
    *   日志不记录敏感信息。
    *   禁止在库代码中配置全局日志实例。
    *   入口/出口记录关键信息，使用结构化字段。
    *   **每个方法必须添加耗时统计日志**。
12. **常量定义**:
    *   通用的常量定义在`app/consts`。
    *   只要涉及到输出中文，就定义成常量。
    *   **所有数字类型、文档、错误提示词都设置成常量**。
13. **数据模型**:
    *   结构体都放到`model/api`。
    *   **所有`int`类型都必须使用`int64`**。
    *   DTO统一定义在 `model/dto`下面。
    *   JSON统一是小写驼峰。
    *   **跟前端交互的时间统一是毫秒时间戳，int64整数值**。
14. **项目构建与启动**:
    *   首先运行`make all` 然后 `make build`构建项目。
    *   使用`bin/gil_teacher`启动服务。
    *   发现错误立即修复。

**Golang 特定编码规则：**

1.  **官方规范**：遵循 Go 官方代码规范和 `golint` 检查。
2.  **类型使用**：使用明确的类型，避免过度使用 `interface{}`。
3.  **调试**：禁止使用 `fmt.Print` 调试；统一使用结构化日志 `log/slog` (与项目日志规范 `app/core/loger/logger_new` 协调，优先项目规范)。
4.  **错误处理 (Error Handling - Go特定实践)**：
    *   错误处理使用显式返回，不要捕获 panic。
    *   使用有意义的错误信息，考虑 `errors.Is`/`errors.As`/`fmt.Errorf`。
    *   使用统一错误码体系。
    *   错误信息包含错误码、错误信息和上下文。
    *   **错误包装 (Error Wrapping)**: 强调使用 `fmt.Errorf` 的 `%w` 动词来包装错误，以便上层代码可以使用 `errors.Is` 和 `errors.As` 进行检查。
        ```go
        import (
            "errors"
            "fmt"
            "os"
            "time"
            "log/slog"
        )

        var ErrResourceNotFound = errors.New("resource not found")

        func readFile(path string) ([]byte, error) {
            start := time.Now()
            defer func() {
                slog.Debug("readFile耗时统计", "duration_ms", time.Since(start).Milliseconds())
            }()
            
            data, err := os.ReadFile(path)
            if err != nil {
                // 包装原始错误，添加上下文信息
                return nil, fmt.Errorf("readFile failed for %s: %w", path, err)
            }
            return data, nil
        }

        func main() {
            _, err := readFile("/non/existent/file")
            if err != nil {
                if errors.Is(err, os.ErrNotExist) { // 可以检查被包装的原始错误
                    fmt.Println("File does not exist.")
                }
                // 也可以检查自定义的包装错误，如果 ErrResourceNotFound 是通过 %w 包装的
            }
        }
        ```
    *   **定义Sentinel Errors (哨兵错误)**: 当需要让调用者能够可靠地识别特定错误类型时（如 `io.EOF`），定义包级别的哨兵错误变量（如 `var ErrUserNotFound = errors.New("user not found")`）。这比仅仅比较错误字符串更可靠。
    *   **自定义错误类型 (Custom Error Types)**: 当错误需要携带额外上下文信息时，可以定义实现 `error` 接口的自定义结构体类型。
        ```go
        type MyCustomError struct {
            Operation string
            Code      int64  // 使用int64
            Message   string
            Err       error // underlying error
        }

        func (e *MyCustomError) Error() string {
            return fmt.Sprintf("operation %s failed with code %d: %s (underlying: %v)", e.Operation, e.Code, e.Message, e.Err)
        }

        // 实现 Unwrap() 以支持 errors.Is 和 errors.As
        func (e *MyCustomError) Unwrap() error {
            return e.Err
        }

        func doSomething() error {
            start := time.Now()
            defer func() {
                slog.Debug("doSomething耗时统计", "duration_ms", time.Since(start).Milliseconds())
            }()
            
            // ...
            if someConditionFails {
                return &MyCustomError{
                    Operation: "doSomething",
                    Code:      1001,
                    Message:   "specific condition failed",
                    Err:       originalError, // 可选的原始错误
                }
            }
            return nil
        }
        ```
    *   **避免在不必要时返回 `(nil, nil)`**: 如果一个函数可能返回错误，其签名通常是 `(SomeType, error)`。如果操作成功但没有有意义的值返回（例如，一个更新操作），可以返回 `(nil, error)` 并让错误为 `nil`。避免设计成 `(SomeType, bool, error)` 这样的形式，除非 `bool` 有非常明确的独立含义。
5.  **代码隔离**：I/O、框架耦合应隔离到适配层。
6.  **并发**：
    *   优先使用同步方法，谨慎使用 goroutine。
    *   合理使用goroutine，避免泄漏。
    *   使用context控制超时和取消。
    *   避免共享状态，使用并发安全数据结构。
7.  **命名约定**：
    *   函数/方法: `MixedCaps`
    *   变量: `mixedCaps`
    *   包名: `lowercase`
    *   常量: `MixedCaps` 或 `ALL_CAPS`
8.  **导入顺序**：标准库 → 第三方 → 内部；使用空行分组。
9.  **注释**：包、结构体、公共函数和方法必须添加注释。
10. **数据结构**：
    *   优先使用结构体。
    *   为 JSON 序列化添加 `json:"fieldName"` tag (遵循小写驼峰)。
11. **性能优化**：
    *   使用 slice/map 预分配。
    *   使用 `io.Reader`/`io.Writer` 避免一次性加载大数据。
    *   频繁访问的数据使用缓存。
    *   使用批量操作提高性能。
12. **函数规范**:
    *   函数长度不超过50行，文件不超过500行。
    *   函数圈复杂度不超过15。
    *   **每个方法必须添加耗时统计**。

**架构与设计：**

1.  **DDD分层架构**：严格遵循DDD分层架构。
    *   禁止跨层依赖，上层只依赖直接下层。
    *   模块间依赖通过接口定义，避免直接依赖实现。
    *   在`domain`目录定义核心业务接口。
    *   按业务功能垂直切分模块，保证高内聚低耦合。
2.  **CQRS**: 设计模式遵循了CQRS原则，将命令(创建行为记录)与查询分离，并使用消息队列进行异步处理，提高系统的伸缩性和可靠性。
3.  **依赖注入**：
    *   通过wire管理组件依赖。
    *   避免全局变量和单例模式。
    *   定义清晰的组件生命周期。
4.  **API设计 (增强)**:
    *   使用URL路径版本控制。
    *   保持API向后兼容性。
    *   使用Swagger文档化API。
    *   统一API响应格式。
    *   正确使用HTTP状态码。
    *   实现适当的缓存控制。
    *   API请求参数使用结构体定义和验证标签。
    *   **明确的请求与响应结构体**: 为每个API端点定义清晰的`XXXRequest`和`XXXResponse`结构体。
        ```go
        // CreateUserRequest 定义创建用户API的请求体
        type CreateUserRequest struct {
            Username string `json:"username" validate:"required,min=3,max=32"`
            Email    string `json:"email" validate:"required,email"`
            Password string `json:"password" validate:"required,min=8"`
        }

        // CreateUserResponse 定义创建用户API的响应体
        type CreateUserResponse struct {
            UserID    int64  `json:"userId"`    // 使用int64
            Message   string `json:"message"`
            CreatedAt int64  `json:"createdAt"` // 毫秒时间戳
        }
        ```
    *   **Context传递**: 所有处理API请求的函数（尤其是在service层和domain层）都应该接受`context.Context`作为第一个参数。
        ```go
        // service/user_service.go
        func (s *UserService) CreateUser(ctx context.Context, req *dto.CreateUserRequest) (*dto.CreateUserResponse, error) {
            start := time.Now()
            defer func() {
                slog.Debug("CreateUser耗时统计", "duration_ms", time.Since(start).Milliseconds())
            }()
            // ... 使用ctx进行数据库调用或调用其他服务
        }
        ```
    *   **Idempotency (幂等性)**: 对于创建或修改资源的API（POST, PUT, PATCH），如果可能，应考虑支持幂等性。客户端可以使用如`Idempotency-Key`这样的HTTP头。
    *   **版本控制清晰化**: "使用URL路径版本控制" (如 `/v1/users`)。也可以考虑通过HTTP Header (`Accept: application/vnd.myapi.v1+json`) 进行版本控制。
5.  **数据库设计**:
    *   表名字段名使用`snake_case`。
    *   包含创建时间和更新时间字段。
    *   使用软删除机制。
    *   使用迁移工具管理表结构变更。
    *   加密存储敏感数据。
    *   设置数据库操作超时时间。
    *   使用参数化查询，禁止SQL拼接。

**示例代码 (已根据增强规范调整)**

```go
package user

import (
	"errors"
	"log/slog" // 假设这是项目选定的标准日志库，或项目中app/core/loger/logger_new的底层实现
	"time"

	// "your_project_path/app/core/logger" // 或者实际项目日志路径
)

// 常量定义 - 所有数字类型、文档、错误提示词都设置成常量
const (
	// 用户状态常量
	UserStatusActive   int64 = 1
	UserStatusInactive int64 = 2
	
	// 分页常量
	DefaultPageSize int64 = 10
	MaxPageSize     int64 = 100
	
	// 错误提示词常量
	ErrMsgInvalidChunkSize = "分块大小必须大于0"
	ErrMsgUserNotFound     = "用户不存在"
	ErrMsgInvalidUserID    = "无效的用户ID"
	
	// 文档常量
	DocUserCreated      = "用户创建成功"
	DocUserUpdated      = "用户更新成功"
	DocProcessStarted   = "开始处理项目"
	DocProcessCompleted = "所有项目处理完成"
)

// UserDTO 表示用户数据传输对象
// 遵循json小写驼峰，时间为毫秒时间戳
type UserDTO struct {
	ID       int64  `json:"id"`
	Username string `json:"username"`
	JoinedAt int64  `json:"joinedAt"` // 毫秒时间戳
}

// User 表示用户领域实体 (model/api)
type User struct {
	ID       int64
	Username string
	JoinedAt time.Time // 内部使用 time.Time
}

// UserMapper 领域实体 ↔ DTO 映射器
type UserMapper struct{}

// ToDTO 将用户实体转换为 DTO
// DTO中的JoinedAt应为毫秒时间戳
func (m *UserMapper) ToDTO(user *User) *UserDTO {
	start := time.Now()
	defer func() {
		// 使用 app/core/loger/logger_new 记录耗时统计
		slog.Debug("ToDTO耗时统计", "duration_ms", time.Since(start).Milliseconds())
	}()
	
	if user == nil {
		return nil
	}
	return &UserDTO{
		ID:       user.ID,
		Username: user.Username,
		JoinedAt: user.JoinedAt.UnixMilli(), // 转换为毫秒时间戳
	}
}

// ToEntity 将 DTO 转换为用户实体
// DTO中的JoinedAt为毫秒时间戳
func (m *UserMapper) ToEntity(dto *UserDTO) *User {
	start := time.Now()
	defer func() {
		// 使用 app/core/loger/logger_new 记录耗时统计
		slog.Debug("ToEntity耗时统计", "duration_ms", time.Since(start).Milliseconds())
	}()
	
	if dto == nil {
		return nil
	}
	return &User{
		ID:       dto.ID,
		Username: dto.Username,
		JoinedAt: time.UnixMilli(dto.JoinedAt).UTC(), // 从毫秒时间戳转换
	}
}

// ErrInvalidChunkSize 表示分块大小无效 (定义在 app/consts)
var ErrInvalidChunkSize = errors.New(ErrMsgInvalidChunkSize)

// Chunked 把整数切片按固定大小分块
//
// 参数:
//   - items: 整数切片数据源
//   - size: 每块元素数量，需 >0 (int64类型)
//
// 返回:
//   - [][]int64: 分块后的二维切片
//   - error: 如果 size <= 0，返回错误
func Chunked(items []int64, size int64) ([][]int64, error) {
	start := time.Now()
	defer func() {
		// 使用 app/core/loger/logger_new 记录耗时统计
		slog.Debug("Chunked耗时统计", "duration_ms", time.Since(start).Milliseconds())
	}()
	
	// 使用 app/core/loger/logger_new 目录的日志框架
	slog.Debug(DocProcessStarted, "total_items", len(items), "chunk_size", size)

	if size <= 0 {
		slog.Warn(ErrMsgInvalidChunkSize, "size", size)
		return nil, ErrInvalidChunkSize
	}

	if len(items) == 0 {
		return [][]int64{}, nil
	}

	// 预分配结果切片
	chunksCount := (int64(len(items)) + size - 1) / size
	result := make([][]int64, 0, chunksCount)

	for i := int64(0); i < int64(len(items)); i += size {
		end := i + size
		if end > int64(len(items)) {
			end = int64(len(items))
		}
		result = append(result, items[i:end])
	}

	slog.Debug("分块处理完成", "chunks_count", len(result))
	return result, nil
}

// ProcessItems 处理整数切片并使用迭代器模式避免一次性加载大数据
// 参数:
//   - processor: 处理单个项目的方法
//   - items: int64切片数据源
// 返回:
//   - error: 如果处理过程中发生错误
func ProcessItems(processor func(item int64) error, items []int64) error {
	start := time.Now()
	defer func() {
		// 使用 app/core/loger/logger_new 记录耗时统计
		slog.Debug("ProcessItems耗时统计", "duration_ms", time.Since(start).Milliseconds())
	}()
	
	// 使用 app/core/loger/logger_new 目录的日志框架
	slog.Debug(DocProcessStarted, "count", len(items))

	for i, item := range items {
		if err := processor(item); err != nil {
			slog.Error("处理项目失败", "index", i, "error", err)
			return err // 根据SOLID，函数只做一件事，错误直接返回
		}
	}

	slog.Debug(DocProcessCompleted)
	return nil
}