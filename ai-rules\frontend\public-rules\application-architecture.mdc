---
description: 
globs: 
alwaysApply: false
---
# 应用架构文档

## 1. 应用 (Application)

  - 每个应用是一个可独立部署的单元, 拥有独立的运行环境和部署流程。
  - 应用聚焦于某一类业务场景, 承担特定的业务目标。例如: 学生端应用、教师端应用、AI生产工具应用等。
  - 应用之间通过 API 或共享包（例如 `@repo/core`, `@repo/lib`）进行通信与复用, 保持高内聚、低耦合。
  - 每个应用拥有独立的配置、依赖和资源管理, 便于独立开发、测试和上线。

## 2. 页面 (Page)

  - 应用由一个或多个页面组成, 每个页面对应唯一的 URL 路径。
  - 页面是用户与应用交互的入口单元, 承载具体的业务流程。
  - 页面负责组织和调度业务视图 (Business Views) 或业务组件, 处理页面级的路由、权限、布局、配置等逻辑。
  - 页面结构清晰, 便于利用 Next.js 的 App Router 进行按需加载和性能优化。
  - 页面组件通常直接位于 `app/` 目录下各路由段中 (e.g., `app/dashboard/settings/page.tsx`), 符合 Next.js 的 App Router 规范。

## 3. 业务视图 (Business View)

  - 页面可以通过使用单个业务视图或组合多个业务视图来实现具体的业务功能。
  - 业务视图是构成页面的主要功能模块，封装了特定业务场景的完整展现和交互逻辑。
  - 业务视图通常位于 `app/views/` 目录下，例如 `app/views/user-management/user-list-view.tsx`。
  - 业务视图采用 **MVVM (Model-View-ViewModel)** 模式进行设计和实现，旨在清晰地分离关注点，提升代码的可维护性和可测试性。
  - 对于复杂的状态管理场景，可以使用 **MVVM + Context 混合模式**，结合两种架构的优势。

### 架构模式选择

业务视图根据复杂度和场景选择合适的架构模式：

#### 3个核心判断问题
1. **业务完整性**：这些状态是否属于同一个业务流程？
2. **状态复杂度**：是否涉及复杂的状态管理和数据转换？
3. **组件层级**：状态需要跨越多少层组件？

#### 推荐的架构模式
- **简单场景** → 纯 MVVM 模式
- **复杂状态管理** → MVVM + Context 混合模式
- **跨组件共享** → Context 为主，ViewModel 辅助

#### 灵活的调用链模式
根据业务复杂度和场景需求，支持以下调用链模式：

**模式一：直接 MVVM（简单场景）**
```
View → ViewModel → Model
```

**模式二：Context 协调（复杂状态管理）**
```
View → Context → ViewModel → Model
```

**模式三：混合模式（最灵活）**
```
View → Context (共享状态)
View → ViewModel (独立逻辑)
ViewModel → Model (数据获取)
```

#### 调用链选择原则
- **简单独立功能** → 使用模式一（直接 MVVM）
- **需要状态共享** → 使用模式二（Context 协调）
- **复杂业务场景** → 使用模式三（混合模式）
- **核心约束**：View 层严禁直接调用 Model 层（跨层调用）

### MVVM 模式详解

MVVM 模式将业务视图划分为三个核心部分：

  - **Model (数据模型层):**

      - **核心职责:**
          - 负责数据的获取、验证、转换、存储和管理。
          - 定义应用的数据结构（优先使用Zod进行Schema定义和运行时校验）及与数据源（如后端API、本地存储）的交互逻辑。
          - 封装数据操作的细节，向上层（ViewModel）提供清晰、一致的数据服务接口（通常是自定义SWR Hooks或服务函数）。
      - **核心约束:**
          - **严禁直接与View层或ViewModel层产生任何形式的直接耦合或引用。** Model层不应感知View或ViewModel的存在。
          - 数据获取和操作应通过统一封装的机制（如共享的`fetcher`函数）进行，以确保请求配置、错误处理、认证头等的一致性。
          - **重要：** 共享的 `fetcher` 函数（如 `@repo/lib/utils/fetcher` 中的 `post`, `get` 等）已经处理了统一的 API 响应格式校验（`code` 字段检查）和数据解构，直接返回 `data` 部分。Model层使用这些函数时，无需再次检查 `response.code` 或手动解构 `response.data`。
      - **核心任务:**
          - 定义数据Schema (使用Zod) 并推断TypeScript类型。
          - 实现数据获取逻辑，通常通过封装`useSWR`等库，并结合统一的`fetcher`函数。
          - 在数据获取后、暴露给ViewModel前，进行必要的数据校验 (Zod `parse`) 和转换 (如API原始数据到前端友好结构的映射)。
          - 管理数据缓存策略和状态同步 (SWR等库可辅助处理)。
      - **位置:** 通常位于 `app/models` 目录。若为跨应用复用的模型，则位于相应共享包的 `models` 目录 (e.g., `packages/core/models`)。

  - **View (视图层):**

      - **核心职责:**
          - 负责界面的声明式渲染和用户原始输入的捕获与转发。
          - 作为纯粹的UI呈现层，专注于"如何展示数据"和"如何将用户行为通知给ViewModel或Context"。
      - **核心约束:**
          - **严禁包含任何业务逻辑、业务相关的状态声明或直接的数据操作方法。** 所有业务处理必须委托给ViewModel或Context。
          - **严禁直接调用 Model 层**，必须通过 ViewModel 或 Context 进行数据访问。
          - 仅允许包含纯UI相关的瞬时状态（如动画控制、特定UI元素的展开/折叠状态等，且这类状态不应影响或依赖业务流程）和简单的事件处理函数。
          - 事件处理函数的主要职责是调用ViewModel或Context暴露的命令或方法，并传递必要的事件参数。
      - **允许的调用方式:**
          - ✅ **直接调用 ViewModel**：`const viewModel = useUserViewModel()`
          - ✅ **使用 Context**：`const context = useUserContext()`
          - ✅ **混合使用**：同时使用 Context（共享状态）和 ViewModel（独立逻辑）
          - ❌ **禁止直接调用 Model**：不能直接使用 `useUsers()` 等 Model hooks
      - **核心任务:**
          - 接收来自 ViewModel 或 Context 的数据并将其渲染到用户界面。
          - 将用户的交互事件转换为对 ViewModel 或 Context 命令的调用。
      - **位置:** 通常是 React 组件，位于 `app/components` (应用内通用纯UI组件) 或业务视图内部的 `components` 子目录 (特定于该业务视图的纯UI组件)。若为跨应用复用的UI组件，则位于 `@repo/ui/components`。

  - **ViewModel (视图模型层):**

      - **核心职责:**
          - 作为 Model 和 View 之间的桥梁，集中处理所有业务逻辑、表现逻辑和用户交互响应。
          - 从 Model 获取原始业务数据，并根据 View 的需求进行处理、聚合和格式化。
          - 管理与特定视图相关的所有业务状态和UI控制状态，并向 View 暴露这些状态和操作界面。
      - **核心约束:**
          - - ✅ 允许：调用Model层封装的自定义Hooks（如 useUsers(), useSubmitUserAnswer() 等）
          - ❌ 禁止：直接使用原始数据获取方法和Hooks：
            - ❌ 禁止：直接调用 fetch(), axios.get() 等HTTP客户端
            - ❌ 禁止：直接使用 useSWR(), useSWRMutation() 等原始数据获取Hooks
            - ❌ 禁止：绕过Model层直接访问API端点
          - 核心原则：所有数据操作必须通过Model层提供的封装接口进行
          - **严禁在ViewModel层包含任何形式的Mock数据或Mock逻辑。** 所有数据Mocking应遵循本文档第4节"数据Mocking"中定义的策略。
          - **严禁直接操作DOM或引用View层组件实例。** 与View的交互应完全通过数据绑定和命令执行。
      - **核心任务:**
          - 调用 Model 层提供的接口获取业务数据。
          - 管理视图所需的状态 (e.g., 加载状态 `isLoading`, 错误状态 `error`, 用户输入 `searchTerm`, UI控制状态 `isModalOpen` 等)，使用 React 的状态管理机制（如 `useState`, `useReducer`）。
          - 对从Model层获取的数据进行转换、计算或组合，以生成View可以直接消费的数据形态。
          - 向 View 暴露经过处理的、用于渲染的数据和可供调用的操作方法/命令 (通常是稳定的回调函数，使用 `useCallback` 包裹)。
          - 处理 View 传递过来的用户交互事件，根据业务规则更新自身状态、调用 Model 层接口或执行其他业务操作。
      - **位置:** 通常是自定义 React Hooks (e.g., `useUserListViewModel`)，位于 `app/viewmodels` 目录。若为跨应用复用的 ViewModel，则位于相应共享包的 `viewmodels` 目录。

> 如果基于本规范设计架构（TDD）,必须将核心约束在架构文档中体现

### 架构模式示例

以"用户管理"业务为例，展示不同架构模式的实现：

#### Model层 - 数据获取与转换
```typescript
// app/models/user-model.ts
export function useUsers() {
  const { data: users, error, isLoading, mutate } = useSWR(
    '/api/users',
    fetchAndTransformUsers
  );
  return { users, isLoading, error, mutateUsers: mutate };
}
```

#### 模式一：直接 MVVM（简单场景）
```typescript
// ViewModel层 - 业务逻辑处理
// app/viewmodels/use-user-list-viewmodel.ts
export function useUserListViewModel() {
  const { users, isLoading, error, mutateUsers } = useUsers();
  const [searchTerm, setSearchTerm] = useState('');
  
  const filteredUsers = useMemo(() =>
    users?.filter(user =>
      user.displayName.includes(searchTerm) ||
      user.emailAddress.includes(searchTerm)
    ) || [], [users, searchTerm]
  );

  return {
    users: filteredUsers,
    isLoading, error, searchTerm,
    onSearchTermChange: setSearchTerm,
    onRefresh: mutateUsers
  };
}

// View层 - 直接使用 ViewModel
// app/views/user-management/user-list-view.tsx
export function UserListView() {
  const { users, isLoading, error, searchTerm, onSearchTermChange, onRefresh } = useUserListViewModel();
  
  if (isLoading) return <div>加载中...</div>;
  if (error) return <div>错误: {error.message}</div>;
  
  return (
    <div>
      <input value={searchTerm} onChange={e => onSearchTermChange(e.target.value)} />
      <button onClick={onRefresh}>刷新</button>
      <UserList users={users} />
    </div>
  );
}
```

#### 模式二：Context 协调（复杂状态管理）
```typescript
// Context层 - 状态协调
// app/contexts/user-management-context.tsx
export function UserManagementProvider({ children }) {
  const userListViewModel = useUserListViewModel();
  const userFormViewModel = useUserFormViewModel();
  
  const handleUserUpdate = useCallback((updatedUser) => {
    userFormViewModel.updateUser(updatedUser);
    userListViewModel.onRefresh(); // 刷新列表
  }, [userListViewModel, userFormViewModel]);
  
  return (
    <UserManagementContext.Provider value={{
      userListViewModel,
      userFormViewModel,
      handleUserUpdate
    }}>
      {children}
    </UserManagementContext.Provider>
  );
}

// View层 - 使用 Context
export function UserManagementView() {
  const { userListViewModel, handleUserUpdate } = useUserManagementContext();
  
  return (
    <div>
      <UserList
        users={userListViewModel.users}
        onUserSelect={handleUserUpdate}
      />
      <UserForm onSubmit={handleUserUpdate} />
    </div>
  );
}
```

#### 模式三：混合模式（最灵活）
```typescript
// View层 - 同时使用 Context 和 ViewModel
export function ComplexUserView() {
  // 使用 Context 获取共享状态
  const { globalUserState, updateGlobalState } = useUserContext();
  
  // 直接使用 ViewModel 处理独立逻辑
  const { searchResults, onSearch } = useUserSearchViewModel();
  
  return (
    <div>
      {/* 显示全局状态 */}
      <div>当前用户: {globalUserState.currentUser?.name}</div>
      
      {/* 独立的搜索功能 */}
      <SearchBox onSearch={onSearch} results={searchResults} />
      
      {/* 更新全局状态 */}
      <button onClick={() => updateGlobalState({ theme: 'dark' })}>
        切换主题
      </button>
    </div>
  );
}
```



### 分层目录结构约定

```
app/
├── models/           # 数据模型 (SWR Hooks, Zod Schemas)
├── viewmodels/       # 视图模型 (业务逻辑处理)
├── contexts/         # 业务状态管理 (复杂场景)
├── utils/           # 工具函数 (按领域组织)
├── views/           # 业务视图
└── components/      # 可复用组件
```

  - **Model层:**
      - 应用内: `app/models/` (e.g., `app/models/user-model.ts`)
      - 共享包: `packages/[packageName]/models/`
  - **ViewModel层:**
      - 应用内: `app/viewmodels/` (e.g., `app/viewmodels/use-user-list-viewmodel.ts`)
      - 共享包: `packages/[packageName]/viewmodels/`
  - **Context层:**
      - 应用内: `app/contexts/` (e.g., `app/contexts/form-context.tsx`)
      - 共享包: `packages/[packageName]/contexts/`
  - **View层 (纯 UI 组件):**
      - 应用内通用: `app/components/` (e.g., `app/components/user-list.tsx`)
      - 视图特定: `app/views/[viewName]/components/` (如果组件仅用于特定视图)
      - 共享UI库: `@repo/ui/components/`

### 组件命名与文件命名

  - **组件/Hooks命名 (PascalCase):** `UserList`, `useUserListViewModel`, `useUsers`
  - **文件名 (kebab-case):** `user-list.tsx`, `use-user-list-viewmodel.ts`, `user-model.ts`

### 可复用性与可测试性

  - **Model层:**
      - 自定义SWR Hooks (`useUsers`) 封装了所有数据获取、校验和转换逻辑，可被多个ViewModel复用。
      - 其内部的纯函数（如`transformRawUsersToTransformedUsers`）易于进行单元测试。
      - SWR Hooks本身可以通过Mock Service Worker (MSW) 或其他Mocking库进行集成测试。
  - **ViewModel层:**
      - 作为纯自定义Hooks (`useUserListViewModel`)，其业务逻辑可以通过Mock其依赖的Model层Hooks (`useUsers`)来进行单元测试。
      - 不包含直接的副作用（如API调用），使得测试更简单可靠。
  - **View层:**
      - 作为纯UI组件 (`UserList`, `UserListView`)，接收props并渲染，易于通过Storybook进行可视化展示和测试。
      - 可以使用React Testing Library (RTL) 进行交互测试，验证其是否正确调用ViewModel提供的回调。
  - 这种严格的分层和职责划分，极大地提高了代码的模块化程度、可理解性、可维护性和可测试性，从而提升整体开发质量和效率。

### 架构决策指南

#### 调用链模式选择
**模式一：直接 MVVM（View → ViewModel → Model）**
- ✅ **适用场景：**
  - 单一组件的独立功能
  - 简单的数据展示和交互
  - 无需跨组件状态共享
  - 纯展示类组件
- ❌ **不适用场景：**
  - 需要多组件状态协调
  - 复杂的业务流程
  - 深层组件树状态传递

**模式二：Context 协调（View → Context → ViewModel → Model）**
- ✅ **适用场景：**
  - 多步骤流程，状态相互依赖
  - 复杂表单，需要状态协调
  - 用户会话管理
  - 主题和配置管理
- ❌ **不适用场景：**
  - 简单独立功能
  - 临时 UI 状态
  - 单组件业务逻辑

**模式三：混合模式（View → Context + ViewModel → Model）**
- ✅ **适用场景：**
  - 既需要共享状态又有独立逻辑
  - 复杂业务场景的灵活处理
  - 渐进式架构升级
  - 大型组件的功能分离
- ⚠️ **注意事项：**
  - 需要明确区分共享状态和独立逻辑
  - 避免状态管理混乱
  - 保持代码可读性

#### 工具函数提取原则
**✅ 适合提取：**
- 纯函数，无副作用
- 通用验证逻辑
- 数据格式化函数
- 常量和配置映射

**❌ 不适合提取：**
- 高度特化的逻辑
- 与组件状态强耦合
- 只在单处使用的代码

#### 复用性评估标准
- **复用可能性 > 70%** → 强烈建议抽象
- **复用可能性 < 30%** → 保持内联
- **复用可能性 30-70%** → 评估其他收益

## 4. 数据 Mocking

在开发和测试过程中，有效的数据 Mocking 策略至关重要。**ViewModel层和Model层本身严禁包含任何Mock数据实现。**

1.  **API 中心化 Mock (ApiFox / Postman Mock Servers / etc.):**

      * **首选方案:** 优先使用专业的API设计和Mock工具（如ApiFox）。在工具中定义API接口并启用其Mock功能。
      * **优势:**
          * 统一Mock数据源，团队成员共享一致的Mock API，避免Mock数据不一致。
          * Mock数据与API文档（通常在同一平台维护）保持同步。
          * 无需在前端项目中编写和维护Mock代码。
      * **集成方式:** 前端应用在开发时，通过配置环境变量 `NEXT_PUBLIC_API_HOST` 指向Mock服务器的URL。共享的`fetcher` (`@repo/lib/utils/fetcher`) 将使用此Host地址。

2.  **前端项目级 Mock API (Next.js Route Handlers):**

      * **适用场景:**
          * 当外部Mock服务不可用或不便使用时（如离线开发）。
          * 需要模拟特定API边缘行为、错误状态或复杂交互序列，而外部Mock工具难以灵活支持时。
          * 进行快速原型开发或本地演示。
      * **实现方式:** 利用 Next.js 的 Route Handlers 在前端项目的 `app/api/[...slug]/route.ts` (示例路径) 目录下创建Mock API端点。
          * 这些Route Handlers **模拟真实的后端API行为**，并返回符合API契约（包括Zod Schema定义的结构）的数据。
          * **这些Mock API端点不应被Model层代码直接感知或调用。** 它们和真实API一样，通过`fetcher`经由`NEXT_PUBLIC_API_HOST`被访问。
        ```typescript
        // app/api/users/route.ts
        export async function GET() {
          return NextResponse.json({
            code: 0,
            message: 'Success',
            data: mockApiUsers
          });
        }
        ```
      * **注意事项:**
          * Mock API返回的数据结构**必须**与真实API的原始响应结构一致，统一采用 `{ code: 0, message: "Success", data: 实际数据 }` 的格式。
          * Mock API中禁止使用 model 中定义的枚举，直接使用硬编码，否则会无法正常获取数据。
          * 错误响应格式为 `{ code: 非0值, message: "错误信息", data: null }`。
          * 通过环境变量 (`NEXT_PUBLIC_API_HOST`, `NODE_ENV`) 控制应用是连接到真实API、外部Mock服务还是本地Mock API。
          * 当真实API可用时，应优先使用真实API进行集成测试。

## 5. 层级文档化与复用策略 (`README.md`)

为了提高代码的可发现性、可维护性和复用率，我们约定在 MVVM 的每一层（Model, ViewModel, View/Components）的根目录下维护一个 `README.md` 文件。

### 5.1 `README.md` 的核心目的

  - **功能索引:** 作为当前层级（或目录下）所有可用模块（Models, ViewModels, Components）的清单和快速导航。
  - **复用指南:** 帮助开发者快速了解现有功能，避免不必要的重复开发。
  - **维护契约:** 确保新增或修改功能时，相关文档得到同步更新。

### 5.2 `README.md` 的维护位置

  - **Model层:** `app/models/README.md`
  - **ViewModel层:** `app/viewmodels/README.md`
  - **View层 (应用内通用组件):** `app/components/README.md`
  - **(可选) 业务视图特定组件:** `app/views/[viewName]/components/README.md`
  - **共享包 (如 `@repo/ui`, `@repo/core`):** 在其各自的 `models/`, `viewmodels/`, `components/` 等目录下也应维护相应的 `README.md`。

### 5.3 `README.md` 的内容规范

每个 `README.md` 文件应至少包含以下内容：

  - **模块列表:**
      - **名称:** Model Hook (e.g., `useUsers`), ViewModel Hook (e.g., `useUserListViewModel`), Component (e.g., `UserList`)。
      - **简要描述:** 一句话概括其核心功能和职责。
      - **主要参数/Props (可选，针对复杂模块):** 列出关键的输入参数或 props 及其用途和类型概念。
      - **返回值/暴露接口 (可选，针对复杂模块):** 列出关键的返回值或 Hook 暴露的接口（如状态Signals、操作函数）及其类型概念。
      - **依赖关系 (可选):** 指出其依赖的其他关键模块（如某个Model Hook依赖特定的API端点概念）。
      - **链接 (可选):** 指向更详细的 JSDoc 注释或具体使用示例代码片段（如果适用）。
  - **使用场景/示例 (可选):** 对于较为复杂的模块，可以提供简短的代码片段作为使用示例。

**示例 `app/models/README.md`:**

```markdown
# Models

本文档列出了 `/app/models` 目录下所有可用的 Model Hooks 和相关数据结构。
这些模块负责封装应用的数据获取、校验、转换和业务实体逻辑。

## 可用 Model Hooks

| Hook 名称                 | 描述                                                       | 主要暴露接口/数据类型概念                                                               | 依赖API (概念)        |
| ------------------------- | ---------------------------------------------------------- | --------------------------------------------------------------------------------------- | --------------------- |
| `useUsers`                | 获取、校验并转换用户列表数据，提供加载、错误和mutate状态。 | `users: TransformedUser[]`, `isLoading: boolean`, `error: any`, `mutateUsers: Function` | GET `/api/users`      |
| `useUserById(id: number)` | 获取特定ID用户的详细信息。                                 | `user: TransformedUser`, `isLoading: boolean`, ...                                      | GET `/api/users/{id}` |
| ...                       | ...                                                        | ...                                                                                     | ...                   |

##核心数据结构 (Zod Schemas & Types)

- `RawUserSchema`, `RawUser` (原始API用户数据)
- `TransformedUserSchema`, `TransformedUser` (转换后供ViewModel使用的用户数据)
- ...

---
```

### 5.4 工作流程集成

#### 核心要求
- **新增模块时**：必须同步更新对应层级的 `README.md`
- **开发前检查**：查阅 `README.md` 了解可复用模块
- **Code Review**：检查文档更新情况

#### 复用决策流程
1. **完全可复用** → 直接使用
2. **部分可复用** → 评估变更影响，团队审批后修改
3. **不适合复用** → 开发新模块，更新文档

