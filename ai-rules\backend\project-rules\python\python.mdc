---
description: 
globs: backend/**/*.py
alwaysApply: false
---

# Python 通用代码生成规则

> **核心原则：简洁优先**

## 边界
- 遵循 `PEP 8 / PEP 257`
- 强制类型注解，`mypy --strict` 通过  
- 禁止 `print()` 调试；统一 `logging.getLogger(__name__)`  
- 禁止在库代码调用 `logging.basicConfig`  
- 禁止可变默认参数；禁止裸 `except`  
- I/O、框架耦合应隔离到适配层  
- 统一使用同步方法，尽量少使用异步方法

## 约定
- 命名：函数/变量 `snake_case`；类 `PascalCase`；常量 `UPPER_SNAKE`  
- 导入顺序：`stdlib` → 第三方 → 内部；组间空行  
- Docstring：Google 风格；类、公共函数、方法必须撰写  
- 数据结构：优先 `dataclasses` / `pydantic.BaseModel`  
- 日志：入口 / 出口打印关键信息  
- 性能：使用生成器 / 迭代器，避免一次性加载大列表 

# 6. 示例代码
```python
import logging
from datetime import datetime
from typing import Iterable

logger = logging.getLogger(__name__)


class UserDTO(BaseModel):
    id: int
    username: str
    joined_at: datetime


class UserMapper:
    """领域实体 ↔ DTO 映射器"""

    @staticmethod
    def to_dto(user: "User") -> UserDTO:
        return UserDTO(id=user.id, username=user.username, joined_at=user.joined_at)

    @staticmethod
    def to_entity(dto: UserDTO) -> "User":
        return User(id=dto.id, username=dto.username, joined_at=dto.joined_at)


def chunked(iterable: Iterable[int], size: int) -> Iterable[list[int]]:
    """把可迭代对象按固定大小分块

    Args:
        iterable: 数据源
        size: 每块元素数量，需 >0

    Yields:
        list[int]: 分块后的列表
    """
    if size <= 0:
        raise ValueError("size 必须大于 0")

    buf: list[int] = []
    for item in iterable:
        buf.append(item)
        if len(buf) == size:
            yield buf
            buf = []
    if buf:
        yield buf
```