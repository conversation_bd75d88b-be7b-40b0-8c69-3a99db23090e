---
description: 
globs: 
alwaysApply: false
---

# 日志规范 (v1.0)

## 1. 引言

### 1.1. 目的
本文档旨在定义项目统一的日志记录规范，确保日志的有效性、一致性和可管理性，便于问题排查、系统监控和性能分析。

### 1.2. 核心原则
*   **一致性**: 所有项目模块采用统一的日志记录方式。
*   **适当性**: 避免过多或过少的日志，确保记录关键信息但不影响系统性能。
*   **可追踪性**: 日志应包含足够的上下文信息，能够准确追踪问题。
*   **安全性**: 日志中绝不包含敏感信息。
*   **可查询性**: 日志格式应便于搜索和分析。

## 2. 日志组件

### 2.1. 日志库
项目统一使用 `app/core/loger/logger_new` 作为日志记录工具。严禁直接使用其他日志库或标准输出函数（如 `fmt.Print`）记录日志。

### 2.2. 底层实现
`logger_new` 内部基于 `github.com/sirupsen/logrus` 实现，支持结构化日志记录、多级别日志和自定义输出。

### 2.3. 日志格式
*   默认使用 JSON 格式输出，便于日志收集和解析。
*   时间戳采用 RFC3339 格式，包含毫秒级精度。
*   支持通过配置文件调整输出格式。

### 2.4. 日志输出
*   开发环境: 日志默认输出到控制台，彩色显示不同级别。
*   测试/生产环境: 日志写入文件，并通过日志收集系统统一管理。
*   支持同时输出到多个目标（文件、控制台、网络等）。

## 3. 日志级别

项目定义以下日志级别，按照严重程度由低到高排序：

### 3.1. DEBUG (调试)
*   **用途**: 详细的调试信息，用于开发和问题排查。
*   **原则**: 这是项目的**默认日志级别**，常规调试信息应使用此级别。
*   **示例场景**:
    *   函数入口/出口记录
    *   详细的执行路径
    *   临时变量值
    *   SQL查询记录
    *   第三方服务调用详情

### 3.2. INFO (信息)
*   **用途**: 重要事件的记录，通常表示应用正常工作中的关键节点。
*   **原则**: 根据项目规范，**一般情况下不使用 INFO 级别**，除非有明确记录审计需求或特殊情况。
*   **示例场景** (仅在需要时使用):
    *   应用启动/关闭
    *   重要配置加载完成
    *   服务注册/发现成功
    *   计划任务执行完成
    *   用户关键操作（如登录、重要业务提交）

### 3.3. WARN (警告)
*   **用途**: 潜在问题或异常情况，不影响系统正常运行但需要关注。
*   **原则**: 用于记录可能导致未来错误的情况或系统运行不理想的状态。
*   **示例场景**:
    *   配置项缺失但使用了默认值
    *   非关键依赖服务不可用但已降级处理
    *   性能下降但在可接受范围内
    *   资源使用率接近警戒线
    *   接口调用超时但已重试成功

### 3.4. ERROR (错误)
*   **用途**: 错误事件记录，表示功能无法正确执行但不影响整体服务运行。
*   **原则**: 所有影响功能正常运行的异常情况都应记录为 ERROR。
*   **示例场景**:
    *   数据库操作失败
    *   关键第三方服务调用失败
    *   请求处理过程中的未处理异常
    *   业务规则校验严重失败
    *   配置错误导致功能不可用

### 3.5. FATAL (致命)
*   **用途**: 导致应用程序终止运行的严重错误。
*   **原则**: 谨慎使用，通常在记录后应用会自动退出。
*   **示例场景**:
    *   无法连接到关键数据库且无法恢复
    *   核心配置文件加载失败
    *   系统资源耗尽（如内存溢出）
    *   关键安全机制失效

## 4. 日志内容规范

### 4.1. 必要上下文信息
每条日志记录应包含以下基本上下文信息：

*   **时间戳**: 精确到毫秒
*   **日志级别**: 明确标示日志等级
*   **请求ID (TraceID)**: 用于链路追踪
*   **调用位置**: 文件名和行号
*   **模块/服务名**: 标识日志来源
*   **消息内容**: 简洁明了的描述信息

### 4.2. 结构化信息
除了基本上下文外，还应根据具体场景记录以下结构化信息：

*   **用户ID**: 当请求关联到特定用户时
*   **请求参数**: 请求的关键参数（注意脱敏）
*   **错误详情**: 对于 ERROR/FATAL 级别，包含错误类型、错误码、错误消息
*   **性能指标**: 对于性能相关日志，记录耗时、资源使用情况
*   **业务数据**: 与日志事件相关的业务标识符（订单ID、商品ID等）

### 4.3. 日志信息规范
*   **日志消息格式**: 简洁、明确、包含必要信息
*   **避免重复信息**: 不在消息中重复已经在结构化字段中存在的信息
*   **禁止记录敏感信息**: 密码、令牌、身份证、银行卡等敏感信息必须脱敏或删除
*   **错误日志完整性**: 错误日志必须包含错误栈信息或详细错误描述
*   **避免大段数据**: 不记录大段数据（如完整的响应体）

## 5. 各层日志记录规范

### 5.1. DAO层
*   主要记录 **DEBUG** 级别日志，包含SQL语句、参数和执行时间
*   数据库操作异常记录为 **ERROR** 级别
*   避免记录完整结果集，大结果集只记录条数

### 5.2. Domain层
*   只记录核心业务规则验证和状态转换的 **DEBUG** 级别日志
*   业务规则验证失败记录为 **WARN** 或 **ERROR**（取决于严重程度）

### 5.3. Service层
*   方法入口记录 **DEBUG** 级别，包含关键参数
*   业务处理失败记录为 **ERROR** 级别
*   完整记录错误信息和上下文，便于排查

### 5.4. Controller层
*   请求入口和出口记录为 **DEBUG** 级别
*   记录请求耗时、状态码和关键参数（注意脱敏）
*   框架或参数绑定异常记录为 **ERROR** 级别

### 5.5. 中间件层
*   记录请求路径、方法、客户端信息等基础信息为 **DEBUG** 级别
*   记录认证授权结果
*   异常请求（如限流、无权限）记录为 **WARN** 级别

## 6. 日志使用最佳实践

### 6.1. 使用方式
项目中记录日志时，应遵循以下模式：

```go
// 导入
import "app/core/loger/logger_new"

// 创建logger实例
logger := logger_new.New("模块名")

// 记录不同级别日志
logger.Debug("这是一条调试信息", logger_new.Fields{
    "key1": value1,
    "key2": value2,
})

// 错误日志应包含错误对象
if err != nil {
    logger.Error("操作失败", logger_new.Fields{
        "error": err,
        "param": param,
    })
}

// 包含链路追踪ID
logger.WithContext(ctx).Debug("带上下文的日志")
```

### 6.2. 何时记录日志
*   **应记录的情况**:
    *   服务启动和初始化
    *   关键业务流程的节点
    *   异常和错误情况
    *   性能关键点（耗时操作前后）
    *   安全相关事件
    *   系统状态变更

*   **不应记录的情况**:
    *   循环中的高频重复信息
    *   对象的完整内容（尤其是大对象）
    *   敏感个人信息
    *   无实际价值的"噪音"信息
    *   已在上层捕获并记录的异常

### 6.3. 避免常见问题
*   **避免日志爆炸**: 在循环或高频调用中谨慎记录日志
*   **防止资源泄露**: 确保日志组件正确初始化和关闭
*   **注意日志性能**: 在热点代码路径上最小化日志开销
*   **处理日志异常**: 日志记录本身的错误不应影响业务流程

## 7. 日志配置

### 7.1. 日志级别配置
*   默认级别为 **DEBUG**
*   生产环境可根据需要调整为更高级别
*   支持动态调整日志级别（无需重启服务）

### 7.2. 文件滚动配置
项目使用 `github.com/lestrrat-go/file-rotatelogs` 进行日志文件管理：

*   **按时间滚动**: 默认每日滚动一次
*   **按大小滚动**: 单个文件超过指定大小（如1GB）时滚动
*   **保留策略**: 默认保留30天的日志文件
*   **压缩策略**: 可配置对历史日志进行压缩

### 7.3. 采样配置
对于高频日志，可启用采样功能减少日志量：

*   初始N条保持完整记录
*   后续每M条记录1条
*   ERROR及以上级别不参与采样，始终完整记录

## 8. 日志监控与分析

### 8.1. 日志聚合
*   使用 ELK(Elasticsearch, Logstash, Kibana) 或类似系统进行日志聚合
*   确保不同服务的日志格式统一，便于集中检索
*   实现基于 TraceID 的分布式调用链跟踪

### 8.2. 告警机制
*   ERROR 级别日志超过阈值时触发告警
*   FATAL 级别日志立即触发高优先级告警
*   定期汇总 WARN 级别日志分析潜在问题

### 8.3. 日志分析
*   定期分析日志中的异常模式
*   监控关键操作的性能指标
*   通过日志分析识别系统瓶颈和优化机会

## 9. 与链路追踪集成

### 9.1. Zipkin集成
项目已集成 Zipkin (3.4.3) 进行分布式链路追踪：

*   日志中 TraceID 与 Zipkin 追踪 ID 保持一致
*   关键日志点应同时记录 Zipkin span 事件
*   通过 TraceID 可在 Zipkin 界面查看完整调用链

### 9.2. 使用方式
```go
// 从context中获取链路信息并记录日志
logger.WithContext(ctx).Debug("处理请求")

// 或手动指定TraceID
logger.WithField("trace_id", getTraceIDFromContext(ctx)).Debug("处理请求")
```

## 10. 敏感信息处理

### 10.1. 数据脱敏规则
对以下字段进行自动脱敏处理：

*   **手机号**: 仅显示前3位和后4位，如 `138****1234`
*   **身份证**: 仅显示前6位和最后4位，如 `110101********1234`
*   **银行卡**: 仅显示后4位，如 `************1234`
*   **密码/Token**: 完全不显示，替换为 `[REDACTED]`
*   **邮箱地址**: 仅显示用户名首尾字符和域名，如 `j***<EMAIL>`

### 10.2. 实现方式
```go
// 使用工具函数进行脱敏
logger.Debug("用户信息", logger_new.Fields{
    "phone": utils.MaskPhone(phone),
    "email": utils.MaskEmail(email),
})

// 或使用自动脱敏字段
logger.Debug("用户信息", logger_new.SensitiveFields{
    "phone": phone,  // 自动进行脱敏
    "email": email,  // 自动进行脱敏
})
```

## 11. 日志规范合规检查

### 11.1. 代码审查检查点
*   是否使用了 `app/core/loger/logger_new` 而非其他日志库
*   日志级别是否合理（默认使用 DEBUG 而非 INFO）
*   是否包含必要上下文信息
*   敏感信息是否已脱敏
*   错误日志是否包含完整错误信息

### 11.2. 自动化检查
*   使用静态代码分析工具检查日志调用规范
*   CI 流程中检查日志敏感信息泄露
*   定期扫描日志文件中的敏感信息模式

## 12. 最佳实践总结

*   使用统一的日志库 `app/core/loger/logger_new`
*   默认使用 DEBUG 级别，避免使用 INFO 级别
*   始终包含请求ID (TraceID) 等上下文信息
*   错误日志必须包含详细错误信息
*   严格进行敏感数据脱敏
*   合理配置日志级别和滚动策略
*   集成 Zipkin 实现全链路追踪
*   定期进行日志分析和优化
*   日志信息应简洁明了，避免冗余
*   确保日志内容对问题排查有实际帮助

---
*本规范将根据项目实践持续更新。*

