# 运营工具_老师&学生课程/题目反馈**相关文档**

多维表格：[用户反馈管理（AI 打标）](https://wcng60ba718p.feishu.cn/wiki/IUXzwxlnwibh0GkKVKGc46K6nzc?table=tblv2bV4BYvzjYXQ&view=vewhAzoAcq)
多维表格接入技术说明：
[https://open.feishu.cn/document/server-docs/docs/bitable-v1/bitable-overview](https://open.feishu.cn/document/server-docs/docs/bitable-v1/bitable-overview)
[https://open.feishu.cn/document/introduction-2#6b060ed7](https://open.feishu.cn/document/introduction-2#6b060ed7)
**版本管理**

<table>
<tr>
<td>版本号<br/></td><td>日期<br/></td><td>变更人<br/></td><td>变更类型<br/></td><td>变更详情<br/></td></tr>
<tr>
<td>V1.0.0<br/></td><td>4-30<br/></td><td>袁全<br/></td><td>新建<br/></td><td>新建文档<br/></td></tr>
<tr>
<td>V5.0.0<br/></td><td>5-13<br/></td><td>袁全<br/></td><td>升稿<br/></td><td>完成核心页面交互图和prd说明<br/></td></tr>
<tr>
<td>V9.0.0<br/></td><td>5-22<br/></td><td>袁全<br/></td><td>升稿<br/></td><td>完成详细prd 和 修改各元素的影响说明<br/>加入多维表格的技术实现文档说明<br/></td></tr>
<tr>
<td><br/></td><td><br/></td><td><br/></td><td><br/></td><td><br/></td></tr>
</table>

**设计稿**
学生端&老师端 UI：

### 一、背景和目标

#### 1.1、需求背景

本期课程生产方式对比之前有较大的变化，为了让 6 月及之后的用户对课程内容有更多的反馈途径，将“用户对课程内容挑错”当成是帮助产品变得更好的驱动力。本期区分以下角色，产品目标如下：

1. 对用户：1. 支持教师端&学生端用户，快速提交课程问题；
2. 完成修改后，为老师/教师下发积分或奖励，提升用户产品参与感，减化用户对内容 bug 的反感
3. 对系统：1. 通过:收集-> 快速解决问题，实现优化课程，快速提升用户对课程内容的体验
4. 使用 Agent 自动归类用户反馈，并在后续流程中通过人工打标签提升模型对好课程的要求

<table>
<tr>
<td><br/></td><td>学生端<br/></td><td>教师端<br/></td><td>产课后台<br/></td><td>智能审核Agent<br/></td></tr>
<tr>
<td>P0核心功能<br/></td><td>- 问题反馈<br/>- 消息通知：反馈解决<br/></td><td>- 问题反馈<br/>- 消息通知：反馈解决<br/>- 个人中心：下发积分<br/></td><td>用户反馈统计表单<br/>课程结算页统计表单<br/></td><td>agent工作流<br/>人工复核纠正<br/></td></tr>
<tr>
<td>未来功能<br/></td><td><br/></td><td>- 积分历史&消费<br/></td><td><br/></td><td><br/></td></tr>
</table>

#### 1.2、项目收益

1. 课程上线后完成：学生反馈 & 教师反馈功能；快速解决问题，可快速提升课程质量
2. 发现课程问题，老师有积分可积累：提升老师对课程的满意度 和 系统使用积极性

#### 1.3、覆盖用户

反馈人：老师端和学生端用户
改内容：教研教师、总部教研管理老师
积分：运营下发奖励

#### 1.4、方案简述

**核心流程**
**1.课程 bug 反馈、处理和消息回复**
![G1epwigmBhGTGobMsA5c3QFyn1H](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_G1epwigmBhGTGobMsA5c3QFyn1H.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】

该图片展示了教研产品中教师反馈 Bug 的处理全流程，包含三个主要阶段：

1.  **教师端提交bug反馈**
    *   **课程反馈**:
        *   在“AI课稿件播放页”中，教师可进行课程内容相关的反馈。
        *   点击“反馈”后，进入“播放组件页”提交具体反馈内容。
    *   **题目反馈**:
        *   在题目展示界面（如图中“页面示例（习题摘要、作答分析）”），教师可针对具体题目进行反馈。
        *   点击“评价”后，进入“答题组件页”提交具体反馈内容。

2.  **产课后台check后变更标签状态，人工点“发送”**
    此阶段为后台处理流程，使用Mermaid流程图描述如下：
    ```mermaid
    graph TD
        A[教师从端提交bug] --> B(bug反馈统计表);
        B --> C{PO初期筛选/人工check};
        C -- 问题有效 --> D["根据问题类型分类<br/>(如:工具问题/知识问题/内容优化等)"];
        D --> E[问题修改后重新上线];
        E --> F[标签状态变更];
        F --> G["人工点'发送'"];
        G --> H[教师收到消息和积分];
        C -- 问题无效 --> I[无效问题];
        I --> J[状态变更为'无效'];
        J --> K["人工点'发送'"];
        K --> L[教师收到消息];
    ```
    *   **关键节点**:
        *   **bug反馈统计表**: 后台汇总教师提交的Bug。
        *   **人工check (PO初期筛选)**: 产品运营或相关人员对Bug进行初步筛选和有效性判断。
        *   **标签状态变更**: 根据Bug的有效性和处理阶段，变更其状态标签。
        *   **人工点“发送”**: 手动触发通知，将处理结果反馈给教师。
    
3.  **用户收到回复消息和积分**
    *   **老师端：消息中心**:
        *   教师在此处收到关于其提交Bug处理结果的通知消息。
        *   消息内容示例：“您于XXXX年XX月XX日反馈的[意见反馈类型]我们已经收到并处理...”。
    *   **教师信息/积分**:
        *   若Bug有效并处理完毕，教师会获得相应积分奖励。
        *   “教师信息”页面展示教师的“当前积分”和“积分记录”。
        *   “为你精选”可能为积分兑换或其他推荐内容区域。

【============== 图片解析 END ==============】


**2.建议反馈、处理 和消息回复**
![Uai1wr7dkh0w9CbKA7FcfHGYnqf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_Uai1wr7dkh0w9CbKA7FcfHGYnqf.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了用户反馈处理的三个主要阶段及其对应的用户界面。

### 1. 用户提交反馈

此阶段展示用户在产品界面提交反馈的场景。

*   **左侧界面 (导航与入口):**
    *   顶部显示 "产品标识" 和 "早, 杨老师"。
    *   左侧导航栏包含："课程"、"作业"、"用户反馈" (高亮)、"个人中心"、"数据管理"、"公告"、"退出登录"。
    *   主区域显示一个2025年6月的日历视图，其中包含课程安排。
    *   用户通过点击左侧导航栏的 "用户反馈" 进入反馈提交界面。
*   **右侧界面 (用户反馈表单):**
    *   标题: "用户反馈"。
    *   副标题: "欢迎与我们反馈您的问题"。
    *   **问题描述\***: 必填项，文本输入框，提示 "请输入10个字以上的问题描述以便我们提供更好的帮助"，字符限制 "0/2000"。
    *   **上传截图 (选填) (0/4)**: 允许用户上传最多4张图片，有 "+" 图标和 "上传图片" 文字提示。
    *   **提交按钮**: 用于提交反馈内容。

### 2. 产课后台check后变更标签状态，人工点“发送”

此阶段展示产课后台管理人员处理用户反馈的界面和操作。这是一个表格形式的后台管理系统。

*   **表格列包括:**
    *   **课程id/题目id**
    *   **机器审核结果**:
        *   有效/无效
        *   PO:有问题找出来了(准确率)
        *   P1:无问题实际有问题(召回率)
    *   **人工check结果**:
        *   1.采纳-有效问题: 修改url (图片中显示更细致的选项，如 "逐字稿工具: (按修改url")
        *   2.采纳-无效问题:
        *   3.不采纳-无效问题:
        *   4.模型遗漏-有效问题:
    *   **机器返回的信息**:
        *   1.问题1:问题说明 怎么改
        *   2.问题2:问题说明 怎么改
    *   **审核标签**:
        *   1.有效反馈:
            *   (1) 知识提问
            *   (2) 内容优化
            *   (3) 内容错误
        *   2.无效反馈
    *   **人工check问题模块及url**:
        *   板书文字: 逐字稿-板书
        *   配音: 朗读稿
        *   字幕: 逐字稿
        *   板书图画: 逐字稿-板书/人工圈
        *   讲师: 数字人
        *   讲解: 待升级
    *   **当前进展**: (例如：0.设计中, 1.开发中, 2.生产中, 3.已上线)
    *   **问题优先级**
    *   **派发生产人**
    *   **审核人**
*   **操作流程暗示**: 后台人员根据机器审核结果和人工检查，对反馈进行分类、打标签，变更状态，并最终人工点击（未在截图中直接显示“发送”按钮，但由标题指明）发送处理结果。

### 3. 用户收到回复消息

此阶段展示用户在产品内收到反馈处理结果通知的界面。

*   **界面布局**:
    *   顶部显示 "产品标识"，时间 "9:30 AM"，电量 "80%"。
    *   消息提示: "消息 共2条, 0条未读"。
    *   左侧导航栏: "布置"、"课程"、"作业"、"资源"、"学情"、"社区"。
*   **消息内容示例**:
    *   **已采纳消息**: "您在“5月16日 10:31”的反馈被采纳了! 我们已完成上线修改, 敬请关注。再次感谢您的支持~ 2025年5月16日 13:23"
    *   **未采纳消息**: "您在“5月15日 18:31”的反馈暂未被采纳, 我们将在后续版本中考虑优化。再次感谢您的支持~ 2025年5月16日 09:23"

### 整体流程图

```mermaid
flowchart TD
    A["用户提交反馈"] --> B["产课后台处理"];
    B -- Check --> C[变更标签状态];
    C -- 人工操作 --> D[点击“发送”];
    D --> E["用户收到回复消息"];
```

【============== 图片解析 END ==============】


**涉及角色**

- **反馈人：学生、老师**
- **内容生产人：用户反馈审核后台**（总部管理老师 / 审核老师）
- **分类 Agent：**自动归类、初筛问题 + 人工复核优化 agent

**整体流程**
**1.反馈人：学生/老师端 反馈问题**

- 发现：课程问题（字幕、配音、板书错误等）、题目问题、MV 视频问题
- 快速反馈（点选选项 + 说明文字 + 系统自动截图）

| 模块         | 说明                               | 细节设计                                                       |
| ------------ | ---------------------------------- | -------------------------------------------------------------- |
| 反馈入口     | 在课程页、答题页：增加【反馈按钮】 | 小按钮悬浮，不影响体验                                         |
| 反馈表单     | 根据当前页面引导式收集问题信息     | 问题点选（1或多个），文字 描述（非必填），截图上传（自动完成） |
| 提交成功提示 | 简洁toast反馈                      | “感谢你的反馈，我们会尽快处理！”                               |

  **2.反馈收集后台：Agent 分类问题**
- 接收问题数据
- 对于涉及文字类课程内容问题的（逐字稿、板书文字、字幕），调用 Agent 系统判定反馈是否合理 & 初步判断严重程度：

> 反馈合理/不合理，优先级：高
>
> - 建议采纳问题 1:字幕问题  - 问题说明：xx 分 xx 秒 字幕 xxxxx；

- 改动方案：修改为 xxxxx，使用逐字稿工具（url=xxx）

> - 建议采纳问题 2:板书问题 - 问题说明：Part1 xx 分 xx 秒 出现的板书为 xxxxxx；

- 改动方案：修改为 xxxxx，使用逐字稿工具（url=xxx）

| 模块          | 说明                     | 细节设计                                 |
| ------------- | ------------------------ | ---------------------------------------- |
| 反馈列表      | 所有反馈记录展示         | 状态（新提交/处理中/已完成），类型，时间 |
| Agent归类模块 | 自动分类并打标签         | NLP归类，关键字提取，优先级评分          |
| 审核分发模块  | 将任务派发给人工老师审核 | 可以根据科目、课程自动分配               |

**3.人工审核 Agent 判定结果、修改并审核后上线**

- 优先看 Agent 判定“反馈有效”的反馈，逐条确认 Agent 的判定结果是否可采纳：1#.采纳-有效问题：有问题找出来了、2#.不采纳-无效问题：没问题找出来了
- 再看其他 Agent 判定“反馈无效”的反馈，确认 Agent 的判定结果是否可采纳：3#.采纳-无效问题：无问题实际没问题、4#.模型遗漏-有效问题
- 人工进入修改：进入 AI 课生产环境，修改对应问题（字幕、板书、逐字稿）
- 审核后，发布修改到线上：总部工作人员审核修改完成后，提交修改上线
- 回复用户结果：回复至用户消息中心：反馈采纳，已上线 / 稍后处理

| 模块                           | 说明                             | 细节设计                    |
| ------------------------------ | -------------------------------- | --------------------------- |
| 归类模型	文本分类+图像识别模型 | 预训练模型，或者自定义小型分类器 |
| 优先级评估                     | 快速筛选高危问题                 | 如影响理解的内容错误优先    |
| 预处理推荐                     | 给审核老师推荐初步修改意见       | 例如“建议删除错误句子第2段” |

  **4.反馈人：收到系统通知和奖励下发**
- 通知：问题处理后通知反馈者结果（增强学生/老师参与感）
- 奖励：积分下发至对应老师的账户

| 模块           | 说明                       | 细节设计                                             |
| -------------- | -------------------------- | ---------------------------------------------------- |
| 消息通知       | 下发消息给教师端           | 教师端消息中心回复                                   |
| 积分下发（P2） | 下发积分到教师账号         |
| 奖品下发（P2） | 扣减积分，完成实物奖品下发 | 客服联系教师后，核对确认下发奖励，并扣除账户中的积分 |

### **二、名词说明**

**已对齐，上架后修改当前 id 课程的预期**
一个课程 id 在上架后，在用户报错情况下，会有以下两类修改课程的情况

1. **「纠错类」**修改，目的是让新、老用户都能看到修改结果
2. **「升级类」**修改，目的是让新用户能看到最新用户，并通知老用户有新课可以看
   说明：

<table>
<tr>
<td>case<br/></td><td>反馈模块<br/></td><td>修改分类<br/></td><td>优先级<br/></td><td>待修改元素<br/></td><td>教研老师修改工具<br/></td></tr>
<tr>
<td>1<br/></td><td>课程：MV视频有错<br/></td><td>纠错类<br/></td><td>P0<br/></td><td>视频内容<br/></td><td>配课工具：视频工具：重新上传视频<br/></td></tr>
<tr>
<td>2<br/></td><td>课程：书写圈画有错<br/></td><td>纠错类<br/></td><td>P0<br/></td><td>人工圈画视频<br/></td><td>圈画工具<br/></td></tr>
<tr>
<td>3<br/></td><td>课程：板书文字、图片、圈画有错<br/></td><td>纠错类<br/></td><td>P0<br/></td><td>板书内容<br/></td><td>逐字稿-改bug模式：板书更新工具+圈画工具：提示重录<br/></td></tr>
<tr>
<td>4<br/></td><td>课程：单句字幕有错字<br/></td><td>纠错类<br/></td><td>P0<br/></td><td>单句字幕<br/></td><td>逐字稿-改bug模式：字幕-单句编辑工具<br/></td></tr>
<tr>
<td>5<br/></td><td>课程：单句配音有错、音画不同步<br/></td><td>纠错类<br/></td><td>P0<br/></td><td>单句音频<br/></td><td>逐字稿-改bug模式：朗读稿-单句上传工具<br/></td></tr>
<tr>
<td>6<br/></td><td>课程：大段配音有问题、音画不同步<br/></td><td>纠错类<br/></td><td>P1<br/></td><td>音频时间轴<br/></td><td>逐字稿：朗读稿编辑工具，时间轴变化将引起整个part重录<br/></td></tr>
<tr>
<td>7<br/></td><td>课程：Part内容修改、删减<br/></td><td>优化类<br/></td><td>P0<br/></td><td>调整标题、顺序、内容删减<br/></td><td>逐字稿：part编辑工具<br/></td></tr>
<tr>
<td>8<br/></td><td>课程：讲师有问题<br/></td><td>纠错类<br/></td><td>P2<br/></td><td>数字人视频<br/></td><td>暂无，需算法介入<br/></td></tr>
<tr>
<td>9<br/></td><td>课程/作业xx题目有错（不需重新批改）<br/></td><td>纠错类<br/></td><td>P0<br/></td><td>题目内容<br/></td><td>题库直接修改这个id题目，经审核，一键发布至所有引用的课程<br/></td></tr>
<tr>
<td>10<br/></td><td>课程/作业xx题目有错（需重新批改）<br/></td><td>纠错类<br/></td><td>P2<br/></td><td>题目内容、学生作答<br/></td><td>题库修改 + 发起重新批改，完成后同步至老师端 & 学生端<br/></td></tr>
<tr>
<td>11<br/></td><td>课程/作业xx题目太难<br/></td><td>优化类<br/></td><td>P0<br/></td><td>题目id、题目数量减少<br/></td><td>配课工具：模版工具：修改题目id<br/></td></tr>
</table>

### 三、业务流程

根据和教研老师核对，本期问题反馈主要归为以下 11 类：ou_42cd533b7895cc063c24e710c4b5727b

- 前 9#是内容反馈，走教研审核 + 生产流程
- 第 10#为功能 bug 反馈，走技术 oncall 流程
- 第 11#为功能建议和优化，走产品问题评估流程

<table>
<tr>
<td>序号<br/></td><td>用户已选分类<br/></td><td>所属分类<br/></td><td>流程<br/></td><td>有效反馈优先级<br/></td></tr>
<tr>
<td>1<br/></td><td>板书文字问题<br/></td><td>文字类内容反馈<br/></td><td>dify机审流程1<br/></td><td>P0<br/></td></tr>
<tr>
<td>2<br/></td><td>字幕问题<br/></td><td>文字类内容反馈<br/></td><td>dify机审流程2<br/></td><td>P0<br/></td></tr>
<tr>
<td>3<br/></td><td>题目问题（题干/选项/解析）<br/></td><td>文字类内容反馈<br/></td><td>dify机审流程3<br/></td><td>P0<br/></td></tr>
<tr>
<td>4<br/></td><td>板书图片/圈画问题<br/></td><td>非文字类内容反馈<br/></td><td>教研人审<br/></td><td>P0<br/></td></tr>
<tr>
<td>5<br/></td><td>配音问题<br/></td><td>非文字类内容反馈<br/></td><td>教研人审<br/></td><td>P1<br/></td></tr>
<tr>
<td>6<br/></td><td>音画不同步<br/></td><td>非文字类内容反馈<br/></td><td>教研人审<br/></td><td>P1<br/></td></tr>
<tr>
<td>7<br/></td><td>题目（配图问题）<br/></td><td>非文字类内容反馈<br/></td><td>教研人审<br/></td><td>P0<br/></td></tr>
<tr>
<td>8<br/></td><td>视频问题（全部子类）<br/></td><td>非文字类内容反馈<br/></td><td>教研人审<br/></td><td>P0<br/></td></tr>
<tr>
<td>9<br/></td><td>超纲、听不懂、太难了<br/></td><td>非文字类内容反馈<br/></td><td>教研人审<br/></td><td>P1<br/></td></tr>
<tr>
<td>10<br/></td><td>黑屏、卡顿<br/></td><td>功能问题<br/></td><td>技术oncall流程<br/></td><td>P0<br/></td></tr>
<tr>
<td>11<br/></td><td>其他<br/></td><td>功能建议和优化<br/></td><td>产品问题评估<br/></td><td>P1<br/></td></tr>
</table>

**老师&学生：bug 反馈****-核心工作流**
![XvhJw45DEhG2GxbEGVKcxGDfnBe](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_XvhJw45DEhG2GxbEGVKcxGDfnBe.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】

```mermaid
flowchart TD
    subgraph 学生端
        S_Entry["「报告结算页」&「答疑页」<br>点击反馈<br>(AI课内容问题, 题目问题, 功能bug/优化建议)"]
    end

    subgraph 教师端
        T_Entry["「AI课预览播放」&「答疑页」<br>点击反馈<br>(AI课内容问题, 题目问题, 功能bug/优化建议)"]
    end

    subgraph feedback_backend ["反馈收集后台 (多维表格实现)"]
        direction LR
        FB_DataTable["多维表格：<br>反馈收集数据表"]
        FB_IsTextOnly{"是否仅包含文本<br>类反馈？"}
        FB_RunAgent["跑Agent"]
        FB_ManualCheckForNonText["派给人工检查和修改"]
        FB_IsValid{"是否判定为有效<br>反馈？"}
        FB_P2Priority["否，P2优先级"]
        FB_CompleteTicket["完成工单：<br>发送消息通知"]

        S_Entry --> FB_DataTable
        T_Entry --> FB_DataTable
        FB_DataTable --> FB_IsTextOnly
        FB_IsTextOnly -- 是 --> FB_RunAgent
        FB_IsTextOnly -- 否 --> FB_ManualCheckForNonText
        FB_RunAgent --> FB_IsValid
        FB_IsValid -- "否，P2优先级" ----> FB_CompleteTicket
    end

    subgraph 教研生产人 ["教研生产人 (飞书通知, 产课后台改)"]
        direction LR
        JR_ManualCheckTagModify["派给人工检查打标<br>& 完成修改"]
        JR_HQDeploy["总部管理：<br>对修改上线"]

        FB_IsValid -- "是，P0派人工核对" --> JR_ManualCheckTagModify
        FB_ManualCheckForNonText --> JR_ManualCheckTagModify
        JR_ManualCheckTagModify --> JR_HQDeploy
        JR_HQDeploy --> FB_CompleteTicket
    end

    subgraph 技术 ["技术 (飞书oncall拉群)"]
        direction LR
        Tech_AssignOncall["派给技术oncall"]
        Tech_FixDev["技术结合报错信息<br>拉群修改"]
        Tech_Deploy["技术：对修改上线"]

        FB_DataTable -- "包含：黑屏/卡顿<br>功能bug" --> Tech_AssignOncall
        Tech_AssignOncall --> Tech_FixDev
        Tech_FixDev --> Tech_Deploy
        Tech_Deploy --> FB_CompleteTicket
    end

    subgraph 产品 ["产品 (飞书通知)"]
        direction LR
        Prod_AssignEval["派给产品评估"]
        Prod_IsBug{"是否包含bug<br>反馈？"}
        Prod_AddToPool["加入需求池"]

        FB_DataTable -- "包含：反馈其他建议<br>与意见" --> Prod_AssignEval
        Prod_AssignEval --> Prod_IsBug
        Prod_IsBug -- 是 --> Tech_AssignOncall
        Prod_IsBug -- 否 --> Prod_AddToPool
        Prod_AddToPool -.-> FB_CompleteTicket
    end

    subgraph 用户通知与记录
        direction LR
        S_MsgCenter["消息中心"]
        S_UserCenter["用户中心：<br>积分变化"]
    end

    FB_CompleteTicket --> S_MsgCenter
    FB_CompleteTicket --> S_UserCenter

```

【============== 图片解析 END ==============】

**多维表格的 bug 流转-工作流**
![GcD1wBDI7hzvdcbsZ12crtcMnEh](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_GcD1wBDI7hzvdcbsZ12crtcMnEh.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】
```mermaid
flowchart TD
    A[老师端数据进入反馈表] --> B[获取「反馈内容分类」]
    B --> C[获取「核心信息」: 不可编辑<br/>「反馈原文」+「截图」+「模块」+<br/>「学段学科」+「课程id/题目id」+<br/>「part几」+「三级标题名称」]
    C --> D[获取「反馈人信息」: 不可编辑<br/>来源客户端+来源学校+地区+年级+班级+<br/>姓名+反馈人id+设备型号+OS版本]

    D --> E{仅包含1~3文字<br/>内容反馈}
    E -- 是 --> F_auto[自动更新:<br/>是否过agent:是<br/>进展:机器评估中]
    F_auto --> F1[1.板书文字问题<br/>dify工作流]
    F_auto --> F2[2.字幕文字问题<br/>dify工作流]
    F_auto --> F3[题目文字问题<br/>dify工作流]
    
    F1 --> G_auto_done[完成, 自动更新]
    F2 --> G_auto_done
    F3 --> G_auto_done
    
    G_auto_done --> H_machine_eval["机器分类结果: xxx<br/>机器分类详情: xxx<br/>优先级: PO(有效)/P3(机器判定无效)<br/>进展:人工评估中"]

    E -- 否 --> I1{是否包含4~9非<br/>文字内容反馈}
    I1 -- 是 --> J1_manual[更新:<br/>优先级<br/>是否过agent:否<br/>进展:人工评估中]
    J1_manual --> L_direct_manual[直接人工评估]

    I1 -- 否 --> I2{是否包含“黑<br/>屏、卡顿”反馈}
    I2 -- 是 --> J2_manual[更新:<br/>优先级P0<br/>是否过agent:否<br/>进展:人工评估中]
    J2_manual --> L_direct_manual

    I2 -- 否 --> I3{是否包含“其<br/>他”反馈}
    I3 -- 是 --> J3_manual[更新:<br/>是否过agent:否<br/>进展:人工评估中]
    J3_manual --> L_direct_manual
    
    H_machine_eval --> M_trigger_feishu[触发飞书通知工作流]
    L_direct_manual --> M_trigger_feishu

    M_trigger_feishu --> N1[对应「学科学段」<br/>对接生产人收到通知]
    M_trigger_feishu --> N2[对应「来源客户端」<br/>飞书机器人拉oncall信息群<br/>附:反馈详情url<br/>对接研发收到通知]
    M_trigger_feishu --> N3[对应「来源客户端」<br/>对接PM收到通知<br/>附:反馈详情url]

    H_machine_eval --> O_manual_check[人工更新: check机器结果]
    O_manual_check --> P_compare[人机对比结果: xxx<br/>优先级: 更新]
    P_compare --> Q_auto_adopt{自动更新<br/>结果: 是否采纳}

    L_direct_manual --> R_manual_locate[完成问题定位和分类, 手动更改]
    R_manual_locate --> S_manual_click{人工点击<br/>结果: 是否采纳}

    Q_auto_adopt -- 未采纳 --> T_no_modify["完成补充: 结论说明<br/>自动变更进展: 无需修改(完结)"]
    S_manual_click -- 未采纳 --> T_no_modify

    Q_auto_adopt -- 已采纳 --> U1["在生产环境完成修改<br/>人工更新进展: 已上线(完结)<br/>结论说明(人工补充)"]
    Q_auto_adopt -- 已采纳 --> U2["在产课平台完成修改<br/>人工更新进展: 已上线(完结)<br/>结论说明(人工补充)"]

    S_manual_click -- 已采纳 --> U1
    S_manual_click -- 已采纳 --> U2

    T_no_modify --> V_message_center[「教师端」-<br/>「消息中心」]
    U1 --> V_message_center
    U2 --> V_message_center
    
    T_no_modify --> W_auto_send[自动发送消息]
    U1 --> W_auto_send
    U2 --> W_auto_send
```
【============== 图片解析 END ==============】



**Agent-工作流**
![IEeEw1Wwwhrfw3b5xxacRvbrn4U](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_IEeEw1Wwwhrfw3b5xxacRvbrn4U.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】

该图片描绘了互联网教育产品中用户反馈问题的处理流程。

1.  **反馈提交与信息搜集**：
    *   流程始于用户提交反馈。
    *   服务端记录反馈详情，包括：反馈已选分类、反馈文字说明、反馈截图、反馈入口模块、学段学科以及关键的 `课程id-part几/题目id`。
    *   客户端同时获取并记录用户设备与环境信息，包括：当前设备型号、os版本、用户id。

2.  **内容核查与初步分类**：
    *   系统利用服务端记录的 `课程id-part几/题目id`，调用查询API获取相关原始内容（板书、字幕、题目+题干+解析）。
    *   结合用户反馈信息和API查询结果，将问题初步判定为以下三类之一：
        *   `dify1: 板书文字有误`
        *   `dify2: 字幕有误`
        *   `dify3: 题目有误`

3.  **标准化纠错处理**：
    *   不同类型的错误反馈在经过学段学科区分后，汇总进入统一的纠错工作流。
    *   纠错工作流产出详细的分析结论，包括：
        *   判定是否有误。
        *   错误详情：如总错误数量，以及具体每处错误的性质（如事实错误、逻辑错误）、原文内容对比、有误内容说明。

4.  **结果反馈**：
    *   最终，纠错工作流的处理结果将返回。

以下是该流程的Mermaid图表表示：

```mermaid
flowchart TD
    A([用户提交反馈]) --> B["服务端返回<br/>- 反馈已选分类<br/>- 反馈文字说明<br/>- 反馈截图<br/>- 反馈入口模块<br/>- 学段学科<br/>- 课程id-part几/题目id"]
    A --> C["客户端获取<br/>- 当前设备型号<br/>- os版本<br/>- 用户id"]

    B -- "为课程id-part几/题目id" --> D["提供查询API<br/>- 板书查询（查看态）<br/>- 字幕查询<br/>- 题目+题干+解析查询"]
    
    B --> E["dify1:<br/>板书文字有误"]
    B --> F["dify2:<br/>字幕有误"]
    B --> G["dify3:<br/>题目有误"]

    D -.-> E
    D -.-> F
    D -.-> G
    
    E --> H_Process["区分学段学科"]
    F --> H_Process
    G --> H_Process
    
    H_Process --> I["纠错工作流<br/>1. 输出结论：是/否有误,<br/>2. 详情：共有x处错误；<br/>   错误1: 属于事实错误，板书原文xxxx，有误内容xx<br/>   错误2: 属于逻辑错误，板书原文xxxx，有误内容xx"]
    I --> J([返回结果])
```

【============== 图片解析 END ==============】



### 四、需求概览

<table>
<tr>
<td>序号<br/></td><td>需求名称<br/></td><td>内容概述<br/></td><td>优先级<br/></td></tr>
<tr>
<td>1<br/></td><td>老师反馈<br/></td><td>- 问题反馈：课程问题、题目问题、mv问题、其他意见和建议<br/>- 消息通知<br/>- 积分展示&历史：p2<br/></td><td>P0<br/></td></tr>
<tr>
<td>2<br/></td><td>学生反馈<br/></td><td>- 问题反馈：课程问题、题目问题、其他意见和建议<br/></td><td>P0<br/></td></tr>
<tr>
<td>3<br/></td><td>用户需求反馈池<br/></td><td>- 生产后台1：问题和意见反馈看板<br/>- 生产后台2：课程结算页反馈看板<br/></td><td>P0<br/></td></tr>
<tr>
<td>4<br/></td><td>智能agent<br/></td><td>- 区分：板书文字问题、字幕问题、题目问题，构建3个dify工作流<br/></td><td>P1<br/></td></tr>
</table>

### 五、详细产品方案

#### **5.1 教师端：用户反馈、消息通知、积分下发**

<table>
<tr>
<td>序号<br/></td><td>模块<br/></td><td>功能名称<br/></td><td>功能概述<br/></td><td>图<br/></td><td>优先级<br/></td></tr>
<tr>
<td>1<br/><br/></td><td>bug反馈<br/></td><td>**课程反馈**<br/></td><td>- 「AI课预览页」-「课程播放」模块：展示课程页的悬浮【反馈】入口。<br/>- 反馈内容：- *选择1到多个问题类型（必填）：- 板书文字问题、板书图片问题、板书圈画问题、配音问题、字幕问题、音画不同步- 讲解超纲、听不懂太难了- 黑屏、播放卡顿- 其他- 可填写详细问题描述（<=300字）- 点击“提交”后，同步上传下列信息到产课反馈池：- 上传上述用户已选：问题分类、反馈原文内容、反馈时间- 上传课程信息：- 反馈类型：Ai课内容问题- 反馈模块名称：Ai课-课- 当前页面截图（含有时间轴） - 当前课程id- 当前学科名称- 上传当前用户和设备信息：- 反馈用户类型：教师- 来源学校名称- 地区（学校所属：省/市/县）- 年级、班级、姓名、用户id- 设备型号、OS版本<br/>- 反馈成功：实现简单的提交成功提示。<br/></td><td>课程问题反馈入口：<br/>![in_table_BjY4bsSuroBB2BxP0Rncz23gnCd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BjY4bsSuroBB2BxP0Rncz23gnCd.png)<br/>反馈抽屉页：<br/>![in_table_AcdIb2YOhoX0gPxKwOacMzAonKe](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_AcdIb2YOhoX0gPxKwOacMzAonKe.png)<br/><br/></td><td>P0<br/></td></tr>
<tr>
<td>2<br/></td><td>bug反馈<br/></td><td>**题目反馈**<br/><br/></td><td>- 「AI课预览页」-「答题」或「题目列表」& 「学情报告」-「题目列表页 & 题目详情页」：展示【反馈】入口。<br/>- 反馈内容：- *选择1到多个问题类型（必填）：- 题干选项有误、答案有误、解析有误、配图有误- 题目超纲了、太难了、太简单了- 其他- 可填写详细问题描述（<=300字）- 点击“提交”后，同步上传下列信息到产课反馈池：- 上传上述用户已选：问题分类、反馈原文内容、反馈时间- 上传课程信息：- 反馈类型：Ai课内容问题（AI课中报错） 或 题内容问题（资源中心/学情报告 中报错）- 反馈模块名称：Ai课-题 或 资源中心/学情报告  - 当前页面截图（含有时间轴） - 当前题目id- 当前学段学科（例：高中数学）- 上传当前用户和设备信息：- 反馈用户类型：教师- 来源学校名称- 地区（学校所属：省/市/县）- 年级、班级、姓名、用户id- 设备型号、OS版本<br/>- 反馈成功：实现简单的提交成功提示。<br/></td><td>题目问题反馈入口：<br/>![in_table_I3Vvb6oFToy9T3xSU5ncOzdrn8c](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_I3Vvb6oFToy9T3xSU5ncOzdrn8c.png)![in_table_LlwubnXnwoVu1Rxr15xchWS3nob](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_LlwubnXnwoVu1Rxr15xchWS3nob.png)<br/>反馈抽屉页：<br/>![in_table_HcLHbVIZpoL1pIxVFU4caINUnjg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_HcLHbVIZpoL1pIxVFU4caINUnjg.png)<br/><br/></td><td>P0<br/></td></tr>
<tr>
<td>3<br/></td><td>bug反馈<br/></td><td>**视频MV反馈**<br/></td><td>- 「AI课预览页」-「MV」页的悬浮【反馈】入口。<br/>- 反馈内容：- *选择1到多个问题类型（必填）：- 知识讲解有误、和本课不相关、引起不适- 其他- 可填写详细问题描述（<=300字）- 点击“提交”后，同步上传下列信息到产课反馈池：- 上传上述用户已选：问题分类、反馈原文内容、反馈时间- 上传课程信息：- 反馈类型：Ai课内容问题- 反馈模块名称：Ai课-视频  - 当前页面截图（含有时间轴） - 当前题目id- 当前学段学科（例：高中数学）- 上传当前用户和设备信息：- 反馈用户类型：教师- 来源学校名称- 地区（学校所属：省/市/县）- 年级、班级、姓名、用户id- 设备型号、OS版本<br/>- 反馈成功：实现简单的提交成功提示。<br/></td><td>视频组件的反馈入口<br/>![in_table_INqfb2UipoAqF8xpxpec6WPHncf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_INqfb2UipoAqF8xpxpec6WPHncf.png)<br/>反馈抽屉页<br/>![in_table_FROrb9I0Io9PPVxdq3TcEZOanUb](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_FROrb9I0Io9PPVxdq3TcEZOanUb.png)<br/><br/></td><td>P0<br/></td></tr>
<tr>
<td>4<br/></td><td>建议反馈<br/></td><td>**其他建议反馈**<br/></td><td>- 教师端，点击头像，弹出的菜单中：- 展示「用户反馈」入口。菜单顺序：个人中心、消息中心、<u>用户反馈</u>、退出登录- 点击后，收起菜单，展示「用户反馈弹窗」<br/>- 反馈内容：- *需填写详细问题描述（<=300字）- 上传截图（选填，0/4）- 点击“提交”后，同步上传下列信息到产课反馈池：- 上传上述用户已选：反馈原文内容、上传截图、反馈时间- 上传当前用户和设备信息：- 反馈用户类型：教师- 来源学校名称- 地区（学校所属：省/市/县）- 年级、班级、姓名、用户id- 设备型号、OS版本<br/>- 反馈成功：实现简单的提交成功提示。<br/></td><td>其他反馈入口<br/>![in_table_ZBBKbAXu4oU6MYxjNqncHEqsnyb](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ZBBKbAXu4oU6MYxjNqncHEqsnyb.png)<br/>其他反馈页面<br/>![in_table_Ba11briOdoyyjVxQ1T1cru4Dn5g](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Ba11briOdoyyjVxQ1T1cru4Dn5g.png)<br/></td><td>P0<br/></td></tr>
<tr>
<td>5<br/></td><td>系统通知<br/></td><td>**消息中心 展示：通知**<br/><br/></td><td>- 在「生产后台 bug反馈看板」，点击「发送」的情况下，由后台（？）给教师端的「消息中心」发送一条消息：区分“已采纳”和“未采纳”两种状态，发送不同文案：- 已采纳：- ~~文案1（发放积分不为0）：~~- ~~您在“x月x日 hh:mm”关于“Ai课~~~~内容~~~~问题”的反馈被采纳了！我们将于24小时内完成上线修改，敬请关注。~~~~为表达我们的谢意，我们已为您发送~~~~150~~~~积分到您的积分账户。您可以前往「个人中心」查看。~~~~再次感谢您的支持！~~- 文案2（发放积分为0）:- 您在“x月x日 hh:mm”关于“Ai课<u>内容</u>问题”的反馈被采纳了！我们已完成了上线修改，敬请关注。再次感谢您的支持！- 未采纳：文案- 您在“x月x日 hh:mm”关于“Ai课<u>内容</u>问题”的反馈暂未被采纳，我们将在后续版本中进行优化。~~您可以加入~~~~xx~~~~学科微信群：~~~~xxxxx~~~~和我们进行更即时的交流沟通。~~再次感谢您的支持！- 边界case说明：若该手机号对应的用户就职于多所学校，这个消息中心是共同的，与学校无关，与手机号相关<br/>- 教师端，点击头像，弹出的菜单中：- 展示「消息中心」入口。菜单顺序：个人中心、<u>消息中心</u>、用户反馈、退出登录- 点击后，收起菜单，进入「消息中心」<br/>- 展示：- 消息未读/已读态- 消息按收件时间显示<br/></td><td>教师端-我的-消息中心<br/>![in_table_DAs7bArrqoITe8xorzjcqIxqnbd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_DAs7bArrqoITe8xorzjcqIxqnbd.png)<br/><br/></td><td>P0<br/></td></tr>
<tr>
<td>6<br/></td><td>积分<br/></td><td>**个人中心-积分展示**<br/>（P2）<br/></td><td>- 教师端，点击头像，弹出的菜单中，选择「个人中心」：- 在姓名右侧醒目位置显示：当前教师的积分余额。- 边界case说明：若该手机号对应的用户就职于多所学校，这个积分余额是共同的，与学校无关，与手机号相关<br/></td><td>![in_table_SCoFbJNxiok21oxM5LBcElNWncc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_SCoFbJNxiok21oxM5LBcElNWncc.png)<br/></td><td>P2<br/></td></tr>
</table>

![in_table_BjY4bsSuroBB2BxP0Rncz23gnCd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BjY4bsSuroBB2BxP0Rncz23gnCd.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】
该图片展示了一个在线教育平台的课程学习界面，具体元素如下：

1.  **整体课程标题**：总体百分位数的估计
2.  **左侧导航栏 (课程大纲)**：
    *   **百分位数的概念引入**
    *   **百分位数--课程讲解** (当前选中项)
        *   **段落** (子项目，题目数量: 1)
    *   **补充讲解**
    *   **落实检测（固定题目）** (子项目，题目数量: 2)
3.  **右侧内容展示区**：
    *   **内容标题**：概念讲解
    *   **数据列表**：
        *   标题：100户居民月均用水量数据:
        *   数据内容 (10x10 表格)：
            ```
            9.0  13.6  14.9   5.9   4.0   7.1   6.4   5.4  19.4   2.0
            2.2   8.6  13.8   5.4  10.2   4.9   6.8  14.0   2.0  10.5
            2.1   5.7   5.1  16.8   6.0  11.1   1.3  11.2   7.7   4.9
            2.3  10.0  16.7  12.0  12.4   7.8   5.2  13.6   2.6  22.4
            3.6   7.1   8.8  25.6   3.2  18.3   5.1   2.0   3.0  12.0
            22.2  10.8   5.5   2.0  24.3   9.9   3.6   5.6   4.4   7.9
            5.1  24.5   6.4   7.5   4.7  20.5   5.5  15.7   2.6   5.7
            5.5   6.0  16.0   2.4   9.5   3.7  17.0   3.8   4.1   2.3
            5.3   7.8   8.1   4.3  13.3   6.8   1.3   7.0   4.9   1.8
            7.1  28.0  10.2  13.8  17.9  10.1   5.5   4.6   3.2  21.6
            ```
    *   **问题描述**：如果该市政府希望使80%的居民用户生活用水费支出不受影响，你能给市政府提出确定居民用户月均用水量标准建议吗？
    *   **讲师画面**：右下角有讲师的视频或图片。
4.  **顶部操作栏**：
    *   **反馈按钮** (位于右上角)

**元素间关联**：
*   左侧导航栏用于切换和展示课程结构，当前选中的是“百分位数--课程讲解”下的“段落”部分。
*   右侧内容展示区显示当前选中导航栏目对应的学习内容，即“概念讲解”，包括相关数据和思考问题。
*   讲师画面辅助教学。
*   反馈按钮为用户提供反馈渠道。

【============== 图片解析 END ==============】

![in_table_AcdIb2YOhoX0gPxKwOacMzAonKe](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_AcdIb2YOhoX0gPxKwOacMzAonKe.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为一个“反馈”界面，其核心功能是收集用户对课程问题的反馈。

1.  **界面整体**
    *   **标题区域**
        *   包含一个返回图标（`<`）。
        *   标题文字：“反馈”。
    *   **问题类型选择区域**
        *   提示文字：“\*请选择课程问题类型 (可多选)”。
        *   包含以下可多选的问题类型按钮：
            *   板书文字问题
            *   板书图片问题
            *   板书圈画问题
            *   老师配音问题
            *   字幕问题
            *   音画不同步
            *   听不懂、太难了
            *   讲解超纲
            *   黑屏
            *   播放卡顿
            *   其他
    *   **问题描述区域**
        *   提示文字（或标签）：“请描述问题”。
        *   下方为一个多行文本输入框。
    *   **操作区域**
        *   包含一个“提交”按钮。

【============== 图片解析 END ==============】

![in_table_I3Vvb6oFToy9T3xSU5ncOzdrn8c](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_I3Vvb6oFToy9T3xSU5ncOzdrn8c.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个在线学习界面的截图，主要包含以下层级化结构和关键元素：

1.  **页面全局元素**:
    *   **顶部标题**: "总体百分位数的估计"
    *   **右上角控件**:
        *   反馈按钮 (图标文字为 "反馈")
        *   关闭按钮 (图标为 "X")

2.  **左侧导航栏 (课程大纲/内容)**:
    *   **导航项1**: "百分位数的概念引入"
        *   **控件**: 向左箭头图标 (表示可返回或折叠)
    *   **导航项2**: "百分位数—课程讲解"
        *   **状态**: 当前展开 (由 "▼" 图标指示)
        *   **子项**: "段落"
            *   **标注**: "题目数量: 1"
    *   **导航项3**: "补充讲解"
    *   **导航项4**: "落实检测(固定题目)"
        *   **状态**: 当前选中 (背景高亮)
        *   **标注**: "题目数量: 2"

3.  **右侧内容区 (当前显示为测验题详情)**:
    *   **顶部信息栏**:
        *   计时器: "00:10"
        *   提示信息: "推荐用时 1 分 30 秒"
    *   **题目区域**:
        *   **题型**: "单选"
        *   **题目来源/元信息**: "(2021年黑龙江省哈尔滨市南岗区期末)"
        *   **题干**: "下列一组数据的25百分位数是 ( )"
        *   **题目数据**: "2.1, 3.0, 3.2, 3.4, 3.8, 4.0, 4.2, 4.4, 5.3, 5.6"
    *   **选项区域**:
        *   选项 A: "3.2"
        *   选项 B: "3.0"
        *   选项 C: "4.4"
        *   选项 D: "2.5"
    *   **底部操作按钮**:
        *   按钮1: "答错" (样式：白色背景，灰色文字)
        *   按钮2: "答对" (样式：蓝色背景，白色文字，视觉上为主操作按钮)

【============== 图片解析 END ==============】

![in_table_LlwubnXnwoVu1Rxr15xchWS3nob](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_LlwubnXnwoVu1Rxr15xchWS3nob.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

此图片为在线教育产品中学生作业/测验批改结果查看界面。

1.  **页面状态栏**
    *   左上角：时间 `9:30 AM`
    *   右上角：电池电量 `80%`

2.  **页面头部区域**
    *   左侧：返回按钮 (`<`)
    *   中央：
        *   标题：`王玮 | 3.2.2函数的最值`
        *   副标题（完成时间）：`完成时间: 2024.01.23 12:00`
    *   右侧：
        *   操作按钮：`点赞`
        *   操作按钮：`去处理`
        *   课程信息：`课程: 全部2节课` (可能为下拉选择器)

3.  **内容切换标签**
    *   `答题结果` (当前选中)
    *   `学生提问`

4.  **作业进度提示**
    *   信息图标 (`i`)
    *   文本：`目前已完成作业进度80%`

5.  **题目筛选与统计区域**
    *   左侧：搜索框，提示文字 `请输入知识点关键词`，附带搜索图标 (`Q`)
    *   搜索框右侧文本：`共28道题, 3道错题 (按照已作答排序)`
    *   右侧筛选控件：
        *   开关：`只看共性错题`
        *   下拉选择器：`作答人数排序`
        *   下拉选择器：`全部题型`

6.  **题目列表区域**
    *   **题目 1 (题号 123)**
        *   题干：`若函数$y=f(x)$在R上可导且满足不等式$xf'(x) > -f(x)$恒成立，则下列不等式一定成立的是（ ）`
        *   选项：
            *   A. $a f(b) > b f(a)$
            *   B. $a f(a) > b f(b)$
            *   C. $a f(a) < b f(b)$
            *   D. $a f(b) < b f(a)$
        *   标签：`2020年·江西省·期末`, `单选`, `简单`
        *   作答状态：`尚无作答, 暂无结果`
        *   操作：`答案解析` (下拉菜单)，`反馈` (按钮)，`查看` (按钮)
    *   **题目 2 (题号 78)**
        *   题干：`若函数$y=f(x)$在R上可导且满足不等式$xf'(x) > -f(x)$恒成立，则下列不等式一定成立的是（ ）`
        *   选项：
            *   A. $a f(b) > b f(a)$
            *   B. $a f(a) > b f(b)$
            *   C. $a f(a) < b f(b)$
            *   D. $a f(b) < b f(a)$
        *   标签：`2020年·江西省·期末`, `单选`, `简单`
        *   作答状态：绿色勾选图标，`正确`
        *   操作：`答案解析` (下拉菜单)，`反馈` (按钮)，`查看` (按钮)

7.  **侧边浮动面板入口**
    *   右侧边缘：`题目面板 >` (指示可展开或跳转的面板)

【============== 图片解析 END ==============】

![in_table_HcLHbVIZpoL1pIxVFU4caINUnjg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_HcLHbVIZpoL1pIxVFU4caINUnjg.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】
该图片展示了一个**反馈**界面。
1.  **顶部导航栏**
    *   左侧为返回图标：`<`
    *   中间为标题：“反馈”
2.  **问题类型选择区**
    *   提示文字：“*请选择题目问题类型（可多选）”
    *   可选择的预设问题类型按钮，共八个，分四行两列展示：
        *   “题干、选项有错”
        *   “答案有错”
        *   “配图有错”
        *   “解析有错”
        *   “题目超纲了”
        *   “太难了”
        *   “太简单了”
        *   “其他”
3.  **问题描述区**
    *   提示文字：“请描述问题”
    *   下方为一个多行文本输入区域，供用户详细描述问题。
4.  **提交操作区**
    *   底部为一个“提交”按钮。

【============== 图片解析 END ==============】

![in_table_INqfb2UipoAqF8xpxpec6WPHncf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_INqfb2UipoAqF8xpxpec6WPHncf.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

# 整体界面：在线课程学习页面

## 1. 页面顶部标题栏
- 页面主标题：“总体百分位数的估计”
- 右上角操作区域：
    - 视频 (图标)
    - 习题 (图标)
    - 图片 (图标)
    - 反馈 (图标，当前选中状态)
    - 关闭 (X 图标)

## 2. 左侧导航栏 (课程目录)
- **当前章节**：“总体百分位数的估计” (与页面主标题呼应)
- **导航项一**：“百分位数的概念引入” (带有一个向右的指示箭头，表示可展开或跳转)
- **导航项二**：“百分位数-课程讲解” (当前展开状态，用向下的三角符号表示)
    - **子项1**：“段落”
        - 附注：“题目数量：1”
    - **子项2**：“补充讲解” (当前选中高亮项)
        - 标签：“视频组件”
    - **子项3**：“落实检测（固定题目）”
        - 附注：“题目数量：2”

## 3. 右侧内容区 (视频播放器)
- **视频画面内容** (讲师授课，画面中PPT内容):
    - PPT大标题：“课程总结”
    - PPT核心内容：“用样本百分位数估计总体百分位数”
        - 分支内容1：“百分位数的概念”
        - 分支内容2：“百分位数的计算”
        - 分支内容3：“常见的百分位数”
        - 分支内容4：“百分位数的特点”
- **视频播放器控件栏**:
    - 左侧：“视频”文字标签
    - 播放控制：
        - 后退10秒按钮
        - 播放/暂停按钮
        - 前进10秒按钮
    - 播放进度条
    - 右侧：“倍速” 文字按钮 (用于调节播放速度)

【============== 图片解析 END ==============】

![in_table_FROrb9I0Io9PPVxdq3TcEZOanUb](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_FROrb9I0Io9PPVxdq3TcEZOanUb.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个“反馈”表单界面，其关键元素和组成部分结构如下：

1.  **页面头部 (Page Header)**
    *   **返回图标 (Back Icon):** 位于左上角，通常用于导航至上一页。
    *   **标题 (Title):** “反馈”。

2.  **视频问题类型选择区 (Video Problem Type Selection Area)**
    *   **引导文本 (Instruction Text):** “\*请选择视频问题类型（可多选）”，其中星号通常表示必填项。
    *   **问题类型选项 (Problem Type Options):** 以按钮形式呈现，用户可多选。
        *   “知识讲解有误”
        *   “和本课不相关”
        *   “引起不适”
        *   “其他”

3.  **问题描述区 (Problem Description Area)**
    *   **引导文本/占位符 (Instruction Text/Placeholder):** “请描述问题”。
    *   **文本输入区域 (Text Input Area):** 一个多行文本框，供用户详细输入问题描述。

4.  **操作区 (Action Area)**
    *   **提交按钮 (Submit Button):** 带有“提交”文字的按钮，用于提交反馈内容。

【============== 图片解析 END ==============】

![in_table_ZBBKbAXu4oU6MYxjNqncHEqsnyb](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ZBBKbAXu4oU6MYxjNqncHEqsnyb.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为一个在线教育平台的教师课程管理界面，具体元素如下：

**一、 整体界面布局**
*   **左侧导航栏**：固定宽度，包含产品标识、导航菜单和用户信息。
*   **右侧主内容区域**：显示问候语、日期切换控件和课程日历。

**二、 左侧导航栏**
1.  **产品标识**：位于导航栏顶部，文字为“产品标识”。
2.  **导航菜单**：
    *   **课程**：当前选中项，有图标。
    *   **作业**：有图标。
    *   **用户反馈**
    *   **个人中心**
    *   **群组管理**
    *   **公告**
    *   **退出登录**
3.  **用户信息区域**：位于导航栏底部。
    *   头像图标
    *   **杨斯文**
    *   **教师ID：4**

**三、 右侧主内容区域**
1.  **顶部问候与日期控制**
    *   问候语：“**早，杨老师**”
    *   日期导航：
        *   【**回到今天**】按钮
        *   【**<**】向左箭头（上一月）
        *   【**>**】向右箭头（下一月）
        *   当前显示月份：“**2025年5月**”
2.  **课程日历视图**：以周为单位展示课程。
    *   **日期行**：
        *   周一 **12**
        *   周二 **13**
        *   周三 **14**
        *   周四 **15**
        *   周五 **16** (日期16被蓝色圆圈高亮)
        *   周六 **17**
        *   周日 **18**
    *   **时间列**：从上至下分别为 07:00, 08:00, 09:00, 10:00, 11:00, 12:00。
    *   **课程安排格**：
        *   周一 08:00 - 08:40：**高一一班** (标签: 语)
        *   周一 09:00 - 09:40：**高一一班** (标签: 语)
        *   周二 09:00 - 09:40：**高一一班** (标签: 语)
        *   周三 08:00 - 08:40：**高一一班** (标签: 语)
        *   周三 10:00 - 10:40：**高一一班** (标签: 语)
        *   周四 09:00 - 09:40：**高一一班** (标签: 语)
        *   周五 08:00 - 08:40：**高一一班** (标签: 语)
        *   周五 10:00 - 10:40：**高一一班** (标签: 语)
    *   **当前时间指示线**：一条粉色水平线，横跨日历区域，位于12:00时间刻度线上。

【============== 图片解析 END ==============】

![in_table_Ba11briOdoyyjVxQ1T1cru4Dn5g](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Ba11briOdoyyjVxQ1T1cru4Dn5g.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为一个“用户反馈”的界面。

1.  **页面整体**
    *   **标题**: “用户反馈”
    *   **副标题/引导语**: “欢迎与我们反馈您的问题”

2.  **问题描述区域**
    *   **标签**: “问题描述\*”（星号表示此项为必填）
    *   **输入框**: 一个多行文本输入区域。
        *   **占位提示文字**: “请输入10个字以上的问题描述以便我们提供更好的帮助”
        *   **字数限制提示**: 右上角显示 “0/2000”，表示当前已输入0字，最多可输入2000字。

3.  **上传截图区域**
    *   **标签**: “上传截图 (选填) (0/4)” （表示此项为选填，当前已上传0张图片，最多可上传4张）
    *   **上传触发区域**: 一个虚线框区域，内部包含：
        *   “+” 图标
        *   文字：“上传图片”

4.  **提交操作**
    *   **按钮**: 一个 содержащим文本 “提交” 的按钮。

**元素间关联**:
*   “用户反馈”页面包含“问题描述”输入区、“上传截图”区和“提交”按钮。
*   “问题描述”是必填项，用于用户输入文本反馈。
*   “上传截图”是选填项，用于用户上传图片作为辅助说明。
*   用户填写完信息后，通过点击“提交”按钮发送反馈。

【============== 图片解析 END ==============】

![in_table_DAs7bArrqoITe8xorzjcqIxqnbd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_DAs7bArrqoITe8xorzjcqIxqnbd.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】
该图片为一个用户界面截图，主要分为状态栏、左侧导航栏和右侧内容区。

1.  **状态栏 (顶部)**
    *   时间显示: "9:30 AM"
    *   系统图标: 右侧包含网络信号和电池电量 "80%"

2.  **左侧导航栏**
    *   **产品标识区域**:
        *   图标: 一个圆形图标
        *   文字: "产品标识"
    *   **菜单项**:
        *   布置 (当前选中状态)
        *   课程
        *   作业
        *   资源
        *   学情
        *   社区
    *   **用户信息区域 (底部)**:
        *   头像: 一个猫形头像
        *   姓名: "宋萍"
        *   ID: "教师 ID: 135121"

3.  **右侧内容区 (消息区域)**
    *   **消息概览**: "消息 共2条, 0条未读"
    *   **消息列表**:
        *   **第一条消息**:
            *   内容: "您在“5月16日 10:31”的反馈被采纳了！我们已完成上线修改，敬请关注。再次感谢您的支持～"
            *   时间戳: "2025年5月16日 13:23"
        *   **第二条消息**:
            *   内容: "您在“5月15日 18:31”的反馈暂未被采纳，我们将在后续版本中考虑优化。再次感谢您的支持～"
            *   时间戳: "2025年5月16日 09:23"

【============== 图片解析 END ==============】

![in_table_SCoFbJNxiok21oxM5LBcElNWncc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_SCoFbJNxiok21oxM5LBcElNWncc.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个教育产品的用户界面，主要分为左侧导航栏和右侧内容展示区。

一、 左侧导航栏
    1.  **产品标识**：位于导航栏顶部，显示为一个图形 Logo。
    2.  **导航菜单**：
        *   布置
        *   课程
        *   作业 (当前选中状态)
        *   资源
        *   学情
        *   社区
    3.  **底部用户信息**：
        *   用户头像
        *   用户姓名：宋萍
        *   用户ID：教师 ID: 135121

二、 右侧内容展示区
    1.  **教师信息模块**：
        *   模块标题：“教师信息”
        *   教师头像
        *   教师姓名：宋萍
        *   附加信息/标识：DIV, 3280 (旁边有图标)
        *   操作按钮：修改
        *   手机号：16710047780
        *   教师ID：135121 (旁边有复制图标)
    2.  **当前职务模块**：
        *   当前职务信息：安徽省阜阳亲情高中 (带有下拉选择标识)
        *   **年级主任信息卡片**：
            *   职务标题：年级主任
            *   负责年级：高二 / 高三
        *   **数学教师信息卡片**：
            *   职务标题：数学教师
            *   负责班级：高二3班、高二5班、高二8班
    3.  **“为你精选”模块**：
        *   模块标题：“为你精选”
        *   **推荐内容列表** (以卡片形式横向展示)：
            *   **推荐项一**：
                *   卡片标题：“如何使用资源布置功能”
                *   卡片预览图文字内容：“01 章节目录点击定位，选择更聚焦”，辅以相关操作界面截图。
            *   **推荐项二**：
                *   卡片标题：“三角函数公式及其变换”
                *   卡片预览图文字内容：“高中数学课件：三角函数 常用公式及其变换”。
            *   **推荐项三**：
                *   卡片标题：“二次函数课件分享”
                *   卡片预览图内容：
                    *   课程类型：“基础教育精品课”
                    *   课程章节/名称：“22.1.1 二次函数”
                    *   课程详细信息：
                        *   年级：九年级
                        *   主讲人：陈月琼
                        *   学科：数学 (人教版)
                        *   学校：云安区都杨镇都骑中学

【============== 图片解析 END ==============】



#### **5.2 学生端：用户反馈**

<table>
<tr>
<td>序号<br/></td><td>模块<br/></td><td>功能名称<br/></td><td>功能概述<br/></td><td>图<br/></td><td>优先级<br/></td></tr>
<tr>
<td>1<br/></td><td>反馈<br/></td><td>**课程反馈**<br/><br/></td><td>- 提供「课程结算页」的悬浮【反馈】入口。<br/>- 支持选择问题类型1个或多选<br/>- 支持填写详细问题描述。<br/>- 支持用户上传0～4张截图（UI图需补充）<br/>- 提供：“提交成功，感谢您的反馈”的toast展示。<br/></td><td>![in_table_IgDrbPZLvoCX3dxIUYYcqXtLn7g](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_IgDrbPZLvoCX3dxIUYYcqXtLn7g.png)<br/><br/></td><td><br/></td></tr>
<tr>
<td>2<br/></td><td>反馈<br/></td><td>**课程反馈**<br/><br/></td><td>- 提供「课中」-「评论」大模型判定为反馈的数据 导入到用户反馈看板的数据导入规则<br/>- 需服务端同时提供：评论页面截图、用户id等信息<br/>- 待补充详情<br/></td><td>无需ui<br/></td><td><br/></td></tr>
<tr>
<td>3<br/></td><td>反馈<br/></td><td>**题目反馈**<br/></td><td>- 提供答题页的悬浮【反馈】入口。<br/>- 支持选择问题类型（内容错误、题目错误、视频错误、其他）。<br/>- 支持填写详细问题描述。<br/>- 上传当前页面的截图（客户端自动截屏）。<br/>- 提供：“提交成功，感谢您的反馈”的toast展示。<br/></td><td>![in_table_FIuybyIiGoTt2ux079zcy37XnHh](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_FIuybyIiGoTt2ux079zcy37XnHh.png)<br/><br/></td><td><br/></td></tr>
<tr>
<td>4<br/></td><td>反馈<br/></td><td>**其他bug反馈**<br/></td><td>提供「我的」页面-【我要反馈】入口。<br/><br/></td><td>待补充ui<br/></td><td><br/></td></tr>
</table>

![in_table_IgDrbPZLvoCX3dxIUYYcqXtLn7g](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_IgDrbPZLvoCX3dxIUYYcqXtLn7g.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为一个用户反馈界面，用于收集用户在课程学习过程中遇到的问题。

### 1. 页面主要组成部分

该界面由以下几个核心区域构成：

*   **顶部导航区**：
    *   **返回按钮**：位于左上角，图标为 "<"，用于返回前一页面。
    *   **页面标题**："反馈"，明确当前页面的功能。
*   **问题类型选择区**：
    *   **引导说明**：文字为 "请选择课程问题类型 (可多选)"，提示用户此区域为问题分类选择，且允许选择多个选项。
    *   **问题选项列表**（以可点击标签/按钮形式呈现）：
        *   板书文字问题
        *   板书图片问题
        *   板书圈画问题
        *   老师配音问题
        *   字幕问题
        *   音画不同步
        *   听不懂、太难了
        *   讲解超纲
        *   黑屏
        *   播放卡顿
        *   其他
*   **问题描述区**：
    *   **引导说明**：文字为 "请描述问题"。
    *   **文本输入框**：一个多行文本输入区域，供用户详细填写具体问题。
*   **提交操作区**：
    *   **提交按钮**：位于页面底部，文字为 "提交"，用户点击后提交反馈内容。

### 2. 元素间关联

*   用户首先在 **问题类型选择区** 选择一个或多个预设的问题类型。
*   如果预设类型无法准确描述，或需要补充说明，用户可以在 **问题描述区** 的 **文本输入框** 内输入详细文字。
*   完成问题选择和描述后，用户点击 **提交操作区** 的 **提交按钮** 来发送反馈。
*   **顶部导航区** 的 **返回按钮** 允许用户在任何时候放弃反馈并返回之前的页面。

【============== 图片解析 END ==============】

![in_table_FIuybyIiGoTt2ux079zcy37XnHh](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_FIuybyIiGoTt2ux079zcy37XnHh.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为一个用户界面，用于提交反馈。其关键元素与组成部分层级化结构如下：

*   **页面标题区域**
    *   **返回按钮**: 位于左上角，图标为向左的箭头。
    *   **页面标题**: "反馈"。

*   **问题类型选择区域**
    *   **引导文字**: "*请选择题目问题类型（可多选）"。
    *   **问题类型选项 (按钮形式，可多选)**:
        *   "题干、选项有错"
        *   "答案有错"
        *   "配图有错"
        *   "解析有错"
        *   "题目超纲了"
        *   "太难了"
        *   "太简单了"
        *   "其他"

*   **问题描述区域**
    *   **引导文字**: "请描述问题"。
    *   **文本输入框**: 一个多行文本输入区域，用于用户填写详细问题描述。

*   **操作区域**
    *   **提交按钮**: 按钮文案为 "提交"。

【============== 图片解析 END ==============】



#### **5.3 生产后台：用户反馈看板（多维表格方案）**

**bug 反馈统计表单（使用多维表格实现）：**[用户反馈管理（AI 打标）](https://wcng60ba718p.feishu.cn/wiki/IUXzwxlnwibh0GkKVKGc46K6nzc?table=tblv2bV4BYvzjYXQ&view=vewhAzoAcq)
**功能说明**

<table>
<tr>
<td>序号<br/></td><td>模块<br/></td><td>功能名称<br/></td><td>功能概述<br/></td><td>图<br/></td><td>优先级<br/></td></tr>
<tr>
<td>1<br/><br/></td><td>生产后台-用户反馈管理（表格视图）<br/></td><td>用户反馈列表<br/></td><td>**用户反馈列表（使用多维表格实现）：**<br/>表格信息：<br/>- 核心信息- 用户反馈原文- 用户反馈截图- 反馈模块- 反馈内容分类- 学科- 课程id/题目id- 反馈日期<br/>- 对接人- 生产负责人- 审核人<br/>- 审核agent- 机器分类结果- 机器分类优先级- 人工check机器审核结果<br/>- 反馈结果：- 进展- 分类结果- 积分- 自动回复话术<br/>**排序：**<br/>- 默认按反馈时间倒序排列<br/>**筛选与搜索：**<br/>- 提供上述列名称进行等多种筛选条件。<br/>- 支持点击“反馈原文”查看详情<br/></td><td>多维表格：<br/>![in_table_F7ivbuITFo41AIxjhz8cHyzRnQd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_F7ivbuITFo41AIxjhz8cHyzRnQd.png)<br/>查看详情：<br/>![in_table_Nz6Qbr3lQoHxnUxFFU9cJve7nxe](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Nz6Qbr3lQoHxnUxFFU9cJve7nxe.png)<br/>排序：<br/>![in_table_RgYubnIyzoCQ4pxHrdpcw6RbnSf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_RgYubnIyzoCQ4pxHrdpcw6RbnSf.png)<br/><br/></td><td><br/></td></tr>
<tr>
<td>2<br/></td><td>生产后台-用户反馈管理（表格视图 & 看板视图）<br/><br/></td><td>任务分发 和 统计<br/></td><td>**任务分发：**<br/>- 管理员可以配置自动分发规则，设置对接人：生产负责人，例如将特定科目的反馈自动分配给对应的审核老师。<br/>- 支持手动选择审核人员进行任务分配。<br/>**工作流设置：**<br/>- 反馈中有新的记录时，feishu-bot发送消息提醒对应学科的生产负责人、技术负责人、产品PM关注<br/>- 对于人工核对结果为“无效反馈”或“有效反馈”&“已上线”的自动发送后台客服消息给用户<br/></td><td>任务分发<br/>![in_table_YZH0b7ZYtoBBfUxShk1cp4Ynnof](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_YZH0b7ZYtoBBfUxShk1cp4Ynnof.png)<br/>生产工作流<br/>![in_table_WXv6bDGFSoHDopxGGu9c1WsxnGg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_WXv6bDGFSoHDopxGGu9c1WsxnGg.png)<br/>统计看板<br/>![in_table_E99ebzxJSoDPwBxMSpzcxC9Tn8e](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_E99ebzxJSoDPwBxMSpzcxC9Tn8e.png)<br/></td><td><br/></td></tr>
<tr>
<td>3<br/></td><td>生产后台-用户反馈管理（仪表盘视图）<br/></td><td>数据统计<br/></td><td>**统计报表：**<br/>- 统计不同时间段的反馈数量趋势。<br/>- 统计各种问题类型的占比。<br/>- 统计反馈的处理效率和质量。<br/>- 支持自定义报表生成和导出。<br/></td><td>仪表盘<br/>![in_table_PzNTbFTXNoKGSjx7Lhuce0n3n6X](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_PzNTbFTXNoKGSjx7Lhuce0n3n6X.png)<br/><br/></td><td><br/></td></tr>
</table>

![in_table_F7ivbuITFo41AIxjhz8cHyzRnQd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_F7ivbuITFo41AIxjhz8cHyzRnQd.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个名为“用户反馈管理 (AI打标)”的Web应用程序界面。

**一、 整体布局与导航**

*   **顶部导航栏**: 显示面包屑路径 “银河智学 > 袁全 > 用户反馈管理(AI打标)”，以及分享、自动化等操作按钮。
*   **左侧边栏**:
    *   搜索框
    *   功能列表: 用户反馈 (当前选中)、查询页、仪表盘、当有新的差评时,...
    *   新建操作: 导入Excel、数据表、收集表、仪表盘、工作流、文档、文件夹、从其他数据源...
*   **主内容区**: 用户反馈数据的表格展示。
*   **右下角浮动按钮**: 包含“智能查询记录”、“如何使用多维表格”、“总结当前多维表格”等快捷操作。

**二、 主内容区 - 用户反馈表格**

1.  **视图切换**: 支持 “表格”、“甘特图”、“看板” 等视图，并可添加新视图。
2.  **表格操作栏**:
    *   `+ 添加记录`
    *   `字段配置`
    *   `视图配置`
    *   `筛选`
    *   `分组`
    *   `排序`
    *   `行高`
    *   `填色`
3.  **表格列 (核心信息)**:
    *   `反馈原文` (文本)
    *   `用户反馈截图` (附件)
    *   `反馈模块` (分类标签)
    *   `反馈内容分类` (分类标签)
    *   `学科` (分类标签)
    *   `课程id/题目id` (数字ID)
    *   `反馈日期` (日期时间)
    *   `生产负责人` (人员)
4.  **表格数据行示例**:
    *   **记录1**:
        *   反馈原文: 我的职务数据展示不完整
        *   反馈模块: 教师端其...
        *   反馈内容分类: 其他bug或建议
        *   学科: 未知
        *   课程id/题目id: (空)
        *   反馈日期: 2025/07/01 11:00
        *   生产负责人: 李盼
    *   **记录2**:
        *   反馈原文: 题目答案有误
        *   反馈模块: ai课-练习
        *   反馈内容分类: 题目答案有误
        *   学科: 数学
        *   课程id/题目id: 2733048
        *   反馈日期: 2025/07/01 12:30
        *   生产负责人: 李盼
    *   **记录3**:
        *   反馈原文: 课程内容有误
        *   反馈模块: ai课-课程
        *   反馈内容分类: 课程part名称...
        *   学科: 数学
        *   课程id/题目id: 100001
        *   反馈日期: 2025/07/03 13:30
        *   生产负责人: 袁全
5.  **表格底部**: 显示“3条记录”。

**三、 关联性**

该界面用于集中管理和追踪用户反馈。每个反馈条目（行）包含详细信息（列），便于分类、定位问题（如学科、课程ID）并指派负责人。AI打标的字样暗示系统可能利用AI辅助处理反馈信息。表格上方的工具栏提供了对数据进行管理和自定义视图的功能。

【============== 图片解析 END ==============】

![in_table_Nz6Qbr3lQoHxnUxFFU9cJve7nxe](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Nz6Qbr3lQoHxnUxFFU9cJve7nxe.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为用户反馈管理系统的一个详情页面截图，展示了关于“题目答案有误”的用户反馈的具体信息。页面结构和关键元素如下：

**一、整体页面结构**

*   **顶部导航/标题栏**:
    *   左上角区域:
        *   应用标识和路径: `银河智学 >`
        *   文档保存状态: `已经保存到云`
    *   主标题 (通常位于页面视觉中心，此图显示在左上角信息下方，并作为右侧内容区的顶部标题): `题目答案有误`
    *   右上角全局操作按钮: `分享`, `关注`, `评论`, `...` (更多), `X` (关闭), `+ 添加插件`

*   **左侧边栏**:
    *   顶部控制按钮: 菜单折叠/展开图标、主页图标
    *   搜索框: 内含占位符 `搜索`
    *   导航列表:
        *   `用户反馈` (当前选中，右侧有三点更多操作图标)
        *   `查询页`
        *   `仪表盘`
        *   `当有新的差评时, ...`
    *   `新建` (下拉操作区):
        *   `导入 Excel`
        *   `数据表`
        *   `收集表`
        *   `仪表盘`
        *   `工作流` (带 `New` 标识)
        *   `文档`
        *   `文件夹`
    *   底部操作: `从其他数据源...`

*   **右侧主内容区**:
    *   (顶部为此反馈的主标题: `题目答案有误`)
    *   选项卡: `详情` (激活状态), `历史`
    *   反馈信息详情:
        *   **核心信息** (可展开/收起的区域)
            *   字段名: `A 反馈原文` (字段名前有字母'A'形状的图标)，值: `题目答案有误`
            *   字段名: `用户反馈截图` (字段名前有回形针图标)，值: [图片缩略图]
        *   其他属性字段 (部分字段名左侧有对应图标):
            *   字段名: `反馈模块`，值: `ai课-练习` (标签形式，可移除)
            *   字段名: `反馈内容分类`，值: `题目答案有误` (标签形式，可移除)
            *   字段名: `学科`，值: `数学` (标签形式，可移除)
            *   字段名: `课程id/题目id`，值: `2733048`
            *   字段名: `反馈日期`，值: `2025/07/01 12:30`
            *   字段名: `对接人` (可展开/收起的区域)
                *   子字段名: `生产负责人`，值: `李盼` (带圆形头像的标签形式，可移除)
            *   字段名: `审核人`，值: `请选择选项` (占位文字)
            *   字段名: `情感分析`，值: `负向` (标签形式，可移除)
            *   字段名: `审核agent` (可展开/收起的区域，字段名仅部分可见)
        *   内容区右侧边缘有一个独立的 `+` 按钮。

*   **右下角浮动工具栏**:
    *   时钟图标
    *   文字按钮: `智能查询记录`
    *   文字按钮: `如何使用多维表格`
    *   文字按钮: `总结当前多维表格`
    *   蓝色背景星芒图标按钮
    *   用户头像图标 (可能为AI助手或客服)

【============== 图片解析 END ==============】

![in_table_RgYubnIyzoCQ4pxHrdpcw6RbnSf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_RgYubnIyzoCQ4pxHrdpcw6RbnSf.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】
该图片为一个用户界面截图，用于“设置排序条件”。

1.  **标题与全局设置**:
    *   “设置排序条件”：界面功能的主标题。
        *   右侧附带一个问号图标，通常表示帮助或提示信息。
    *   “自动排序”：文本标签，其右侧为一个开启状态的开关控件。

2.  **排序条件列表**:
    *   **第一个排序条件**:
        *   左侧有拖拽图标（六个点），用于调整排序优先级。
        *   第一个下拉选择框，当前选定值为“反馈日期”。
        *   排序方向选择器：
            *   按钮“0 → 9”（表示升序），当前未选中。
            *   按钮“9 → 0”（表示降序），当前为选中状态。
        *   右侧有“X”图标，用于删除该条排序条件。
    *   **添加新排序条件**:
        *   下方有一个下拉选择框，默认显示“选择条件”，用于添加新的排序字段。

3.  **视图操作**:
    *   “另存为新视图”：一个文本链接，点击后可将当前排序配置保存为新的视图。

【============== 图片解析 END ==============】

![in_table_YZH0b7ZYtoBBfUxShk1cp4Ynnof](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_YZH0b7ZYtoBBfUxShk1cp4Ynnof.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个用于管理反馈信息的表格视图界面，主要构成如下：

1.  **视图切换区**: 位于界面顶部，允许用户在不同视图间切换。
    *   **表格**: 当前选中的视图（以列表形式展示数据）。
    *   **甘特图**: 可切换的视图类型。
    *   **看板**: 可切换的视图类型。
    *   **+ (加号图标)**: 用于添加新的视图。

2.  **操作工具栏**: 位于视图切换区下方，提供对表格数据的操作功能。
    *   **添加记录**: 按钮，用于新增数据行。
    *   **字段配置**: 功能，用于管理表格的列（字段）。
    *   **视图配置**: 功能，用于配置当前视图的显示方式。
    *   **筛选**: 功能，用于按条件过滤数据。
    *   **分组**: 功能，用于对数据进行分组显示。
    *   **排序**: 功能，用于对数据进行排序（图片中显示为“1 排序”，表示当前有一个排序规则）。
    *   **行高**: 功能，用于调整表格行的高度。
    *   **填色**: 功能，用于对单元格或行进行颜色标记。
    *   **右侧其他操作图标**:
        *   一个彩色圆环图标（功能未知）。
        *   一个时钟图标（可能代表“历史记录”）。
        *   **生成表单**: 功能，用于基于当前表格结构创建数据收集表单。
        *   撤销、重做、搜索、评论等标准操作图标。

3.  **数据表格区**: 显示具体的反馈信息记录。
    *   **行首复选框**: 用于选择表格中的行。
    *   **表头 (字段)**:
        *   **核心信息** (这是一个逻辑分组，涵盖了其下方的多个字段):
            *   **反馈模块**: 记录反馈所属的系统模块 (例如: `ai课-课程`, `ai课-练习`, `教师端其...`)。
            *   **反馈内容分类**: 对反馈内容的具体分类 (例如: `课程part名称...`, `题目答案有误`, `其他bug或建议`)。
            *   **学科**: 反馈涉及的学科 (例如: `数学`, `未知`)。
            *   **课程id/题目id**: 相关的课程ID或题目ID (例如: `100001`, `2733048`)。
            *   **反馈日期**: 反馈提交的日期和时间 (例如: `2025/07/03 13:30`)。
        *   **对接人**: 负责处理该反馈的对接人员 (此列及后续三列被图片中的红色方框特别标出)。
        *   **生产负责人**: 负责相关内容生产的人员 (例如: `李盼`, `袁全`)。
        *   **审核人**: 负责审核的人员。
        *   **上线人**: 负责上线的人员。
        *   **情感分析**: AI (人工智能)辅助的情感倾向判断 (例如: `正向`, `负向`)。
        *   **机器分类**: AI辅助的反馈分类 (例如: `有效反馈`)。
    *   **数据行 (记录示例)**:
        *   记录1: 反馈模块为 `ai课-课程`, 反馈内容分类为 `课程part名称...`, 学科为 `数学`, 课程id/题目id为 `100001`, 反馈日期为 `2025/07/03 13:30`, 情感分析为 `正向`。
        *   记录2: 反馈模块为 `ai课-练习`, 反馈内容分类为 `题目答案有误`, 学科为 `数学`, 课程id/题目id为 `2733048`, 反馈日期为 `2025/07/01 12:30`, 生产负责人为 `李盼`, 情感分析为 `负向`, 机器分类为 `有效反馈`。
        *   记录3: 反馈模块为 `教师端其...`, 反馈内容分类为 `其他bug或建议`, 学科为 `未知`, (无课程id/题目id), 反馈日期为 `2025/07/01 11:00`, 生产负责人为 `袁全`, 情感分析为 `正向`。
    *   **添加行按钮 (+)**: 位于表格数据行的最下方，用于快速添加新记录。

4.  **高亮区域**:
    *   图片中使用红色方框高亮了表格头部的“对接人”、“生产负责人”、“审核人”、“上线人”这四个字段列，表明这些是需要特别关注的角色或信息。

【============== 图片解析 END ==============】

![in_table_WXv6bDGFSoHDopxGGu9c1WsxnGg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_WXv6bDGFSoHDopxGGu9c1WsxnGg.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

此图片展示了一个自动化工作流配置界面，主要包含以下部分：

**一、 整体布局**
*   **左侧导航区:**
    *   顶部: 搜索框 (提示文字: "搜索")。
    *   导航列表:
        *   用户反馈
        *   查询页
        *   仪表盘
        *   当有新的差评... (当前高亮选中项)
    *   "新建" 折叠菜单:
        *   新建
        *   导入 Excel
    *   底部列表:
        *   数据表
        *   收集表
*   **右侧主内容区:**
    *   顶部操作栏:
        *   工作流名称: "当有新的差评时，为售后管理员发送飞书消息"
        *   状态开关按钮 (用于开启/关闭此工作流)
        *   右侧: "运行日志" 链接，"保存并启用" 蓝色按钮。
    *   工作流配置区: 可视化展示一个自动化流程。

**二、 工作流配置详情 (流程图)**
*   **流程名称:** "当有新的差评时，为售后管理员发送飞书消息"
*   **流程步骤:**
    1.  **触发条件 (步骤 1):**
        *   类型: "修改记录时"
        *   关联数据表: "用户反馈"
    2.  **执行动作 (步骤 2):**
        *   类型: "发送飞书消息"
        *   接收方: "生产负责人"
    *   下方有一个 "+" 按钮，用于添加新的流程步骤。

**三、 Mermaid 流程图表示**

```mermaid
flowchart TD
    A["1. 修改记录时<br>数据表: 用户反馈"] --> B["2. 发送飞书消息<br>接收方: 生产负责人"]
    B --> C("+")
```

【============== 图片解析 END ==============】

![in_table_E99ebzxJSoDPwBxMSpzcxC9Tn8e](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_E99ebzxJSoDPwBxMSpzcxC9Tn8e.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为一个看板（Kanban）界面的截图，用于任务或信息管理。

1.  **顶部导航栏与操作栏**
    *   **视图切换**:
        *   表格
        *   甘特图
        *   看板 (当前选中)
    *   **分组状态提示**: "你调整了分组"
    *   **操作按钮**:
        *   清除
        *   保存
        *   “+” (添加)
    *   **主要操作区域**:
        *   **添加记录**: 蓝色“+”号按钮
        *   **分组依据**: "生产负责人" (此文字在红色框内突出显示)
        *   **卡片配置**: 图标按钮
        *   **筛选**: 图标按钮
        *   **排序**: 图标按钮

2.  **看板区域**
    *   **分组列**:
        *   **未分类 1**: 包含1张卡片。
            *   **卡片1**:
                *   标题: "课程内容有误"
                *   缩略图
                *   标签: "ai课-课程", "课程part名称有误", "数学"
                *   ID: "100001"
                *   日期时间: "2025/07/03 13:30"
                *   标识: "正向"
                *   描述: "感谢您的评价！我们非常高兴您满意我们的产品。我们会尽快改进尺码问题..."
                *   记录ID: "rec27sJu5t"
                *   列末尾有 "+" 添加按钮。
        *   **袁全 1**: (头像) 袁全，包含1张卡片。
            *   **卡片1**:
                *   标题: "我的职务数据展示不完整"
                *   缩略图
                *   标签: "教师端其他模块名称", "其他bug或建议", "未知"
                *   日期时间: "2025/07/01 11:00"
                *   标识: "正向"
                *   描述: "亲爱的顾客，太感谢您的认可啦！很高兴衣服能让您满意，我们会继续保持..."
                *   状态: "暂无需回复"
                *   记录ID: "rect2qMOhk"
                *   列末尾有 "+" 添加按钮。
        *   **李盼 1**: (头像) 李盼，包含1张卡片。
            *   **卡片1**:
                *   标题: "题目答案有误"
                *   缩略图
                *   标签: "ai课-练习", "题目答案有误", "数学"
                *   ID: "2733048"
                *   日期时间: "2025/07/01 12:30"
                *   标识: "负向"
                *   状态标签: "有效反馈", "采纳了：机器审核结果", "生产中", "反馈被采纳了"
                *   数值: "50"
                *   描述: "非常抱歉您对我们的产品感到失望，我们会尽快核实情况并尽快给您回复。"
                *   状态: "尚未回复"
                *   记录ID: "rechdvzyNa"
                *   列末尾有 "+" 添加按钮。
        *   **新建分组**: "+" 按钮，用于添加新的分组列。
    *   界面存在 "袁全 7006" 和 "李盼 7006" 等水印或背景文字。

【============== 图片解析 END ==============】

![in_table_PzNTbFTXNoKGSjx7Lhuce0n3n6X](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_PzNTbFTXNoKGSjx7Lhuce0n3n6X.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为“用户反馈管理 (AI打标)”界面的仪表盘视图，旨在展示用户反馈的各项分析数据。

**1. 页面整体布局与导航**

*   **顶部导航栏 (Header)**:
    *   **面包屑导航**: `银河智学 > 袁全 > 用户反馈管理 (AI打标)`，表明当前页面在产品内的层级位置。
    *   **保存状态**: "已经保存到云端"。
*   **顶部工具栏 (Top Toolbar - Right)**:
    *   操作按钮: "分享"、"自动化"。
    *   其他功能图标: 搜索、添加、通知、用户头像等。
    *   仪表盘操作: "全屏演示"、"分享仪表盘"。
*   **左侧导航栏 (Left Sidebar)**:
    *   **搜索框**: "搜索"。
    *   **菜单项**:
        *   "用户反馈" (分类标题)
        *   "查询页"
        *   "仪表盘" (当前选中项)
        *   "当有新的差评时, ..." (自动化任务或规则)
    *   **新建功能**: "新建" 下拉菜单，包含 "导入 Excel"、"数据表"、"收集表"、"仪表盘"、"工作流 New"、"文档"、"文件夹"、"从其他数据源..." 等选项。

**2. 主要内容区域: 仪表盘 (Dashboard)**

*   **仪表盘操作栏**:
    *   当前视图标识: "仪表盘"
    *   操作按钮: "+ 添加组件"、"筛选"、"设置自动化发送"、"主题"。

*   **数据看板模块**:
    *   **模块一: 用户声音 (Word Cloud)**
        *   **标题**: `用户声音`
        *   **内容**: 以词云形式展示用户反馈中的高频词汇，如 "答案"、"展示"、"题目"、"数据"、"课程"、"内容"、"完整"、"职务"、"有误" 等。字体大小代表词频高低。

    *   **模块二: 用户反馈情感分布 (Pie Chart)**
        *   **标题**: `用户反馈情感分布`
        *   **图例**: "正向"、"负向"
        *   **数据**:
            *   正向: 2 (66.67%)
            *   负向: 1 (33.33%)
        *   **Mermaid 表示**:
            ```mermaid
            pieChart
                title 用户反馈情感分布
                "正向" : 66.67
                "负向" : 33.33
            ```

    *   **模块三: 用户反馈提及内容维度 (Bar Chart)**
        *   **标题**: `用户反馈提及内容维度`
        *   **Y轴标签**: "计数"
        *   **数据**: 图表包含三个柱状图，每个柱体顶端数值标记为 "1"。具体的X轴维度分类名称在图片中不清晰。

    *   **模块四: 反馈量趋势 (Line Chart)**
        *   **标题**: `反馈量趋势`
        *   **Y轴标签**: "计数"
        *   **数据**: 图表显示一条水平线，Y轴数值为 "1"，表示反馈量在所示时间区间内（具体时间点不清晰）保持稳定。右侧有一个用户头像图标，可能表示与特定用户或操作者相关。

【============== 图片解析 END ==============】



#### **5.4 生产后台：课程结算页反馈看板**

展示学生在 AI 课结算页面上提交的反馈信息，字段如下：

#### 5.5 智能审核 Agent

目标

- **准确性：** Agent 对文字类问题合理性的判断准确率需达到 80% 以上。
- **效率：** Agent 应在接收到反馈后的 5 秒内完成分析并输出结果。
- **可维护性：** 模型结构清晰，易于迭代和优化。
- **可扩展性：** 能够方便地接入新的问题类型和数据源进行模型训练。
- **监控：** 需要对 Agent 的运行状态和性能进行监控，及时发现和解决问题。
  设计：

### 六、数据需求

#### 6.1 效果追踪

描述计划监控数据效果的方式及所需数据字段

#### 6.2 数据埋点

<table>
<tr>
<td>页面<br/></td><td>模块<br/></td><td>动作<br/></td><td>埋点名称<br/></td><td>图示（如需）<br/></td></tr>
<tr>
<td><br/></td><td><br/></td><td><br/></td><td><br/></td><td><br/></td></tr>
<tr>
<td><br/></td><td><br/></td><td><br/></td><td><br/></td><td><br/></td></tr>
<tr>
<td><br/></td><td><br/></td><td><br/></td><td><br/></td><td><br/></td></tr>
<tr>
<td><br/></td><td><br/></td><td><br/></td><td><br/></td><td><br/></td></tr>
</table>

### 七、a/b 实验需求

> 若有，则写清楚实验方案
> **实验目的**
> XXX
> **实验对象**
> 限制条件：端/版本/用户
> 实验组策略：
> 对照组策略：
> **实验及分流**
> 启动时间、启动时用户量、预计持续时间
> **评价指标**
> 结果指标（标明预期提升值）、过程指标
> **暂无**

### 附：评审记录

> 记录存档历次需求评审中的会议纪要，方便追踪
> 举例：
> **20250204 - 初评纪要**
>
> 1. 会后尽快确定巩固类型本期题目类型范围 @ 杨宇航
>    已确认，并补充进文档
> 2. 数据下发方式待服务端和客户端最终确认 @ 服务端 rd @ 客户端 rd
>    确定服务端下发，客户端仅做兜底逻辑
> 3. ...
>
> **20250321 - 二次评审纪要**
>
> 1. XX

1.