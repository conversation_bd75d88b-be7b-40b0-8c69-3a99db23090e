---
description: 
globs: 
alwaysApply: false
---
# DAO (Data Access Object) 层开发规范 v1.1

## 1. 核心原则

### 1.1 职责边界
- DAO 层**仅负责**数据持久化操作，实现 Domain 层定义的 Repository 接口
- 不包含业务逻辑，只处理数据库 CRUD 和基础查询
- 处理数据库错误并转换为统一错误类型
- 负责 Domain Entity 与 GORM Model 之间的转换

### 1.2 依赖规则
- DAO 实现依赖于 Domain 层定义的接口
- 禁止 Domain 层依赖 DAO 层实现
- 通过 Wire 注入数据库连接和日志组件
- 遵循 CQRS 原则，区分读写操作

## 2. 命名规范

### 2.1 文件命名
- GORM Model 文件：`dao/struct.go`
- DAO 实现文件：`dao/impl/{entity}_impl.go`
- 单元测试文件：`dao/impl/{entity}_impl_test.go`
- Wire Provider 文件：`dao/impl/wire.go`

### 2.2 类型命名
- GORM Model：与表名对应，如 `User`、`UserProfile`
- DAO 结构体：`{entity}DAO`，如 `userDAO`
- 接口：`{Entity}Repository`，如 `UserRepository`
- 查询参数：`{Entity}ListParams`，如 `UserListParams`

### 2.3 方法命名
- 创建：`Create{Entity}`
- 查询：`Get{Entity}By{Field}`
- 列表：`List{Entity}`
- 更新：`Update{Entity}`
- 删除：`Delete{Entity}`
- 事务：`WithTx`
- 批量操作：`Batch{Action}{Entity}`

### 2.4 变量命名
- GORM Model 实例：`{entity}PO`，如 `userPO`
- Domain Entity 实例：`{entity}DO`，如 `userDO`
- 数据库实例：`db`
- 日志实例：`log`
- 上下文：`ctx`

## 3. 实现规范

### 3.1 结构体定义
```go
// app/user/dao/impl/user_impl.go
package impl

import (
    "context"
    "time"
    
    "gorm.io/gorm"
    "github.com/go-kratos/kratos/v2/log"
    
    "your_project/app/user/domain/entity"
    "your_project/app/user/domain/repository"
    "your_project/app/user/dao/model"
    "your_project/app/consts"
    "your_project/app/core/loger/logger_new"
)

type userDAO struct {
    db  *gorm.DB
    log *log.Helper
}

// NewUserDAO 创建用户DAO
func NewUserDAO(db *gorm.DB, logger log.Logger) repository.UserRepository {
    return &userDAO{
        db:  db,
        log: log.NewHelper(logger),
    }
}
```

### 3.2 CRUD操作实现
```go
// CreateUser 创建用户
func (d *userDAO) CreateUser(ctx context.Context, user *entity.User) (*entity.User, error) {
    startTime := time.Now()
    defer func() {
        duration := time.Since(startTime).Milliseconds()
        d.log.Debugf("CreateUser completed in %d ms", duration)
    }()

    // Domain Entity 转换为 GORM Model
    userPO := &model.User{
        Username:  user.Username,
        Email:     user.Email,
        Password:  user.Password,
        Status:    user.Status,
        CreatedAt: user.CreatedAt,
        UpdatedAt: user.UpdatedAt,
    }

    // 执行数据库操作
    if err := d.db.WithContext(ctx).Create(userPO).Error; err != nil {
        d.log.Errorf("创建用户失败: %v", err)
        return nil, d.handleDBError(err)
    }

    // GORM Model 转换为 Domain Entity
    return d.modelToEntity(userPO), nil
}

// GetUserByID 根据ID获取用户
func (d *userDAO) GetUserByID(ctx context.Context, userID int64) (*entity.User, error) {
    startTime := time.Now()
    defer func() {
        duration := time.Since(startTime).Milliseconds()
        d.log.Debugf("GetUserByID completed in %d ms", duration)
    }()

    var userPO model.User
    err := d.db.WithContext(ctx).
        Where("id = ? AND deleted_at = 0", userID).
        First(&userPO).Error

    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, repository.ErrUserNotFound
        }
        d.log.Errorf("查询用户失败: %v", err)
        return nil, d.handleDBError(err)
    }

    return d.modelToEntity(&userPO), nil
}

// UpdateUser 更新用户
func (d *userDAO) UpdateUser(ctx context.Context, user *entity.User) error {
    startTime := time.Now()
    defer func() {
        duration := time.Since(startTime).Milliseconds()
        d.log.Debugf("UpdateUser completed in %d ms", duration)
    }()

    updates := map[string]interface{}{
        "email":      user.Email,
        "status":     user.Status,
        "updated_at": user.UpdatedAt,
    }

    result := d.db.WithContext(ctx).
        Model(&model.User{}).
        Where("id = ? AND deleted_at = 0", user.ID).
        Updates(updates)

    if result.Error != nil {
        d.log.Errorf("更新用户失败: %v", result.Error)
        return d.handleDBError(result.Error)
    }

    if result.RowsAffected == 0 {
        return repository.ErrUserNotFound
    }

    return nil
}

// DeleteUser 软删除用户
func (d *userDAO) DeleteUser(ctx context.Context, userID int64) error {
    startTime := time.Now()
    defer func() {
        duration := time.Since(startTime).Milliseconds()
        d.log.Debugf("DeleteUser completed in %d ms", duration)
    }()

    now := time.Now().UnixMilli()
    result := d.db.WithContext(ctx).
        Model(&model.User{}).
        Where("id = ? AND deleted_at = 0", userID).
        Update("deleted_at", now)

    if result.Error != nil {
        d.log.Errorf("删除用户失败: %v", result.Error)
        return d.handleDBError(result.Error)
    }

    if result.RowsAffected == 0 {
        return repository.ErrUserNotFound
    }

    return nil
}
```

### 3.3 批量操作实现
```go
// BatchCreateUsers 批量创建用户
func (d *userDAO) BatchCreateUsers(ctx context.Context, users []*entity.User) error {
    startTime := time.Now()
    defer func() {
        duration := time.Since(startTime).Milliseconds()
        d.log.Debugf("BatchCreateUsers completed in %d ms, count: %d", duration, len(users))
    }()

    if len(users) == 0 {
        return nil
    }

    // 转换为GORM Model
    userPOs := make([]*model.User, 0, len(users))
    for _, user := range users {
        userPOs = append(userPOs, &model.User{
            Username:  user.Username,
            Email:     user.Email,
            Password:  user.Password,
            Status:    user.Status,
            CreatedAt: user.CreatedAt,
            UpdatedAt: user.UpdatedAt,
        })
    }

    // 批量插入，每批1000条
    const batchSize = 1000
    if err := d.db.WithContext(ctx).CreateInBatches(userPOs, batchSize).Error; err != nil {
        d.log.Errorf("批量创建用户失败: %v", err)
        return d.handleDBError(err)
    }

    return nil
}

// ListUsers 分页查询用户列表
func (d *userDAO) ListUsers(ctx context.Context, params *repository.UserListParams) ([]*entity.User, int64, error) {
    startTime := time.Now()
    defer func() {
        duration := time.Since(startTime).Milliseconds()
        d.log.Debugf("ListUsers completed in %d ms", duration)
    }()

    query := d.db.WithContext(ctx).Model(&model.User{}).Where("deleted_at = 0")

    // 添加查询条件
    if params.Keyword != "" {
        query = query.Where("username LIKE ? OR email LIKE ?",
            "%"+params.Keyword+"%", "%"+params.Keyword+"%")
    }
    if params.Status != nil {
        query = query.Where("status = ?", *params.Status)
    }

    // 查询总数
    var total int64
    if err := query.Count(&total).Error; err != nil {
        d.log.Errorf("查询用户总数失败: %v", err)
        return nil, 0, d.handleDBError(err)
    }

    // 分页查询
    var userPOs []*model.User
    offset := (params.Page - 1) * params.PageSize
    err := query.Offset(int(offset)).
        Limit(int(params.PageSize)).
        Order("created_at DESC").
        Find(&userPOs).Error

    if err != nil {
        d.log.Errorf("查询用户列表失败: %v", err)
        return nil, 0, d.handleDBError(err)
    }

    // 转换为Domain Entity
    users := make([]*entity.User, 0, len(userPOs))
    for _, userPO := range userPOs {
        users = append(users, d.modelToEntity(userPO))
    }

    return users, total, nil
}
```

### 3.4 事务支持
```go
// WithTx 事务支持
func (d *userDAO) WithTx(tx *gorm.DB) repository.UserRepository {
    return &userDAO{
        db:  tx,
        log: d.log,
    }
}

// CreateUserWithProfile 创建用户和用户资料（事务）
func (d *userDAO) CreateUserWithProfile(ctx context.Context, user *entity.User, profile *entity.UserProfile) error {
    startTime := time.Now()
    defer func() {
        duration := time.Since(startTime).Milliseconds()
        d.log.Debugf("CreateUserWithProfile completed in %d ms", duration)
    }()

    return d.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
        // 创建用户
        userPO := &model.User{
            Username:  user.Username,
            Email:     user.Email,
            Password:  user.Password,
            Status:    user.Status,
            CreatedAt: user.CreatedAt,
            UpdatedAt: user.UpdatedAt,
        }

        if err := tx.Create(userPO).Error; err != nil {
            return err
        }

        // 创建用户资料
        profilePO := &model.UserProfile{
            UserID:    userPO.ID,
            Nickname:  profile.Nickname,
            Avatar:    profile.Avatar,
            CreatedAt: profile.CreatedAt,
            UpdatedAt: profile.UpdatedAt,
        }

        return tx.Create(profilePO).Error
    })
}
```

### 3.5 错误处理和转换
```go
// handleDBError 处理数据库错误
func (d *userDAO) handleDBError(err error) error {
    if err == nil {
        return nil
    }

    // 根据不同的数据库错误类型返回相应的业务错误
    switch {
    case errors.Is(err, gorm.ErrRecordNotFound):
        return repository.ErrUserNotFound
    case errors.Is(err, gorm.ErrDuplicatedKey):
        return repository.ErrUserAlreadyExists
    case errors.Is(err, context.DeadlineExceeded):
        return repository.ErrTimeout
    default:
        return repository.ErrInternal
    }
}

// modelToEntity GORM Model转换为Domain Entity
func (d *userDAO) modelToEntity(userPO *model.User) *entity.User {
    if userPO == nil {
        return nil
    }

    return &entity.User{
        ID:        userPO.ID,
        Username:  userPO.Username,
        Email:     userPO.Email,
        Password:  userPO.Password,
        Status:    userPO.Status,
        CreatedAt: userPO.CreatedAt,
        UpdatedAt: userPO.UpdatedAt,
    }
}
```

### 3.6 性能优化
```go
// GetUsersByIDs 批量查询用户（优化版）
func (d *userDAO) GetUsersByIDs(ctx context.Context, userIDs []int64) ([]*entity.User, error) {
    startTime := time.Now()
    defer func() {
        duration := time.Since(startTime).Milliseconds()
        d.log.Debugf("GetUsersByIDs completed in %d ms, count: %d", duration, len(userIDs))
    }()

    if len(userIDs) == 0 {
        return []*entity.User{}, nil
    }

    var userPOs []*model.User
    err := d.db.WithContext(ctx).
        Where("id IN ? AND deleted_at = 0", userIDs).
        Find(&userPOs).Error

    if err != nil {
        d.log.Errorf("批量查询用户失败: %v", err)
        return nil, d.handleDBError(err)
    }

    // 转换并保持顺序
    userMap := make(map[int64]*entity.User)
    for _, userPO := range userPOs {
        userMap[userPO.ID] = d.modelToEntity(userPO)
    }

    users := make([]*entity.User, 0, len(userIDs))
    for _, userID := range userIDs {
        if user, exists := userMap[userID]; exists {
            users = append(users, user)
        }
    }

    return users, nil
}

// CountUsersByStatus 按状态统计用户数量
func (d *userDAO) CountUsersByStatus(ctx context.Context) (map[int32]int64, error) {
    startTime := time.Now()
    defer func() {
        duration := time.Since(startTime).Milliseconds()
        d.log.Debugf("CountUsersByStatus completed in %d ms", duration)
    }()

    type StatusCount struct {
        Status int32 `json:"status"`
        Count  int64 `json:"count"`
    }

    var results []StatusCount
    err := d.db.WithContext(ctx).
        Model(&model.User{}).
        Select("status, COUNT(*) as count").
        Where("deleted_at = 0").
        Group("status").
        Find(&results).Error

    if err != nil {
        d.log.Errorf("统计用户状态失败: %v", err)
        return nil, d.handleDBError(err)
    }

    statusMap := make(map[int32]int64)
    for _, result := range results {
        statusMap[result.Status] = result.Count
    }

    return statusMap, nil
}
```

## 4. 开发要求

### 4.1 技术要求
- 所有int类型使用int64
- 时间字段使用毫秒时间戳
- 使用app/core/loger/logger_new记录日志
- 每个方法添加性能计时统计
- 正确处理GORM错误
- 支持事务操作
- 添加详细中文注释

### 4.2 性能要求
- 批量操作使用CreateInBatches
- 查询使用适当的索引
- 避免N+1查询问题
- 合理使用数据库连接池
- 设置查询超时时间

### 4.3 质量要求
- 单元测试覆盖率 > 90%
- 错误处理完整且统一
- 代码符合项目规范
- 遵循SOLID原则
- 事务：`tx`

### 2.5 错误变量
- 格式：`Err{Entity}{Action}`
- 示例：`ErrUserNotFound`、`ErrUserCreateFailed`

## 3. 错误码规范

### 3.1 错误码定义
```go
const (
    // 通用错误码 (10000-10099)
    ErrCodeInternalServer = 10000
    ErrCodeDBOperation   = 10001
    ErrCodeInvalidParam  = 10002

    // 用户模块错误码 (10100-10199)
    ErrCodeUserNotFound  = 10100
    ErrCodeUserExists    = 10101
    ErrCodeUserInactive  = 10102
    ErrCodeUserLocked    = 10103

    // 其他模块错误码 (10200+)
    // ...
)
```

### 3.2 错误包装规范
```go
// 错误定义
var (
    ErrUserNotFound = errors.New("user not found")
    ErrUserExists   = errors.New("user already exists")
)

// 错误包装
func wrapError(err error, code int, msg string) *errorx.Error {
    return errorx.New(code, msg, err)
}
```

## 4. SQL 查询规范

### 4.1 基础查询规范
```go
// 1. 始终使用 WithContext
db = db.WithContext(ctx)

// 2. 使用参数化查询
db.Where("username = ?", username)

// 3. 显式指定查询字段
db.Select("id", "username", "email")

// 4. 使用索引提示
db.Clauses(hints.UseIndex("idx_username"))
```

### 4.2 分页查询标准
```go
func (d *userDAO) List(ctx context.Context, params *UserListParams) ([]*User, int64, error) {
    var total int64
    db := d.db.WithContext(ctx).Model(&User{})
    
    // 1. 构建查询条件
    if params.Status != nil {
        db = db.Where("status = ?", *params.Status)
    }
    if params.Username != "" {
        db = db.Where("username LIKE ?", "%"+params.Username+"%")
    }
    
    // 2. 获取总数
    if err := db.Count(&total).Error; err != nil {
        return nil, 0, err
    }
    
    // 3. 分页查询
    offset := (params.PageNum - 1) * params.PageSize
    limit := params.PageSize
    if offset > 1000 { // 防止深度分页
        return nil, total, errors.New("page too deep")
    }
    
    var users []*User
    err := db.Offset(int(offset)).
        Limit(int(limit)).
        Order(params.OrderBy + " " + params.GetOrderDirection()).
        Find(&users).Error
    
    return users, total, err
}
```

### 4.3 复杂查询规范
```go
// 使用 Scopes 组织查询条件
func statusCondition(status UserStatus) func(db *gorm.DB) *gorm.DB {
    return func(db *gorm.DB) *gorm.DB {
        return db.Where("status = ?", status)
    }
}

// 使用示例
db.Scopes(statusCondition(UserStatusActive))
```

### 4.4 批量操作规范
```go
// 批量创建
func (d *userDAO) BatchCreate(ctx context.Context, users []*User) error {
    const batchSize = 1000
    for i := 0; i < len(users); i += batchSize {
        end := i + batchSize
        if end > len(users) {
            end = len(users)
        }
        if err := d.db.WithContext(ctx).Create(users[i:end]).Error; err != nil {
            return err
        }
    }
    return nil
}
```

## 5. 性能规范

### 5.1 查询性能指标
- 单次查询响应时间 ≤ 100ms
- 列表查询响应时间 ≤ 200ms
- 批量操作单次限制 1000 条
- 分页查询限制：
  - 最大页码：100
  - 每页最大数量：100
  - 最大 offset：10000

### 5.2 索引使用规范
- 必须使用索引的场景：
  - 主键查询
  - 唯一键查询
  - 外键关联
  - 排序字段
  - 分组字段
- 建议使用索引的场景：
  - 频繁查询的字段
  - 范围查询的字段
  - 模糊查询的前缀字段

### 5.3 性能优化方法
- 使用 `Select` 指定需要的字段
- 避免 `Select *`
- 合理使用 `Preload`
- 使用批量操作替代循环操作
- 适当使用索引提示

## 6. 高级特性实现

### 6.1 分布式事务
```go
type TxOptions struct {
    Propagation    Propagation
    IsolationLevel sql.IsolationLevel
    Timeout       time.Duration
    ReadOnly      bool
}

func (d *userDAO) WithTransaction(ctx context.Context, opts TxOptions, fn func(ctx context.Context) error) error {
    key := "tx_key"
    if tx, ok := ctx.Value(key).(*gorm.DB); ok {
        // 已存在事务，传播行为处理
        return d.handleExistingTx(ctx, tx, opts, fn)
    }
    // 开启新事务
    return d.db.Transaction(func(tx *gorm.DB) error {
        ctx = context.WithValue(ctx, key, tx)
        return fn(ctx)
    }, &sql.TxOptions{
        Isolation: opts.IsolationLevel,
        ReadOnly: opts.ReadOnly,
    })
}
```

### 6.2 多级缓存策略
```go
type CacheStrategy struct {
    L1TTL    time.Duration  // 本地缓存
    L2TTL    time.Duration  // Redis缓存
    MaxItems int            // 本地缓存容量
}

func (d *userDAO) getWithCache(ctx context.Context, key string, fetch func() (interface{}, error)) (interface{}, error) {
    // 1. 查询本地缓存
    if val, ok := d.localCache.Get(key); ok {
        d.metrics.CacheHitRatio.WithLabels("local").Inc()
        return val, nil
    }

    // 2. 查询Redis缓存
    val, err := d.redisCache.Get(ctx, key)
    if err == nil {
        d.metrics.CacheHitRatio.WithLabels("redis").Inc()
        d.localCache.Set(key, val, d.cacheStrategy.L1TTL)
        return val, nil
    }

    // 3. 查询数据库
    val, err = fetch()
    if err != nil {
        return nil, err
    }

    // 4. 更新缓存
    go d.updateCache(ctx, key, val)
    return val, nil
}
```

### 6.3 批量操作优化
```go
type BatchOptions struct {
    Size        int           // 批次大小
    Timeout     time.Duration // 执行超时
    Parallel    int           // 并行度
    RetryCount  int          // 重试次数
}

func (d *userDAO) BatchCreate(ctx context.Context, users []*domain.User, opts BatchOptions) error {
    // 1. 参数校验
    if err := d.validateBatchInput(users); err != nil {
        return err
    }

    // 2. 并行处理
    chunks := d.splitToBatches(users, opts.Size)
    errChan := make(chan error, len(chunks))
    sem := make(chan struct{}, opts.Parallel)

    for _, chunk := range chunks {
        sem <- struct{}{}
        go func(chunk []*domain.User) {
            defer func() { <-sem }()
            // 3. 重试机制
            err := retry.Do(func() error {
                return d.createBatch(ctx, chunk)
            }, opts.RetryCount)
            errChan <- err
        }(chunk)
    }

    // 4. 错误处理
    return d.handleBatchErrors(errChan, len(chunks))
}
```

## 7. 实现示例 (GORM)

### 7.1 数据模型定义 (DO/PO)

```go
// dao/model/user.go (假设这是数据模型)
package model

import "gorm.io/gorm"

type User struct {
    ID        int64  `gorm:"primaryKey"`
    Username  string `gorm:"uniqueIndex"`
    Password  string
    Status    int32
    CreatedAt int64
    UpdatedAt int64
    DeletedAt gorm.DeletedAt `gorm:"index"` // GORM软删除支持
}

// 可选：指定表名
func (User) TableName() string {
    return "users"
}
```

### 7.2 DAO 实现

```go
// dao/impl/user_impl.go
package impl

import (
    "context"
    "errors"
    "fmt"
    "your_project/app/user/dao/model"
    "your_project/app/user/domain"
    "gorm.io/gorm"
)

type userDAO struct {
    db *gorm.DB
}

func NewUserDAO(db *gorm.DB) domain.UserRepository { // 实现 domain.UserRepository 接口
    return &userDAO{db: db}
}

// --- 实现 UserRepository 接口方法 ---

func (d *userDAO) Create(ctx context.Context, user *domain.User) error {
    // 1. Domain -> DO/PO 转换
    do := d.toDO(user)
    // 2. 执行数据库操作
    if err := d.db.WithContext(ctx).Create(do).Error; err != nil {
        // 3. 错误转换 (例如，唯一键冲突)
        // 注意: GORM V2 的错误检查可能需要结合 DriverError
        // if pgErr, ok := err.(*pgconn.PgError); ok && pgErr.Code == "23505" {
        //    return domain.ErrUserExists
        // }
        // 更通用的方式可能是检查 GORM 提供的错误类型或特定 driver 的错误
        if errors.Is(err, gorm.ErrDuplicatedKey) { // 假设GORM能返回此类错误或通过driver判断
             return domain.ErrUserExists // 返回Domain层定义的错误
        }
        return fmt.Errorf("dao create user failed: %w", err) // 返回通用DAO错误
    }
    // 4. 将DB生成的ID回写给Domain对象
    user.ID = do.ID
    return nil
}

func (d *userDAO) FindByID(ctx context.Context, id int64) (*domain.User, error) {
    var do model.User
    if err := d.db.WithContext(ctx).First(&do, id).Error; err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, domain.ErrUserNotFound
        }
        return nil, fmt.Errorf("dao find user by id failed: %w", err)
    }
    // DO/PO -> Domain 转换
    return d.toDomain(&do), nil
}

// --- 其他接口方法实现 --- (例如 FindByUsername, Update, Delete等)

// --- 转换方法 ---
func (d *userDAO) toDO(user *domain.User) *model.User {
    // 注意：实际项目中，转换逻辑可能更复杂，例如处理指针、时间格式等
    return &model.User{
        ID:        user.ID,
        Username:  user.Username,
        Password:  user.Password, // 密码通常在Service层处理哈希，DAO只负责存储
        Status:    user.Status,
        CreatedAt: user.CreateAt,
        UpdatedAt: user.UpdateAt,
    }
}

func (d *userDAO) toDomain(do *model.User) *domain.User {
    return &domain.User{
        ID:        do.ID,
        Username:  do.Username,
        Password:  do.Password, // 通常Domain对象不包含明文或哈希密码，除非业务需要
        Status:    do.Status,
        CreateAt:  do.CreatedAt,
        UpdateAt:  do.UpdatedAt,
    }
}
```

### 7.3 单元测试 (Mock GORM)

-   (需要使用 `go-sqlmock` 或类似库模拟数据库交互)
-   测试重点：SQL 语句是否正确、错误转换是否正确、DO/Domain 转换是否正确。

## 8. 可观察性实现

### 8.1 指标收集
```go
type Metrics struct {
    QueryLatency   metrics.Histogram
    ErrorCounter   metrics.Counter
    CacheHitRatio  metrics.Gauge
}

func (d *userDAO) recordMetrics(ctx context.Context, op string, start time.Time, err error) {
    duration := time.Since(start)
    d.metrics.QueryLatency.WithLabels(op).Observe(duration.Seconds())
    if err != nil {
        d.metrics.ErrorCounter.WithLabels(op, err.Error()).Inc()
    }
}
```

### 8.2 慢查询监控
```go
func (d *userDAO) monitorSlowQuery(ctx context.Context, operation string, threshold time.Duration) func() {
    start := time.Now()
    return func() {
        duration := time.Since(start)
        if duration > threshold {
            d.log.Warn("slow query detected",
                zap.String("operation", operation),
                zap.Duration("duration", duration),
                zap.String("trace_id", trace.FromContext(ctx)),
            )
            d.metrics.SlowQueryCounter.WithLabels(operation).Inc()
        }
    }
}
```

## 9. 错误处理实现

### 9.1 错误定义
```go
type ErrorCategory int

const (
    ErrorCategoryNotFound ErrorCategory = iota
    ErrorCategoryDuplicate
    ErrorCategoryValidation
    ErrorCategorySystem
)

type DAOError struct {
    Category ErrorCategory
    Code     int
    Message  string
    Err     error
}

func (e *DAOError) Error() string {
    return fmt.Sprintf("[%d] %s: %v", e.Code, e.Message, e.Err)
}
```

### 9.2 错误转换
```go
func (d *userDAO) convertError(err error) error {
    if err == nil {
        return nil
    }

    switch {
    case errors.Is(err, gorm.ErrRecordNotFound):
        return &DAOError{
            Category: ErrorCategoryNotFound,
            Code:    404,
            Message: "record not found",
            Err:     err,
        }
    case isPgUniqueViolation(err):
        return &DAOError{
            Category: ErrorCategoryDuplicate,
            Code:    409,
            Message: "duplicate record",
            Err:     err,
        }
    default:
        return &DAOError{
            Category: ErrorCategorySystem,
            Code:    500,
            Message: "internal error",
            Err:     err,
        }
    }
}
```

### 9.3 错误恢复
```go
func (d *userDAO) withRecovery(ctx context.Context, op string, fn func() error) (err error) {
    defer func() {
        if r := recover(); r != nil {
            stack := debug.Stack()
            d.log.Error("panic recovered",
                zap.String("operation", op),
                zap.Any("panic", r),
                zap.ByteString("stack", stack),
            )
            err = &DAOError{
                Category: ErrorCategorySystem,
                Code:    500,
                Message: "internal error",
                Err:     fmt.Errorf("panic: %v", r),
            }
        }
    }()
    return fn()
}
```

## 10. 完整示例

### 10.1 用户DAO实现
```go
// app/user/dao/impl/user_impl.go

package impl

import (
    "context"
    "github.com/pkg/errors"
    "gorm.io/gorm"
    "your-project/app/user/domain"
)

// userDAO 实现 domain.UserRepository 接口
type userDAO struct {
    db  *gorm.DB
    log *zap.Logger
}

// NewUserDAO 创建UserDAO实例
func NewUserDAO(db *gorm.DB, log *zap.Logger) domain.UserRepository {
    return &userDAO{
        db:  db,
        log: log,
    }
}

// Create 创建新用户
func (d *userDAO) Create(ctx context.Context, user *domain.User) error {
    d.log.Info("creating user", zap.String("username", user.Username))
    
    // 1. DO转PO
    userPO := d.toPO(user)
    
    // 2. 唯一性检查
    if err := d.checkUnique(ctx, userPO); err != nil {
        return err
    }
    
    // 3. 创建记录
    if err := d.db.WithContext(ctx).Create(userPO).Error; err != nil {
        d.log.Error("failed to create user", zap.Error(err))
        return errors.Wrap(err, "failed to create user")
    }
    
    // 4. PO转DO
    *user = *d.toDO(userPO)
    return nil
}

// 其他方法实现...
```

## 11. AI行为约束规则

### 11.1 代码生成限制
- 严格按照Domain层定义的接口生成实现
- 不允许自行扩展或添加额外的方法
- 不允许修改已定义的数据结构
- 不允许更改错误码定义
- 禁止生成与业务无关的工具函数

### 11.2 依赖注入规则
- 只允许注入配置文件中明确定义的依赖
- 禁止创建全局变量和单例
- 禁止在DAO层直接初始化外部服务
- 所有依赖必须通过构造函数注入

### 11.3 数据库操作限制
- 禁止直接拼接SQL语句
- 禁止使用原生SQL查询（除非有特殊性能要求）
- 禁止在DAO层进行跨库操作
- 禁止修改数据库连接配置
- 必须使用预编译语句

### 11.4 缓存使用规则
- 只允许缓存配置文件中定义的数据
- 禁止自行决定缓存策略
- 禁止在DAO层实现分布式锁
- 缓存key必须遵循统一前缀规范

### 11.5 错误处理约束
- 只允许使用预定义的错误类型
- 禁止自定义新的错误码
- 必须在DAO层处理所有数据库错误
- 禁止在错误信息中包含敏感数据

### 11.6 日志记录限制
- 只允许使用项目定义的日志接口
- 禁止直接打印到标准输出
- 禁止记录敏感信息
- 日志级别必须符合规范要求

### 11.7 性能优化限制
- 禁止自行添加索引
- 禁止修改数据库配置参数
- 批量操作必须使用预定义的批次大小
- 禁止更改连接池设置

### 11.8 测试代码限制
- 只允许使用指定的测试框架
- 禁止在测试中连接真实数据库
- 必须使用mock数据
- 禁止更改测试配置

### 11.9 代码生成模板
```go
// 标准DAO实现模板
package impl

import (
    "context"
    "your-project/app/user/domain"
)

type userDAO struct {
    db  *gorm.DB
    log *logger.Logger
}

func NewUserDAO(db *gorm.DB, log *logger.Logger) domain.UserRepository {
    return &userDAO{
        db:  db,
        log: log,
    }
}

// 仅实现接口定义的方法
func (d *userDAO) Create(ctx context.Context, user *domain.User) error {
    // 1. 参数校验
    // 2. 模型转换
    // 3. 数据操作
    // 4. 错误处理
    // 5. 结果转换
}

// 其他接口方法实现...
```

### 11.10 禁止生成的代码类型
1. 工具类或辅助函数
2. 中间件代码
3. 配置管理代码
4. 自定义验证器
5. 数据库迁移代码
6. 缓存管理器
7. 连接池管理
8. 性能监控代码

## 12. DAO层单元测试规范

### 12.1 测试文件组织
- 文件命名：`{entity}_impl_test.go`
- 包名：`impl_test`
- 测试用例按照接口方法分组
- 每个方法的测试用例按照场景分类

### 12.2 测试环境设置
```go
type daoTest struct {
    dao    *xxxDAO
    mock   sqlmock.Sqlmock
    db     *gorm.DB
    logger *zap.Logger
}

// 统一的测试环境设置函数
func setupTest(t *testing.T) (*daoTest, func()) {
    // 初始化测试环境
    // 返回清理函数
}
```

### 12.3 必测场景定义
1. **基础CRUD场景**
   - 创建成功
   - 查询存在记录
   - 查询不存在记录
   - 更新成功
   - 删除成功

2. **业务约束场景**
   - 唯一性约束冲突
   - 外键约束检查
   - 必填字段验证
   - 字段长度限制

3. **事务场景**
   - 事务提交
   - 事务回滚
   - 嵌套事务
   - 事务隔离级别

4. **并发场景**
   - 乐观锁冲突
   - 悲观锁超时
   - 死锁检测

5. **异常场景**
   - 数据库连接失败
   - SQL语法错误
   - 超时处理
   - 资源耗尽

### 12.4 Mock规范
1. **SQL Mock规范**
   ```go
   // 1. INSERT语句Mock
   mock.ExpectBegin()
   mock.ExpectExec(`INSERT INTO "users"`).
       WithArgs(sqlmock.AnyArg(), "username", "email").
       WillReturnResult(sqlmock.NewResult(1, 1))
   mock.ExpectCommit()

   // 2. SELECT语句Mock
   mock.ExpectQuery(`SELECT .* FROM "users"`).
       WithArgs(1).
       WillReturnRows(sqlmock.NewRows([]string{"id", "username"}).
           AddRow(1, "test"))

   // 3. UPDATE语句Mock
   mock.ExpectExec(`UPDATE "users"`).
       WithArgs("new_name", 1).
       WillReturnResult(sqlmock.NewResult(0, 1))

   // 4. DELETE语句Mock
   mock.ExpectExec(`DELETE FROM "users"`).
       WithArgs(1).
       WillReturnResult(sqlmock.NewResult(0, 1))
   ```

2. **错误Mock规范**
   ```go
   // 1. 数据库错误
   mock.ExpectQuery(...).WillReturnError(sql.ErrConnDone)

   // 2. 唯一约束错误
   mock.ExpectExec(...).WillReturnError(&pq.Error{
       Code: "23505", // 唯一约束冲突
   })

   // 3. 外键约束错误
   mock.ExpectExec(...).WillReturnError(&pq.Error{
       Code: "23503", // 外键约束冲突
   })
   ```

### 12.5 断言规范
1. **基础断言**
   ```go
   // 1. 错误断言
   require.NoError(t, err)
   require.Error(t, err)
   require.ErrorIs(t, err, gorm.ErrRecordNotFound)

   // 2. 数据断言
   require.Equal(t, expected.ID, actual.ID)
   require.NotNil(t, result)
   require.Len(t, results, 2)

   // 3. Mock断言
   require.NoError(t, mock.ExpectationsWereMet())
   ```

2. **业务断言**
   ```go
   // 1. 状态断言
   require.True(t, user.IsActive)
   require.Equal(t, domain.StatusActive, user.Status)

   // 2. 时间断言
   require.WithinDuration(t, expectedTime, actualTime, time.Second)

   // 3. 事务断言
   require.True(t, mock.ExpectationsWereMet())
   ```

### 12.6 测试数据构建
1. **测试数据生成器**
   ```go
   func createTestUser(t *testing.T) *domain.User {
       return &domain.User{
           Username: fmt.Sprintf("test_user_%d", time.Now().UnixNano()),
           Email:    fmt.Sprintf("<EMAIL>", time.Now().UnixNano()),
           Status:   domain.StatusActive,
       }
   }
   ```

2. **批量数据生成**
   ```go
   func createTestUsers(t *testing.T, count int) []*domain.User {
       users := make([]*domain.User, 0, count)
       for i := 0; i < count; i++ {
           users = append(users, createTestUser(t))
       }
       return users
   }
   ```

### 12.7 性能测试规范
1. **基准测试场景**
   - 单记录操作
   - 批量操作
   - 复杂查询
   - 并发操作

2. **基准测试模板**
   ```go
   func BenchmarkUserDAO_Create(b *testing.B) {
       test, cleanup := setupBenchmark(b)
       defer cleanup()

       b.ResetTimer()
       for i := 0; i < b.N; i++ {
           user := createTestUser(b)
           err := test.dao.Create(context.Background(), user)
           require.NoError(b, err)
       }
   }
   ```

### 12.8 测试覆盖率要求
1. **覆盖率指标**
   - 语句覆盖率：>90%
   - 分支覆盖率：>80%
   - 条件覆盖率：>80%

2. **覆盖率检查命令**
   ```bash
   go test -coverprofile=coverage.out ./...
   go tool cover -html=coverage.out
   ```

### 12.9 测试代码质量要求
1. **代码规范**
   - 测试函数命名：`Test{Struct}_{Method}_{Scenario}`
   - 测试用例描述清晰
   - 合理使用子测试
   - 避免测试代码重复

2. **注释规范**
   - 测试函数必须包含功能说明
   - 复杂场景需要详细的步骤注释
   - 特殊断言需要解释原因

3. **维护性要求**
   - 测试代码可读性高
   - 测试用例独立性
   - 避免全局状态
   - 清理所有测试资源
```

## 13. 标准代码模板

### 13.1 基础结构模板
```go
// DAO结构定义
type xxxDAO struct {
    db     *gorm.DB
    log    *logger.Logger
    cache  cache.Cache
    config *config.Config
}

// 构造函数
func NewXXXDAO(db *gorm.DB, log *logger.Logger, cache cache.Cache, config *config.Config) domain.XXXRepository {
    return &xxxDAO{
        db:     db,
        log:    log,
        cache:  cache,
        config: config,
    }
}
```

### 13.2 CRUD操作模板
```go
// Create操作模板
func (d *xxxDAO) Create(ctx context.Context, entity *domain.XXX) error {
    d.log.Info("creating xxx", logger.Any("entity", entity))
    
    model := &Model{
        // 领域实体转换为数据库模型
    }
    
    if err := d.db.WithContext(ctx).Create(model).Error; err != nil {
        d.log.Error("failed to create xxx", logger.Error(err))
        return errors.Wrap(err, "failed to create xxx")
    }
    
    // 清除相关缓存
    if err := d.cache.Delete(ctx, d.cacheKey(model.ID)); err != nil {
        d.log.Warn("failed to delete cache", logger.Error(err))
    }
    
    return nil
}

// Read操作模板
func (d *xxxDAO) GetByID(ctx context.Context, id int64) (*domain.XXX, error) {
    // 尝试从缓存获取
    if cached, err := d.getFromCache(ctx, id); err == nil {
        return cached, nil
    }
    
    var model Model
    if err := d.db.WithContext(ctx).First(&model, id).Error; err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, errors.Wrap(err, "xxx not found")
        }
        return nil, errors.Wrap(err, "failed to get xxx")
    }
    
    // 设置缓存
    entity := model.ToDomain()
    if err := d.setCache(ctx, entity); err != nil {
        d.log.Warn("failed to set cache", logger.Error(err))
    }
    
    return entity, nil
}

// Update操作模板
func (d *xxxDAO) Update(ctx context.Context, entity *domain.XXX) error {
    d.log.Info("updating xxx", logger.Any("entity", entity))
    
    model := &Model{
        // 领域实体转换为数据库模型
    }
    
    if err := d.db.WithContext(ctx).Updates(model).Error; err != nil {
        return errors.Wrap(err, "failed to update xxx")
    }
    
    // 清除缓存
    if err := d.cache.Delete(ctx, d.cacheKey(model.ID)); err != nil {
        d.log.Warn("failed to delete cache", logger.Error(err))
    }
    
    return nil
}

// Delete操作模板
func (d *xxxDAO) Delete(ctx context.Context, id int64) error {
    d.log.Info("deleting xxx", logger.Int64("id", id))
    
    if err := d.db.WithContext(ctx).Delete(&Model{}, id).Error; err != nil {
        return errors.Wrap(err, "failed to delete xxx")
    }
    
    // 清除缓存
    if err := d.cache.Delete(ctx, d.cacheKey(id)); err != nil {
        d.log.Warn("failed to delete cache", logger.Error(err))
    }
    
    return nil
}
```

### 13.3 事务处理模板
```go
// 事务接口定义
type TxDAO interface {
    WithTx(tx *gorm.DB) domain.XXXRepository
}

// 事务方法实现
func (d *xxxDAO) WithTx(tx *gorm.DB) domain.XXXRepository {
    return &xxxDAO{
        db:     tx,
        log:    d.log,
        cache:  d.cache,
        config: d.config,
    }
}

// 事务使用示例
func (s *service) TransferExample(ctx context.Context) error {
    return s.db.Transaction(func(tx *gorm.DB) error {
        txRepo := s.repo.(TxDAO).WithTx(tx)
        
        // 在事务中执行操作
        if err := txRepo.Create(ctx, entity1); err != nil {
            return err
        }
        
        if err := txRepo.Update(ctx, entity2); err != nil {
            return err
        }
        
        return nil
    })
}
```

### 13.4 批量操作模板
```go
// 批量创建
func (d *xxxDAO) BatchCreate(ctx context.Context, entities []*domain.XXX) error {
    if len(entities) == 0 {
        return nil
    }
    
    d.log.Info("batch creating xxx", logger.Int("count", len(entities)))
    
    models := make([]*Model, 0, len(entities))
    for _, entity := range entities {
        models = append(models, &Model{
            // 领域实体转换为数据库模型
        })
    }
    
    // 使用事务批量插入
    return d.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
        batchSize := 1000
        for i := 0; i < len(models); i += batchSize {
            end := i + batchSize
            if end > len(models) {
                end = len(models)
            }
            
            if err := tx.Create(models[i:end]).Error; err != nil {
                return errors.Wrap(err, "failed to batch create xxx")
            }
        }
        return nil
    })
}

// 批量查询
func (d *xxxDAO) BatchGet(ctx context.Context, ids []int64) ([]*domain.XXX, error) {
    if len(ids) == 0 {
        return nil, nil
    }
    
    d.log.Info("batch getting xxx", logger.Any("ids", ids))
    
    var models []*Model
    if err := d.db.WithContext(ctx).Find(&models, ids).Error; err != nil {
        return nil, errors.Wrap(err, "failed to batch get xxx")
    }
    
    entities := make([]*domain.XXX, 0, len(models))
    for _, model := range models {
        entities = append(entities, model.ToDomain())
    }
    
    return entities, nil
}
```

### 13.5 复杂查询模板
```go
// 分页查询
func (d *xxxDAO) List(ctx context.Context, params *domain.ListParams) (*domain.ListResult, error) {
    d.log.Info("listing xxx", logger.Any("params", params))
    
    var total int64
    var models []*Model
    
    // 构建查询条件
    query := d.db.WithContext(ctx).Model(&Model{})
    if params.Status != 0 {
        query = query.Where("status = ?", params.Status)
    }
    if params.Keyword != "" {
        query = query.Where("name LIKE ?", "%"+params.Keyword+"%")
    }
    
    // 获取总数
    if err := query.Count(&total).Error; err != nil {
        return nil, errors.Wrap(err, "failed to count xxx")
    }
    
    // 分页查询
    if err := query.Offset(params.Offset).Limit(params.Limit).Find(&models).Error; err != nil {
        return nil, errors.Wrap(err, "failed to list xxx")
    }
    
    // 转换为领域实体
    entities := make([]*domain.XXX, 0, len(models))
    for _, model := range models {
        entities = append(entities, model.ToDomain())
    }
    
    return &domain.ListResult{
        Total:   total,
        Records: entities,
    }, nil
}

// 条件查询
func (d *xxxDAO) Query(ctx context.Context, conditions map[string]interface{}) ([]*domain.XXX, error) {
    d.log.Info("querying xxx", logger.Any("conditions", conditions))
    
    var models []*Model
    if err := d.db.WithContext(ctx).Where(conditions).Find(&models).Error; err != nil {
        return nil, errors.Wrap(err, "failed to query xxx")
    }
    
    entities := make([]*domain.XXX, 0, len(models))
    for _, model := range models {
        entities = append(entities, model.ToDomain())
    }
    
    return entities, nil
}
```

### 13.6 缓存处理模板
```go
// 缓存键生成
func (d *xxxDAO) cacheKey(id int64) string {
    return fmt.Sprintf("xxx:%d", id)
}

// 从缓存获取
func (d *xxxDAO) getFromCache(ctx context.Context, id int64) (*domain.XXX, error) {
    var entity domain.XXX
    err := d.cache.Get(ctx, d.cacheKey(id), &entity)
    if err != nil {
        return nil, err
    }
    return &entity, nil
}

// 设置缓存
func (d *xxxDAO) setCache(ctx context.Context, entity *domain.XXX) error {
    return d.cache.Set(ctx, d.cacheKey(entity.ID), entity, d.config.CacheTTL)
}

// 删除缓存
func (d *xxxDAO) deleteCache(ctx context.Context, id int64) error {
    return d.cache.Delete(ctx, d.cacheKey(id))
}
```

### 13.7 错误处理模板
```go
// 错误定义
var (
    ErrNotFound     = errors.New("xxx not found")
    ErrDuplicate    = errors.New("xxx already exists")
    ErrInvalidParam = errors.New("invalid parameter")
)

// 错误转换
func (d *xxxDAO) convertError(err error) error {
    if err == nil {
        return nil
    }
    
    if errors.Is(err, gorm.ErrRecordNotFound) {
        return ErrNotFound
    }
    
    if pqErr, ok := err.(*pq.Error); ok {
        switch pqErr.Code.Name() {
        case "unique_violation":
            return ErrDuplicate
        case "foreign_key_violation":
            return errors.New("related record not found")
        }
    }
    
    return errors.Wrap(err, "database error")
}
```

### 13.8 性能优化模板
```go
// 索引提示
func (d *xxxDAO) QueryWithIndex(ctx context.Context, params *domain.QueryParams) ([]*domain.XXX, error) {
    var models []*Model
    if err := d.db.WithContext(ctx).
        Clauses(hints.UseIndex("idx_status_created_at")).
        Where("status = ?", params.Status).
        Order("created_at DESC").
        Find(&models).Error; err != nil {
        return nil, err
    }
    
    // 转换为领域实体
    return convertToDomainList(models), nil
}

// 批量操作优化
func (d *xxxDAO) BatchUpsert(ctx context.Context, entities []*domain.XXX) error {
    if len(entities) == 0 {
        return nil
    }
    
    // 构建批量upsert语句
    values := make([]string, 0, len(entities))
    args := make([]interface{}, 0, len(entities)*3)
    
    for _, entity := range entities {
        values = append(values, "(?, ?, ?)")
        args = append(args, entity.ID, entity.Name, entity.Status)
    }
    
    query := fmt.Sprintf(`
        INSERT INTO xxx (id, name, status)
        VALUES %s
        ON CONFLICT (id) DO UPDATE SET
            name = EXCLUDED.name,
            status = EXCLUDED.status
    `, strings.Join(values, ","))
    
    return d.db.WithContext(ctx).Exec(query, args...).Error
}
```

## 14. 单元测试模板

### 14.1 基础测试模板
```go
func TestXXXDAO_Create(t *testing.T) {
    // 初始化测试环境
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()
    
    db := newTestDB(t)
    log := mock_logger.NewMockLogger(ctrl)
    cache := mock_cache.NewMockCache(ctrl)
    config := &config.Config{CacheTTL: time.Hour}
    
    dao := NewXXXDAO(db, log, cache, config)
    
    // 准备测试数据
    entity := &domain.XXX{
        Name:   "test",
        Status: 1,
    }
    
    // 设置日志期望
    log.EXPECT().Info(gomock.Any(), gomock.Any()).Times(1)
    
    // 执行测试
    err := dao.Create(context.Background(), entity)
    
    // 验证结果
    assert.NoError(t, err)
    
    // 验证数据库
    var model Model
    err = db.First(&model, entity.ID).Error
    assert.NoError(t, err)
    assert.Equal(t, entity.Name, model.Name)
    assert.Equal(t, entity.Status, model.Status)
}
```

### 14.2 事务测试模板
```go
func TestXXXDAO_Transaction(t *testing.T) {
    // 初始化测试环境
    db := newTestDB(t)
    dao := NewXXXDAO(db, log, cache, config)
    
    // 测试成功场景
    t.Run("success", func(t *testing.T) {
        err := db.Transaction(func(tx *gorm.DB) error {
            txDao := dao.(TxDAO).WithTx(tx)
            
            // 执行事务操作
            if err := txDao.Create(context.Background(), entity1); err != nil {
                return err
            }
            
            if err := txDao.Update(context.Background(), entity2); err != nil {
                return err
            }
            
            return nil
        })
        
        assert.NoError(t, err)
        // 验证数据库状态
    })
    
    // 测试回滚场景
    t.Run("rollback", func(t *testing.T) {
        err := db.Transaction(func(tx *gorm.DB) error {
            txDao := dao.(TxDAO).WithTx(tx)
            
            if err := txDao.Create(context.Background(), entity1); err != nil {
                return err
            }
            
            // 故意返回错误触发回滚
            return errors.New("force rollback")
        })
        
        assert.Error(t, err)
        // 验证数据库状态，确认回滚成功
    })
}
```

### 14.3 性能测试模板
```go
func BenchmarkXXXDAO_BatchCreate(b *testing.B) {
    // 初始化测试环境
    db := newTestDB(b)
    dao := NewXXXDAO(db, log, cache, config)
    
    // 准备测试数据
    entities := make([]*domain.XXX, 1000)
    for i := range entities {
        entities[i] = &domain.XXX{
            Name:   fmt.Sprintf("test_%d", i),
            Status: 1,
        }
    }
    
    // 重置计时器
    b.ResetTimer()
    
    // 执行基准测试
    for i := 0; i < b.N; i++ {
        err := dao.BatchCreate(context.Background(), entities)
        if err != nil {
            b.Fatal(err)
        }
    }
}
```