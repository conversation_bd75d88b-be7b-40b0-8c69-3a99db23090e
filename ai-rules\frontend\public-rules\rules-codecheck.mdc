---
description: 
globs: 
alwaysApply: false
---
以下是为你的技术栈（React + TypeScript + Tailwind）量身定制的简洁版Prompt，专门用于对比`app/components/`下的代码和`task/svg/`中的Figma设计资源：

---

**Prompt：**

作为精通React和Tailwind的前端助手，请执行：

1. **快速对比检查**
- 扫描`app/components/`下的TSX文件
- 对比同级`task/svg/`目录中的Figma导出文件
- 重点检查：
  - Tailwind类名与Figma设计的匹配度（特别是间距、颜色、字体）
  - SVG组件的尺寸/颜色/命名一致性
  - React组件结构与Figma画板的层级对应关系

2. **生成简易差异报告**
```markdown
[差异报告]
组件位置: app/components/[组件名].tsx
问题类型: [布局/样式/图标]
当前代码: [截图/Tailwind代码片段]
Figma规范: [标注尺寸/颜色值]
修复建议: [具体Tailwind类修改方案]
```

3. **一键修改建议**
对明显问题直接提供可替换的Tailwind方案，例如：
```tsx
// BEFORE (不匹配Figma的间距)
<div className="p-2">

// AFTER (使用Figma标注的4px倍数)
<div className="p-[16px]">
```

4. **特别处理**
- 自动校验所有SVG组件的：
  - 宽度/高度是否与设计稿一致
  - 颜色是否使用`currentColor`便于主题切换
  - 是否存在冗余的`<div>`包裹层

**执行要求：**
- 优先处理props传递错误的TypeScript类型问题
- 保持Tailwind的实用类优先原则
- 对不确定的差异用`// TODO-FIGMA:`标注

请先确认理解目录结构后，从最重要的组件开始分析。

---

**使用示例：**
1. 把Figma导出的SVG命名与React组件关联：
   ```bash
   # Figma导出文件
   task/svg/icon-search.svg
   # 对应组件
   app/components/icons/Search.tsx
   ```

2. 快速定位样式差异：
   ```tsx
   // 检测到Figma用text-gray-700但代码用text-gray-600
   <p className="text-gray-600">...</p>
   ```

这个版本省略了复杂的设计系统术语，直接针对你的技术栈和项目结构进行高效比对。需要更关注某个特定方面（如交互状态或暗黑模式支持）可以随时补充。