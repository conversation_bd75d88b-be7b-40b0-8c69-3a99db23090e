PRD - 巩固练习相关策略 - V1.0

# 一、背景和目标

##  需求背景

在 AI 课程的学习流程中，“巩固练习”是承接课堂知识理解与能力内化的关键环节。学生是否能够在练习阶段接触到**适合其能力水平**的题目，直接决定了知识点掌握的效率和质量。

当前阶段，我们正处于系统冷启动初期，题目缺乏足够的答题数据，难以直接使用模型驱动的推荐算法，因此需要通过一定的“人工可控的策略”，尽早建立“能力匹配、难度适配”的练习体验，并为后续数据积累和算法推荐打下基础。

## 项目收益

1. 为不同能力水平的学生提供匹配的练习题
避免“题太难打击信心”或“题太简单浪费时间”，提升学习效率和满意度。
1. 让学生更有效地掌握课程重点知识点
通过对典型题的优先覆盖与逐步难度推进，提升知识掌握度和迁移能力。
1. 为后续算法推荐提供结构化数据积累
每一次基于策略推送的练习和作答，都是训练模型参数的有效样本，便于未来迭代上线 IRT、知识追踪等自动推荐机制。
1. 提升整体产品智能感与完成率
通过系统“主动推题、智能响应”，增强学生对产品智能化的感知。
## 覆盖用户

使用新AI课学习的全部用户



# 二、内容数据结构

## 内容结构

![board_RCLUwZrT1h4bSjbssEocOVGnn3d](https://static.test.xiaoluxue.cn/demo41/prd_images/1747280690537.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

根据提供的图片及其OCR解析文本，分析如下：

1.  **图片类型、关键元素、组成部分、层级化结构及核心作用与价值**

    *   **图片类型**：该图片为一张 **结构图 / 组织结构图**。
    *   **精准提炼图片内关键元素、组成部分**：
        *   **业务树知识点**：作为练习内容的来源或关联。
        *   **巩固练习**：核心主体，代表整个练习的集合。
        *   **题组 (题组1, 题组2, ..., 题组N)**：巩固练习的下一级组成单元，对题目进行分组。
        *   **题目 (题目1, 题目2, 题目3, ..., 题目N)**：题组的组成单元，是练习的基本单位。
        *   **必做题**：题目的一个分类属性。
        *   **非必做题**：题目的另一个分类属性。
    *   **层级化结构阐述元素间关联**：
        1.  顶层是 "业务树知识点"，它引向 "巩固练习"。
        2.  "巩固练习" 包含多个 "题组" (题组1 到 题组N)。
        3.  每个 "题组" 包含多个 "题目" (题目1 到 题目N)。
        4.  每个 "题目" (或至少是"题目N"作为代表) 可以被归类为 "必做题" 或 "非必做题"。
    *   **结合文档上下文，明确各元素在整体需求或技术方案中的核心作用与价值**：
        *   **业务树知识点**：其核心作用是为巩固练习提供内容来源和上下文，确保练习与教学内容紧密相关。价值在于实现个性化、针对性的学习和复习。
        *   **巩固练习**：作为需求的核心功能，其作用是提供一个结构化的练习环境。价值在于帮助学生检验学习效果，强化知识点记忆和理解。
        *   **题组**：作用是将题目按特定逻辑（如难度、知识点子模块等）进行组织，便于管理和学生阶段性练习。价值在于提升练习的条理性和目标性。
        *   **题目**：是练习的基本单元，直接承载知识点的考察。价值在于具体落实知识点的巩固。
        *   **必做题/非必做题**：作用是对题目进行重要性或难度分级。价值在于引导学生分清主次，高效利用练习时间，满足不同层次学生的练习需求。

2.  **各组成部分拆解及功能模块概述**

    *   **业务树知识点管理模块**：
        *   *简要功能概述*：管理和提供与练习内容相关的知识点信息。
    *   **巩固练习模块**：
        *   *简要功能概述*：组织和呈现整个巩固练习的框架，包含若干题组。
    *   **题组模块**：
        *   *简要功能概述*：将题目进行逻辑分组展示，一个巩固练习包含多个题组。
    *   **题目模块**：
        *   *简要功能概述*：展示单个题目内容，是练习的最小单位。
    *   **题目分类模块 (必做/非必做)**：
        *   *简要功能概述*：对题目进行性质分类，标记其是否为必须完成。

3.  **服务端需要提供的功能和返回的数据内容**

    服务端需要提供以下功能和数据内容：
    *   服务端需要能够根据指定的业务树知识点或巩固练习标识，返回对应的巩固练习的整体结构信息。
    *   对于一个巩固练习，服务端需要返回其包含的所有题组的列表。
    *   对于每一个题组，服务端需要返回该题组下的所有题目的列表。
    *   对于每一道题目，服务端需要返回题目的具体内容，以及该题目是必做题还是非必做题的属性信息。
    *   可能还需要提供题组和题目在各自列表中的顺序信息。

4.  **Mermaid 流程图描述**

    该图片是结构图，使用 Mermaid 的 `graph TD` 语法进行描述：

    ```mermaid
    graph TD
        A[业务树知识点] --> B(巩固练习)
        B --> C1(题组1)
        B --> C2(题组2)
        B --> CDots[...]
        B --> CN(题组N)

        C1 --> Q1_1(题目1)
        C1 --> Q1_2(题目2)
        C1 --> Q1_3(题目3)
        C1 --> Q1_dots[...]
        C1 --> Q1_N(题目N)

        C2 --> Q2_1(题目1)
        C2 --> Q2_2(题目2)
        C2 --> Q2_3(题目3)
        C2 --> Q2_dots[...]
        C2 --> Q2_N(题目N)

        CN --> QN_1(题目1)
        CN --> QN_2(题目2)
        CN --> QN_3(题目3)
        CN --> QN_dots[...]
        CN --> QN_N(题目N)

        Q1_N --> T_Compulsory(必做题)
        Q1_N --> T_Optional(非必做题)
        Q2_N --> T_Compulsory
        Q2_N --> T_Optional
        QN_N --> T_Compulsory
        QN_N --> T_Optional
    ```

【============== 图片解析 END ==============】



- 每节课的巩固练习由 1-5 个题组构成，题组对应为和这节课知识点相关的典型题型。
- 每个题组初期配置10道题左右
- 题组中的典型必做题，老师在选题时会打上推荐标签。每个题组中可以有“0-多”个必做题
- 每个题目都有知识点内的难度等级L1 - L5，一个题组内的难度分布会尽量符合正态分布
## 题目难度系数

根据题目的难度等级，每道题目有对应的难度系数。

冷启动阶段的难度系数为人工设定，当有用户作答数据后，会重新离线计算更准确的难度系数。

| 难度等级 | L1 | L2 | L3 | L4 | L5 |
| --- | --- | --- | --- | --- | --- |
| 题目难度系数 | 0.2 | 0.4 | 0.6 | 0.8 | 1.0 |



# 三、策略方案

## 题目推荐策略

### 前置计算

#### 预估题目数量

- 整体题量
1. 根据这个知识点的难度和考试频率，计算基础答题量
基础题量和知识点难度、考试频率相关。考频高的难点应该多练习，而相对简单且低频的知识点，练习量可以减少。

基础答题量=10*难度系数*考频系数   四舍五入取整   [8 , 14]

1. 根据用户当前掌握度，计算动态调整系数 A。默认值为1。
$$
\text{A} = 1 + \alpha\times\frac{\max(0, \theta_{\text{tar}} - \theta_{\text{cur}})}{\theta_{\text{tar}}}
$$

- θ_tar为该知识点对应的目标掌握度
- θ_cur为学生对该知识点的当前掌握度
- α 是“经验调节系数”，用来控制差距对题量的影响强度，默认取0.6
1. 预估题目数量 = 基础答题量 * 动态调整系数 A
1. 全局上下限
- 下限：题组数量 * 2（保证每个题组至少有2道题）
如：预估整体题量为8，共5个题组。则最终的预估整体题量为10

- 上限：15 （避免单次练习量过多）


示例：当前知识点难度系数 0.9，考频系数 1.2 ，学生当前掌握度 θ_cur=0.3，目标掌握度θ_tar=0.8。

1. 计算基础答题量  0.9 * 1.2 * 10 = 10.8 ≈ 11
1. 计算动态调整系数 A = 1 + 0.6 * [(0.8−0.3)/0.8] ≈ 1.375
1. 计算预估题目数量 11 * 1.375 ≈ 15


- 每个题组的题量
巩固练习的定位是需要让学生把典型题都练到，因此题组在设置的时候也应该是典型的，每个题组都同等重要。

把预估的整体题量，平均到每个题组，如果不能整除，则有部分题组会多1题。

如：共10题，3个题组，则每个题组的预估题量为 3,3,4



- 计算时机
1. 业务树完成难度系数和考频系数配置后，离线计算基础答题量
1. 离线更新用户初始掌握度&目标掌握度后，离线计算动态调整系数
1. 用户完成AI课后，更新动态调整系数。


#### 预期掌握度增量

预估每道题对当前用户带来的掌握度变化量作为推荐依据，掌握度增量计算方式详见：[PRD - 掌握度 - V1.0](https://wcng60ba718p.feishu.cn/wiki/J3d0wrEaDiWRy6kj2uVcQKLfnmh)

1. 
$$
P(\text{答对}) = \text{sigmoid}(4 \times (\theta - \beta))
$$
θ为学生的掌握度，β为题目难度系数

Sigmoid 函数的数学表达式为
$$
S(x)=\frac{1}{1 + e^{-x}}
$$

2. 
$$
\text{预期掌握度增量} = P(\text{答对})\times\text{答对掌握度增量}+P(\text{答错})\times\text{答错掌握度增量}
$$

- 示例
用户当前掌握度 0.3，题目难度系数0.6（L3）

- P(答对）≈ sigmoid(4 × (0.3 − 0.6)) ≈ 0.2315
- P(答错）≈  1 - 0.2315 = 0.7685
- 答对掌握度增量 = 0.2 * （0.6-0.3）= 0.06
- 答错掌握度增量 = -0.1 * （0.6-0.3）* 0.5 = -0.015
- 预期掌握度增量 = 0.2315 * 0.06 + 0.7685 * -0.015 ≈ 0.002
- 计算时机
用户每次答题后计算



### 巩固练习题目推荐

#### 整体流程

![board_ANrJwuzNVhn2PnbsQv3cASfrn9e](https://static.test.xiaoluxue.cn/demo41/prd_images/1747280692576.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

好的，我们来看一下这份关于“PRD-巩固练习相关策略-V1.0”文档中的“巩固练习推题”流程图。

1.  **图片解析概述**

    *   **图片类型：** 流程图。
    *   **核心目的：** 该流程图详细定义了在“巩固练习”场景下，系统向用户推荐练习题目的具体逻辑和判断步骤。
    *   **关键元素与组成：**
        *   **起始点：** “巩固练习推题”作为流程的开端。
        *   **决策节点（判断条件）：** 包括“本次练习是否有未作答的题组”、“当前题目是否为题组内首题”、“题组内是否有必做题”、“当前题组是否已达预估答题量”、“剩余可推荐题目的预期掌握度增量是否≥0.01”。这些节点根据不同条件导向不同分支。
        *   **处理步骤（行为操作）：** 包括“按顺序进入题组”、“在题组中选择「预期掌握度增量」最大的题目”、“在必做题中选择「预期掌握度增量」最大的题目”。
        *   **中间状态/流转指示：** “无操作”，这在图中通常表示在特定条件下不执行额外特殊操作，而是流转到下一个公共步骤或结束当前分支逻辑。
        *   **结束点：** “结束”，表示推题流程的终止。
    *   **层级化结构与关联：**
        整个流程以一个核心问题（是否有未作答题组）开始，分叉出主要的处理路径（有未作答题组则进入推题逻辑，否则结束）。在推题逻辑内部，通过一系列嵌套的判断条件（如是否首题、是否有必做题、是否达到预估量、掌握度增量是否达标）来逐步筛选和确定最终要推送给用户的题目。选择题目的核心依据是“预期掌握度增量”最大化，同时兼顾“必做题”的优先级和练习量控制。
    *   **核心作用与价值：**
        此流程的核心价值在于实现智能化、个性化的练习题推荐。它确保了学生能够有序地完成所有题组，优先处理必做题目，并始终以提升“预期掌握度”为目标进行练习。通过对答题量和题目质量（以预期掌握度增量体现）的控制，旨在提高巩固练习的效率和效果，避免盲目刷题，从而更好地帮助学生巩固知识。

2.  **功能模块拆解**

    *   **练习状态校验模块：**
        *   功能概述：判断当前用户的本次巩固练习中是否存在尚未作答的题组。
    *   **题组导航模块：**
        *   功能概述：当存在未作答题组时，引导用户按预定顺序进入下一个未作答的题组。
    *   **首题判断模块：**
        *   功能概述：判断当前准备推荐的题目是否为所在题组内的第一道题目。
    *   **必做题优先推荐模块：**
        *   功能概述：如果当前是题组首题，且题组内存在必做题，则从必做题中筛选出“预期掌握度增量”最大的题目进行推荐。若无必做题，则执行通用题目推荐逻辑。
    *   **通用题目推荐模块：**
        *   功能概述：在非首题情况，或首题但无必做题的情况下，从当前题组的剩余可选题目中，选择“预期掌握度增量”最大的题目进行推荐。
    *   **题组答题量控制模块：**
        *   功能概述：判断当前题组内已推荐（或作答）的题目数量是否已达到该题组的预估答题上限。
    *   **题目推荐有效性模块：**
        *   功能概述：在题组未达预估答题量时，判断剩余可推荐题目中，其“预期掌握度增量”是否存在大于等于特定阈值（如0.01）的题目，以确保推荐题目的有效性。
    *   **流程结束模块：**
        *   功能概述：当所有题组均已作答，或根据逻辑不再有合适题目推荐时，结束本次推题流程。

3.  **服务端需提供的功能与数据内容**

    服务端需要提供以下功能和数据支持：
    *   需要能够查询用户当前巩固练习的整体状态，特别是是否存在未完成作答的题组。
    *   需要维护题组的顺序信息，并能够按此顺序提供下一个未作答的题组。
    *   需要记录用户在当前题组内的答题进度，以便判断当前是否为题组内的首题。
    *   需要能够区分题组内的题目是否为必做题。
    *   核心数据是每道题目的“预期掌握度增量”值，服务端需要能够计算或获取这个值，并能根据此值对题目进行排序和筛选。
    *   需要存储每个题组的预估答题量信息。
    *   需要能够统计用户在当前题组已推荐或已作答的题目数量。
    *   需要能提供题组内剩余可推荐题目的列表及其各自的“预期掌握度增量”。
    *   基于上述信息，服务端的核心功能是根据流程图中的逻辑判断，返回下一道应该推荐给用户的题目信息，或者返回练习结束的信号。

4.  **Mermaid 流程图描述**

    ```mermaid
    graph TD
        A[巩固练习推题] --> B{本次练习是否有未作答的题组};
        B -- 否 --> Z[结束];
        B -- 是 --> C[按顺序进入题组];
        C --> D{当前题目是否为题组内首题};
        D -- 是 --> E{题组内是否有必做题};
        D -- 否 --> F[在题组中选择「预期掌握度增量」最大的题目];
        E -- 是 --> G[在必做题中选择「预期掌握度增量」最大的题目];
        E -- 否 --> F;
        F --> H{当前题组是否已达预估答题量};
        G --> H;
        H -- 是 --> B;
        H -- 否 --> I{剩余可推荐题目的预期掌握度增量是否≥0.01};
        I -- 是 --> F;
        I -- 否 --> B;
    ```

【============== 图片解析 END ==============】



#### 题目推荐策略

1. 巩固练习中的多个题组，需要依次作答。
1. 每次推题默认在当前题组中选择「预期掌握度增量」最大的题目
1. 如果有必做题，优先推荐必做题。
1. 如果有多道题的「预期掌握度增量」相等，随机选择一道。
1. 最大答题数量（不包括错题）不超过预估答题数，最小答题数量为题组数量*2
**限制条件**：

1. 连续两题之间难度最多提升一个等级（防止难度剧烈跳变）
1. 上题答错，不可提升难度等级
1. 上题答对，不可降低难度等级
**特殊情况：**当前题组作答未满2题已没有用户可推荐的题目，则直接进入下一题组。

注：当推荐的题目难度上升/下降时，C端展示对应动效



#### 熔断策略

熔断策略优先级高于题目推荐优先级，当触发熔断时，直接结束巩固练习。会触发熔断的场景如下：

1. 用户连续做错5题 且 题池中没有难度更低的题目：结束巩固练习
1. 用户专注度分值等于3：在用户提交当前题目后，结束巩固练习。




## 再练一次

### 报告页引导

用户完成巩固练习后，系统根据其对当前知识点的掌握情况和本次练习表现，判断是否需要引导用户再练一次

**判断条件：**

1. 用户当前掌握度低于目标掌握度的70% 
1. 巩固练习正确率低于 70%
当同时满足上述2个条件时，报告页面需要引导用户再练一次。



### 判断是否满足再练一次

用户完成巩固练习后，需要根据题库中的剩余可练习题目数量来判断，是否满足再练一次。如果满足则展示入口，如果不满足则不展示入口（详见C端需求）

**判断步骤：**

1. 确定可练习的题组：只有在“巩固练习”环节中出现过错题的题组，才会进入再练一次环节。
1. 计算单个题组的可用题目：找出该题组内学生在“巩固练习”中未作答过，并且其“预期掌握度增量”不小于0.01的题目。将这些筛选出来的题目数量记为“可用题目数”。
1. 判断是否满足再练一次：所有可练习题组中的可用题目数量≥3题时，满足再练一次条件


### 计算预估题目数量

1. 单个题组的预估题量 = Min(“可用题目数”, 该题组在巩固练习中的“预估答题数”)
1. 所有可练习的题组计算出的“预估题量”进行累加，得到整体的“预估题目数量”


### 再练一次推题策略

和正常的巩固练习推题策略相比

1. 再练一次增加了错题重练：每个题组的首题为巩固练习中「预期掌握度增量最大」的错题原题
1. 没有每个题组最少作答2题的限制
![board_COrbwAFQKhxQCjbyoeZcKn4xnmu](https://static.test.xiaoluxue.cn/demo41/prd_images/1747280694562.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

1.  **图片类型、关键元素、结构、核心作用与价值**

    *   **图片类型**：该图片为一张**流程图**。
    *   **关键元素与组成部分**：
        *   **起始点**：“再练一次推题”。
        *   **处理过程**：“筛选巩固练习环节有错题的题组”、“按顺序进入题组”、“在错题中选择「预期掌握度增量」最大的题目”、“在题组中选择「预期掌握度增量」最大的题目”。
        *   **判断条件**：“本次练习是否有未作答的题组”、“当前题组是否已达预估答题量”、“剩余可推荐题目的预期掌握度增量是否≥0.01”。
        *   **结束点**：“结束”。
    *   **层级化结构与关联**：
        该流程图以“再练一次推题”为起点，首先进行“题组筛选”和“练习状态检查”。若有未作答题组，则“按顺序进入题组”进行处理。在单个题组处理中，优先“从错题中推荐”，然后判断“题组答题量是否达标”。若未达标，则进一步判断“剩余题目掌握度增量”，满足条件则“从题组中推荐”，不满足或题组已达量则可能流转到下一个题组或结束当前题组的推荐。整个流程通过条件判断形成分支和循环，直至所有符合条件的题组处理完毕或无题可推，最终导向“结束”。
    *   **核心作用与价值**：
        此流程图定义了“再练一次”场景下向用户推荐练习题目的核心逻辑。其核心作用在于：
        1.  **个性化复习**：优先处理用户在“巩固练习”中出错的题组和题目，针对性强。
        2.  **学习效率优化**：通过“预期掌握度增量”这一指标来筛选题目，旨在为用户推荐能带来最大认知提升的题目。
        3.  **控制练习负荷**：通过“预估答题量”限制单个题组的题目数量，避免用户疲劳。
        4.  **质量门槛**：设置“预期掌握度增量≥0.01”的门槛，确保推荐的题目具有一定的学习价值。
        其价值在于为用户提供一个智能、高效的错题重练和知识点巩固的路径，提升学习效果和用户体验。

2.  **功能模块拆解与概述**

    *   **推题起始模块**：
        *   功能概述：启动“再练一次”的推题逻辑。
    *   **错题题组筛选模块**：
        *   功能概述：从用户的“巩固练习”记录中，筛选出包含有用户答错题目的题组。
    *   **练习状态判断模块**：
        *   功能概述：判断当前“再练一次”会话中，是否存在尚未被处理（作答）的题组。
    *   **题组顺序处理模块**：
        *   功能概述：按照预定顺序，逐个进入符合条件的题组进行题目推荐。
    *   **错题优先推荐模块**：
        *   功能概述：在当前题组内，从用户之前答错的题目中，选择“预期掌握度增量”最大的那一题进行推荐。
    *   **题组答题量判断模块**：
        *   功能概述：判断当前题组已推荐给用户的题目数量是否达到了预设的“预估答题量”。
    *   **题目增量价值判断模块**：
        *   功能概述：判断当前题组内，剩余可推荐题目的“预期掌握度增量”是否存在大于等于特定阈值（0.01）的题目。
    *   **题组择优推荐模块**：
        *   功能概述：在当前题组内（不限于错题），选择“预期掌握度增量”最大的题目进行推荐。
    *   **流程结束模块**：
        *   功能概述：当没有未作答的题组，或其他条件导致无法继续推题时，结束本次推题流程。

3.  **服务端需提供的功能和返回的数据内容**

    服务端需要能够根据用户和当前的练习上下文，提供以下数据和支持相应的功能：
    *   需要提供用户在“巩固练习”环节中包含错题的题组列表。
    *   需要能够判断在当前的“再练一次”会话中，哪些筛选出来的题组是用户还未开始作答的。
    *   对于一个特定的题组，需要提供该题组内用户所有答错的题目信息，并且每道错题都关联一个“预期掌握度增量”的数值。
    *   需要为每个题组设定一个“预估答题量”的数值。
    *   需要能够追踪并判断当前题组已推荐给用户的题目数量是否已达到其“预估答题量”。
    *   对于一个特定的题组，需要提供该题组内所有可推荐题目（不仅仅是错题）的信息，并且每道题目都关联一个“预期掌握度增量”的数值。
    *   服务端需要支持根据“预期掌握度增量”对题目进行排序和选择的功能。
    *   当流程导向结束时，服务端应能正确终止推题。

4.  **Mermaid 流程图描述**

    ```mermaid
    flowchart TD
        A["再练一次推题"] --> B["筛选巩固练习环节有错题的题组"];
        B --> C{"本次练习是否有未作答的题组?"};
        C -- 否 --> Z["结束"];
        C -- 是 --> D["按顺序进入题组"];
        D --> F["在错题中选择<br/>「预期掌握度增量」最大的题目"];
        F --> G{"当前题组是否已达<br/>预估答题量?"};
        G -- 是 --> D;
        G -- 否 --> H{"剩余可推荐题目的<br/>预期掌握度增量是否≥0.01?"};
        H -- 是 --> I["在题组中选择<br/>「预期掌握度增量」最大的题目"];
        I --> G;
        H -- 否 --> G;
    ```

【============== 图片解析 END ==============】





## 不认真答题判断

用户答题专注度与作答时间、用户行为等多种特征相关。V1.0阶段可构建极简版专注度评分系统，仅考虑作答时长及正确率。

**判断方式：**

| 类型 | 事件 |
| --- | --- |
| 作答时长 | 进入题目1s内作答，且回答错误 |
|  | 作答用时小于平均时长1/3，且回答错误（一期不支持） |
| 正确率 | 连续做错3题 |
|  | 做错难度系数低于当前掌握度的题目 |

引入专注度计分，每个巩固练习的初始分值为10分

每次答题后，如命中下述事件，则专注度分值 -1。同时命中多个事件不累计扣分。

**使用场景：**

- 分数等于7时，触发第一次提醒
- 分数等于5时，触发第二次提醒
- 分数等于3时，触发熔断




# 历史存档（不用看）

![board_OVtFw41M0h0jGKbqJ7ucFUChnEb](https://static.test.xiaoluxue.cn/demo41/prd_images/1747280696609.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

根据提供的图片及其OCR解析文本，此图为互联网教育产品中“巩固练习错题”模块的**流程图**（具体可视为一个决策树）。它详细描述了用户在进行巩固练习，特别是处理错题时的题目推送策略和流程判断逻辑。

1.  **图片关键元素、组成部分及层级化结构阐述**

    该流程图的核心目标是智能地为用户推荐巩固练习中的下一道题目，或者结束练习。其价值在于通过一系列判断条件，实现个性化、适应性的练习路径，旨在提高用户对薄弱知识点的掌握程度。

    *   **顶层入口**：
        *   `巩固练习错题`：整个流程的起点。

    *   **第一层决策：练习初始化判断**
        *   `本次练习是否有未答的选择题`：判断当前练习中是否存在用户尚未作答的选择题。
            *   `否` (无未答选择题)：进入对作答情况的判断。
                *   `最后有答错的必做`：判断在已答题目中，是否有做错的必做题。
                    *   `是`：`进入错题重练` (核心动作，引导用户再次练习错题)。
                    *   `否`：`结果←` (流程结束，显示练习结果)。
            *   `是` (有未答选择题)：进入更复杂的题目推荐逻辑。

    *   **第二层决策：题目推荐核心逻辑 (当存在未答选择题时)**
        *   `题组内是否有未排序的必做卷` (应理解为“题组内是否有未按特定顺序处理的必做题”)：判断当前题组中是否存在尚未经过排序/优先处理的必做题目。
            *   `是` (存在未排序必做题)：进入基于题目类型和用户表现的精细化推题逻辑。
                *   `当前题目是否为巩固练习内容题`：判断当前待选题目是否属于本次巩固练习的核心内容。
                    *   `是` (巩固练习内容题)：
                        *   `基础组题是否已达预估答题量`：判断基础题型的练习量是否已达标。
                            *   `是` (已达标)：`在题组的全题型中选择"题目难度系数*用户知识点掌握度;绝对值最小的题目"` (推题策略A：选择综合匹配度最高的题目)。
                            *   `否` (未达标)：根据用户连续答题表现调整。
                                *   `连续2题错误`：
                                    *   `是否有比上一题难度更低的必做题`：
                                        *   `有`：`在难度更低的题目中选择"题目难度系数*用户知识点掌握度;绝对值最小的题目"` (降级推题策略B)。
                                        *   `没有`：执行推题策略A。
                                *   `连续2题正确` (OCR中缺失此分支的直接父级，但逻辑上与“连续2题错误”对应)：
                                    *   `是否有比上一题难度更高的题目`：
                                        *   `有`：`在难度更高的题目中选择"题目难度系数*用户知识点掌握度;绝对值最小的题目"` (升级推题策略C)。
                                        *   `没有`：`结束` (练习结束)。
                    *   `否` (非巩固练习内容题，但仍为未排序必做题)：根据用户连续答题表现调整。
                        *   `连续2题错误`：
                            *   `是否有比上一题难度更低的必做题`：
                                *   `有`：执行降级推题策略B。
                                *   `没有`：执行推题策略A。
                        *   `连续2题正确` (逻辑对称，但OCR中此分支下未明确列出，可能直接结束或复用其他逻辑，实际图中应存在)：(根据对称性和其他分支，推测与巩固练习内容题的“连续2题正确”逻辑相似)
                            *    (推测) `是否有比上一题难度更高的题目`：
                                *    (推测) `有`：执行升级推题策略C。
                                *    (推测) `没有`：`结束`。
            *   `否` (不存在未排序必做题，或该路径已处理完毕)：进入备选推题逻辑或结束。
                *   `任必做题中选择"按照掌握难度增重;最大的题目"←` (推题策略D：选择掌握最差的必做题，此处 OCR 的箭头指向不明，通常表示一个 fallback 或并行逻辑的入口)。
                *   (紧随其后) `当前题目是否为巩固练习内容题`：再次判断选出题目的类型 (这表明策略D选出的题目需要再次分类处理)。
                    *   `是` / `否`：均进入基于用户答题情况的适应性调整。
                        *   `用户的答题情况`：
                            *   `连续2题正确`：
                                *   `是否有比上一题难度更高的题目`：
                                    *   `有`：执行升级推题策略C。
                                    *   `没有`：`结束`。
                            *   `其他` (包括连续2题错误或非连续表现)：`在剩余的必做题中选择"题目难度系数*用户知识点掌握度;绝对值最小的题目"` (推题策略E：从剩余题目中选择综合匹配度最高的)。
        *   `否` (顶层“题组内是否有未排序的必做卷”为否)：`结果←` (流程结束，显示练习结果)。

    **核心作用与价值**:
    该流程的核心价值在于实现智能、个性化的错题巩固。通过动态评估用户的答题情况（如连续正确/错误次数）、题目属性（如难度、是否巩固内容）以及用户对知识点的掌握程度，系统能够推送最适合当前用户的题目，避免了盲目刷题，旨在高效提升薄弱环节，最终提升学习效果。

2.  **功能模块拆解及概述**

    *   **初始状态检查模块**：
        *   功能概述：判断练习开始时是否存在未作答题目，以及历史作答中是否有错误的必做题。
    *   **错题重练触发模块**：
        *   功能概述：当满足特定条件（如无未答题但有错误必做题）时，引导用户进入专门的错题重练环节。
    *   **核心推题决策模块**：
        *   功能概述：当存在未作答题目时，根据题组情况、题目类型、用户表现等因素决定推送哪道题目。
    *   **题目属性判断模块**：
        *   功能概述：判断题目是否为“巩固练习内容题”、“基础组题”，以及是否达到“预估答题量”。
    *   **用户表现跟踪模块**：
        *   功能概述：跟踪用户连续答对或答错的题目数量。
    *   **动态难度调整模块**：
        *   功能概述：根据用户连续答题表现，判断是否需要推送更难或更容易的题目。
    *   **题目选择策略模块**：
        *   功能概述：包含多种题目选择算法，如：“题目难度系数*用户知识点掌握度;绝对值最小”、“难度更低/更高中选择该绝对值最小”、“全题型中选择该绝对值最小”、“按掌握难度增重最大”、“剩余必做题中选择该绝对值最小”。
    *   **练习结束/结果展示模块**：
        *   功能概述：在满足特定条件（如无合适题目、达到目标等）后，结束练习并展示结果。

3.  **服务端需提供的功能和数据内容**

    服务端需要提供以下功能和数据以支持此流程：

    *   **用户练习状态管理**：
        *   需要能记录和查询用户当前巩固练习的整体状态，包括：
            *   本次练习中所有题目的列表及其作答状态（未答、已答对、已答错）。
            *   用户是否有未作答的选择题。
            *   用户是否有答错的必做题。
            *   用户连续答对题目的数量。
            *   用户连续答错题目的数量。
            *   用户上一道作答题目的信息（尤其是难度）。
            *   基础组题的作答数量以及预估总答题量。

    *   **题目信息管理**：
        *   需要存储和查询每道题目的详细属性，包括：
            *   题目ID。
            *   题目是否为“巩固练习内容题”。
            *   题目是否为“必做题”。
            *   题目的难度系数。
            *   题目所属知识点。
            *   题目是否属于“未排序的必做卷”（或标记其处理优先级）。
            *   题目的题型信息（用于“全题型中选择”的逻辑）。

    *   **用户知识点掌握度管理**：
        *   需要存储和查询用户对各个知识点的掌握度数据。

    *   **题目推荐算法实现**：
        *   服务端需要实现核心的题目选择算法逻辑：
            *   计算“题目难度系数 * 用户知识点掌握度”的绝对值。
            *   根据“掌握难度增重”对题目进行排序和选择。
            *   能够根据条件（如在更低/更高难度、全题型、剩余必做题）筛选题目池，并从中选出最优题目。
            *   判断是否有比上一题难度更高或更低的符合条件的题目。

    *   **流程状态推进**：
        *   根据客户端的用户操作和当前状态，服务端能够判断并返回下一步的指令（如推送新题目、进入错题重练、结束练习并返回结果）。

    服务端返回的数据内容（针对推题请求）：
    *   下一道推荐的题目信息（题目ID、题干、选项等用于展示的内容）。
    *   或者，练习结束的指令及练习结果摘要。
    *   或者，进入错题重练的指令。

4.  **Mermaid 流程图**

    ```mermaid
    flowchart TD
        S[巩固练习错题] --> C1{本次练习是否有未答选择题};
        C1 -- 否 --> C2{最后有答错的必做};
        C2 -- 是 --> A1[进入错题重练];
        C2 -- 否 --> R1[结果 ←];

        C1 -- 是 --> C3{题组内是否有未排序的必做题};
        C3 -- 否 --> C3_No_Path;
        C3_No_Path --> A_Fallback_Select[任必做题中选择<br/>"按照掌握难度增重最大的题目" ←];
        A_Fallback_Select --> C_Fallback_Type{当前题目是否为巩固练习内容题};

        C_Fallback_Type -- 是 --> C_Fallback_Yes_State{用户的答题情况};
        C_Fallback_Yes_State -- 连续2题正确 --> C_Fallback_Yes_Higher{是否有比上一题难度更高的题目};
        C_Fallback_Yes_Higher -- 有 --> A_Fallback_Yes_SelectHigher[在难度更高的题目中选择<br/>"题目难度系数*用户知识点掌握度<br/>绝对值最小的题目"];
        C_Fallback_Yes_Higher -- 没有 --> E_Fallback_Yes_End[结束];
        C_Fallback_Yes_State -- 其他 --> A_Fallback_Yes_SelectOther[在剩余的必做题中选择<br/>"题目难度系数*用户知识点掌握度<br/>绝对值最小的题目"];

        C_Fallback_Type -- 否 --> C_Fallback_No_State{用户的答题情况};
        C_Fallback_No_State -- 连续2题正确 --> C_Fallback_No_Higher{是否有比上一题难度更高的题目};
        C_Fallback_No_Higher -- 有 --> A_Fallback_No_SelectHigher[在难度更高的题目中选择<br/>"题目难度系数*用户知识点掌握度<br/>绝对值最小的题目"];
        C_Fallback_No_Higher -- 没有 --> E_Fallback_No_End[结束];
        C_Fallback_No_State -- 其他 --> A_Fallback_No_SelectOther[在剩余的必做题中选择<br/>"题目难度系数*用户知识点掌握度<br/>绝对值最小的题目"];
        
        %% This R2 result was actually at the very end of OCR, with indent matching C1, it's an error in OCR if it means it is a peer to C1's branches.
        %% Assuming it's the termination for C3 if NO unprioritized mandatory questions exist, and ALSO NO OTHER mandatory questions can be picked by fallback.
        %% However, OCR structure for C3--否 immediately goes to A_Fallback_Select.
        %% The original OCR shows "否 └── 结果←" at the same level as C3's "是" and "否" branches.
        %% This indicates if C3 is "否" at the higher level (no unprioritized mandatory questions), then the complex fallback above is triggered.
        %% The OCR does not show a direct path from C3--否 to "结果←" if A_Fallback_Select exists.
        %% So I am removing a direct R2 connection from C3--否.
        %% The only other "结果←" is at same indent as C1, indicating it's for an earlier state. This seems to be a documentation error in the OCR if it's supposed to be from C3.
        %% For now, strictly following the main path of C3--否 to A_Fallback_Select.

        C3 -- 是 --> C4{当前题目是否为巩固练习内容题};
        C4 -- 是 --> C5{基础组题是否已达预估答题量};
        C5 -- 是 --> A2[在题组的全题型中选择<br/>"题目难度系数*用户知识点掌握度<br/>绝对值最小的题目"];
        C5 -- 否 --> C6_State{用户答题状态};
        C6_State -- 连续2题错误 --> C7{是否有比上一题难度更低的必做题};
        C7 -- 有 --> A3[在难度更低的题目中选择<br/>"题目难度系数*用户知识点掌握度<br/>绝对值最小的题目"];
        C7 -- 没有 --> A2;
        C6_State -- 连续2题正确 --> C8{是否有比上一题难度更高的题目};
        C8 -- 有 --> A4[在难度更高的题目中选择<br/>"题目难度系数*用户知识点掌握度<br/>绝对值最小的题目"];
        C8 -- 没有 --> E1[结束];
        C6_State -- 其他 --> NextOrLoop1[继续或选择默认题目];


        C4 -- 否 --> C10_State{用户答题状态};
        C10_State -- 连续2题错误 --> C11{是否有比上一题难度更低的必做题};
        C11 -- 有 --> A5[在难度更低的题目中选择<br/>"题目难度系数*用户知识点掌握度<br/>绝对值最小的题目"];
        C11 -- 没有 --> A6[在题组的全题型中选择<br/>"题目难度系数*用户知识点掌握度<br/>绝对值最小的题目"];
        C10_State -- 连续2题正确 --> C12{是否有比上一题难度更高的题目};
        C12 -- 有 --> A7[在难度更高的题目中选择<br/>"题目难度系数*用户知识点掌握度<br/>绝对值最小的题目"];
        C12 -- 没有 --> E2[结束];
        C10_State -- 其他 --> NextOrLoop2[继续或选择默认题目];
    ```
    *(Note: The OCR indicates "连续2题错误" and "连续2题正确" as distinct states/paths rather than sequential checks. For states not explicitly "2 wrong" or "2 right" (i.e., "其他"), I've added a placeholder "NextOrLoop" as the OCR doesn't fully specify the behavior for the C5--否 and C4--否 branches if neither 2-correct nor 2-wrong is met. The fallback path (C3_No_Path) is more explicit with an "其他" branch.)*

【============== 图片解析 END ==============】

































