---
description: 
globs: 
alwaysApply: false
---

## 1. 目的

本文档旨在定义何时以及如何导出 PNG 和 SVG 格式的静态资源，这是 Phase 2.3 的核心依据。静态资源（如图标、人物 IP 图像、背景图等）是前端开发中不可或缺的一部分，规范化的资源导出和管理有助于提高开发效率和代码一致性。AI 在生成代码时应及时识别并导出设计稿中的相关资源，尤其是一些前端代码难以直接实现的样式或图像。

## 2. 静态资源的定义

静态资源是指在前端开发过程中需要导出的图像文件（如 PNG、SVG），这些资源通常用于 UI 展示和交互。静态资源包括但不限于以下类型：

* **SVG**：用于矢量图形，常用于图标、插图等，能够在不同分辨率下自适应缩放。
* **PNG**：用于位图，支持透明背景，常用于图标、按钮、背景图片等。
* **人物 IP 图像**：此类图像包括设计中的人物插图或品牌形象图像，通常由设计师创作并作为独特的设计元素。

## 3. 什么时候导出静态资源

### 3.1 静态资源的导出时机

静态资源的导出应基于设计稿的需求和开发进度，主要包括以下时机：

* **设计初稿完成后**：当设计稿中的静态资源（如图标、插图、人物图像、背景图等）被确定后，AI 应及时导出资源以便开发使用。
* **组件开发过程中**：当某个组件依赖于图标或其他静态资源时，AI 在生成组件代码时，应根据设计稿自动导出相应的资源。
* **页面布局调整后**：当设计稿中的布局发生调整，涉及到新的图标或图片时，AI 应导出新资源并替换旧资源。
* **状态变化时**：例如，按钮有不同状态下的图标（hover、active、disabled），这些资源应在状态变化时导出。

### 3.2 静态资源的优先级

导出时应优先考虑以下几类静态资源：

* **UI 组件必需资源**：如按钮图标、导航图标、图形等，这些资源需要优先导出。
* **人物 IP 图像**：如涉及到品牌人物图标或插图等需要独特表达的图像，应优先导出并确保其格式和尺寸的准确性。
* **背景图和装饰性图像**：这些资源可以根据页面开发的进度逐步导出。
* **前端代码难以直接实现的样式**：一些复杂的样式（如渐变背景图、细节装饰图案等）应通过导出静态资源的方式进行实现。

## 4. 如何导出静态资源

### 4.1 从 Figma 中提取资源

AI 在自动化过程中应能够准确识别设计稿中的静态资源，特别是那些无法通过代码直接完成的复杂样式。提取时应遵循以下规则：

* **图标与图片**：图标、按钮图标、插图等图形资源需要从 Figma 中提取，并导出为合适的格式（SVG 或 PNG）。
* **人物 IP 图像**：AI 应识别并导出 Figma 中的品牌人物插图或 IP 图像，这些通常是设计稿中独特的图像资源，应根据需要选择 PNG 或 SVG 格式导出。
* **背景图和装饰性图像**：如果设计中有背景图或装饰性元素，这些也需要提取并导出为 PNG 格式。
* **复杂样式的导出**：例如具有渐变、纹理或其他复杂视觉效果的背景图，应通过导出静态图像的方式处理。

### 4.2 导出资源的格式与尺寸

静态资源应导出为 PNG 或 SVG 格式，具体规则如下：

* **SVG 格式**：对于图标、插图和其他矢量图形资源，优先选择 SVG 格式，确保在不同屏幕尺寸下的缩放效果。
* **PNG 格式**：对于需要透明背景的图标、背景图、人物图像等，导出为 PNG 格式，确保文件的清晰度和透明度。
* **尺寸与分辨率**：根据设计稿要求，静态资源应导出多个分辨率（如 1x, 2x, 3x），确保能够适应不同设备的需求。

#### 示例：

* **按钮图标**：导出为 PNG 格式，尺寸为 24x24px、48x48px、72x72px 适配不同屏幕分辨率。
* **SVG 图标**：导出为适应所有屏幕的 SVG 格式，确保其矢量特性。
* **人物图像**：人物 IP 图像可导出为 PNG 格式，保留其高质量细节。

### 4.3 静态资源命名规范

导出的静态资源应符合统一的命名规范，确保资源与 UI 元素一一对应，方便开发人员和设计人员使用：

* **图标资源**：根据图标功能命名，如 `icon-close.svg`, `icon-search.png`。
* **人物 IP 图像**：根据人物或品牌命名，如 `character-hero.png`, `character-villain.svg`。
* **背景图和装饰图像**：根据用途命名，如 `background-homepage.png`, `background-footer.png`。

#### 示例：

```json
{
  "icon-close": "icon-close.svg",
  "icon-search": "icon-search.svg",
  "character-hero": "character-hero.png",
  "background-homepage": "background-homepage.png",
  "button-icon-normal": "button-icon-normal.svg",
  "button-icon-hover": "button-icon-hover.svg"
}
```

### 4.4 自动化导出与集成

为了提高工作效率，建议使用 Figma 插件或 API 进行静态资源的自动导出。AI 可以通过以下方式进行自动化集成：

* **Figma 插件**：使用 Figma 插件自动导出图标、插图、背景图等静态资源，并将其上传至指定存储位置。
* **CI/CD 集成**：将静态资源提取和上传集成到 CI/CD 流程中，确保在代码构建和部署时，静态资源能够自动更新。

## 5. 静态资源的管理与优化

### 5.1 静态资源的存储

所有导出的静态资源应存储在统一的文件夹结构中，按类型分类，便于后续查找和维护。例如：

* **图标资源**：存放在 `apps/{app_name}/public/icons/` 文件夹。
* **人物 IP 图像**：存放在 `apps/{app_name}/public/characters/` 文件夹。
* **背景图与装饰图像**：存放在 `apps/{app_name}/public/backgrounds/` 文件夹。

### 5.2 静态资源的优化

为了提升网页加载速度，静态资源需要进行适当的优化：

* **图标优化**：SVG 文件应去除不必要的元数据，精简路径以减少文件大小。
* **PNG 优化**：PNG 文件应进行无损压缩，减少文件体积，保持高质量显示。


