---
description: 用户故事和需求管理规范，适用于所有需求相关文档。
globs: user-stories/**/*.us.md, user-stories/**/*.v*.us.md
---
# Requirements Management Rules

## 用户故事管理规范
- 文件组织
  - 存放目录：ai-generates/user-stories/
  - 文件后缀：.us.md
  - 按业务模块组织子目录
  - 命名格式：`[模块]-[功能]-[序号].us.md`

- 故事文档结构
  ```markdown
  # [故事标题]
  
  ## 作为（As a）
  [角色]
  
  ## 我想要（I want）
  [期望实现的功能]
  
  ## 以便于（So that）
  [实现的业务价值]
  
  ## 验收标准（Acceptance Criteria）
  - [ ] [验收项1]
  - [ ] [验收项2]
  
  ## 补充信息
  - 业务规则：
    * [规则1]
    * [规则2]
  - 技术要求：
    * [要求1]
    * [要求2]
  
  ## 关联文件
  - Feature：[路径]
  - API文档：[路径]
  - 原型设计：[路径]
  ```

- 故事开发流程
  * 严格遵循以下顺序：
    1. 创建用户故事文档
    2. 等待用户故事和验收标准的确认
    3. 仅在用户明确要求时才开始编写 Feature 文件
    4. 继续后续开发步骤
  * 禁止在用户故事未确认前编写 Feature 文件
  * 禁止在用户未明确要求前开始编写 Feature 文件
  * Feature 文件创建需要等待用户的明确指示

- 需求与 Feature 关联
  - 每个需求文档对应一个或多个 feature 文件
  - 在需求文档中记录关联的 feature 文件路径
  - 在 feature 文件头部注释关联的需求文档

- 版本管理
  - 版本文件命名：`[原文件名].v[版本号].us.md`
  - 每个版本文件需要包含：
    * 变更说明
    * 变更原因
    * 影响范围
    * 关联文件更新

## 最佳实践
- 故事编写
  - 一个故事专注于一个完整的业务价值
  - 故事要足够小，能在一个迭代内完成
  - 验收标准要具体且可测试
  - 避免技术实现细节

- 故事评审
  - 业务人员和开发人员共同评审
  - 确保故事描述清晰完整
  - 验证验收标准的合理性
  - 评估技术可行性
  - 必须在故事评审通过后才能进入下一步骤

- 关联管理
  - 及时更新关联文件
  - 保持文档间的一致性
  - 定期检查关联有效性