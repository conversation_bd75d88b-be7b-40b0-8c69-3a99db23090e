PRD - 课中练习推题策略 - V1.0

# 一、背景和目标

##  需求背景

在AI课堂教学过程中，为了提升学生的专注度和课堂互动感，避免单向知识输入，需在课程中合理穿插练习题组件。

课中练习题主要用于巩固教师讲解内容，帮助学生即时检验对知识点的理解情况，同时通过适当的练习密度与反馈机制，增强课堂节奏感与参与感。

因此，需要制定一套清晰可执行的课中推题策略，确保练习题推送符合教学节奏，兼顾知识巩固的目的。

## 项目收益

1. 提升学习效果：通过实时练习巩固课堂所学内容，帮助学生在知识点讲解后及时验证理解，降低遗忘率。
1. 增强课堂互动与专注度：通过答题互动打破单向讲授模式，提高学生参与感和课堂活跃度，延长课堂专注时长。
1. 及时发现学习问题：通过答错推送相似题，帮助学生暴露理解误区，实现即时纠偏，提高学习精准度。
## 覆盖用户

使用新AI课学习的全部用户



# 二、内容数据结构

## 内容结构

![board_AFnaw4y5nhDgPybpTZXcJdthnTe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746846294952.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

好的，我们来一起分析这张关于“PRD - 课中练习推题策略 - V1.0”文档中的图片。

1.  **图片类型、关键元素、层级结构及核心作用**

    *   **图片类型**：该图片是一张**层级结构图**（或称为树状结构图）。
    *   **出处**：需求文档 (PRD - 课中练习推题策略 - V1.0)。
    *   **关键元素**：
        *   单节课 (Single Lesson)
        *   练习组件 (Exercise Component)
        *   题目 (Question/Problem)
        *   题目变体 (Question Variant, e.g., 题目3', 题目3'')
    *   **层级结构与关联**：
        1.  **顶层**：`单节课`，是所有练习内容的最高层级容器。
        2.  **第二层**：`单节课`下包含若干个`练习组件`（如图中的`练习组件1`和`练习组件2`）。每个`练习组件`是`单节课`的直接组成部分。
        3.  **第三层**：每个`练习组件`下包含若干个`题目`（如`练习组件1`下的`题目1`、`题目2`、`题目3`、`题目3'`、`题目3''`）。这些`题目`是`练习组件`的直接组成部分。
        4.  **题目变体**：部分`题目`存在变体形式（如`题目3`有`题目3'`和`题目3''`两种变体；`题目4`有`题目4'`变体；`题目5`有`题目5'`、`题目5''`、`题目5'''`三种变体）。这些变体与原题目属于同一组，并在同一`练习组件`下。
    *   **核心作用与价值**：
        该结构图清晰地展示了一节课内练习内容的组织方式。在“课中练习推题策略”这一需求背景下，这个结构至关重要：
        *   **内容组织基础**：定义了练习题目的归属关系，明确了题目是属于哪个练习组件，而练习组件又属于哪一节课。
        *   **策略制定依据**：为推题策略提供了结构化依据。例如，系统可以根据学生在某个`练习组件`内的表现，决定是推送该组件内的其他`题目`、同一`题目`的变体，还是进入到下一个`练习组件`。
        *   **题目多样性管理**：通过`题目`的变体（如 `题目3'`、`题目3''`）设计，支持了诸如错题重练（不同形式）、知识点巩固（不同角度）或防止题目重复等教学需求，丰富了推题的可能性。
        *   **模块化教学**：`练习组件`的设计体现了模块化思想，可能对应课内不同的知识点、技能点或教学环节，便于针对性地推送练习。

2.  **各组成部分功能模块拆解**

    *   **单节课 (Single Lesson)**：
        *   **功能概述**：代表一个完整的教学单元或一节课程。它是所有练习组件和题目的顶层容器，定义了本次学习活动的内容范围。
    *   **练习组件 (Exercise Component)**：
        *   **功能概述**：`单节课`内部的逻辑分组单元。通常包含一组围绕特定主题、知识点或技能点的题目。例如，`练习组件1`和`练习组件2`可能分别对应课程中的不同学习阶段或不同类型的练习。
    *   **题目 (Question/Problem)**：
        *   **功能概述**：最小的练习单元，是学生需要直接作答的具体问题。例如`题目1`、`题目2`、`题目3`、`题目4`、`题目5`。
    *   **题目变体 (Question Variant)**：
        *   **功能概述**：指与某一基础题目相关联，但在形式、数值或侧重点上有所不同的题目版本。例如 `题目3'` 和 `题目3''` 是 `题目3` 的变体。它们用于提供多样化的练习体验，如不同难度的同知识点题目，或相同题目逻辑的不同表述。

3.  **服务端需提供的功能和返回的数据内容描述**

    服务端需要能够根据请求（例如，基于当前课程ID和用户进度），提供课程内练习的结构化数据。具体来说：
    *   服务端应能返回一节课的整体信息。
    *   针对这一节课，服务端需提供其包含的所有练习组件的列表。
    *   对于每一个练习组件，服务端需提供该组件下所有题目的列表。
    *   对于每一道题目，服务端需要提供该题目的具体内容信息。
    *   如果一道题目存在一个或多个变体题目，服务端需要标识出这些变体题目，并提供它们与主题目的关联关系以及各自的具体内容。例如，需要明确指出`题目3'`和`题目3''`是与`题目3`相关的变体，并分别提供这三道题目的内容。同样，需要提供`题目4`及其变体`题目4'`的内容和关联；以及`题目5`和其变体`题目5'`、`题目5''`、`题目5'''`的内容和关联。

4.  **Mermaid 流程图描述**

    由于该图是层级结构图，使用 Mermaid 的 `graph TD` (Top Down flowchart) 语法进行描述最为合适：

    ```mermaid
    graph TD
        A(单节课) --> B1(练习组件1);
        A --> B2(练习组件2);

        B1 --> T1_1(题目1);
        B1 --> T1_2(题目2);
        B1 --> T1_3(题目3);
        B1 --> T1_3_v1(题目3');
        B1 --> T1_3_v2(题目3'');

        B2 --> T2_1(题目4);
        B2 --> T2_1_v1(题目4');
        B2 --> T2_2(题目5);
        B2 --> T2_2_v1(题目5');
        B2 --> T2_2_v2(题目5'');
        B2 --> T2_2_v3(题目5''');
    ```

【============== 图片解析 END ==============】



- 每节课会配置1-N个练习组件，在配课环节插入到不同的课中组件之中。
- 每个练习中包含1-N个题目，题目有「单题」和「相似题组」两种类型，相似题组中至少有2道相似题目。
- 题目之间由教研控制相对顺序，决定了不同题组的推荐顺序
- 每个题目都有知识点内的难度等级标签，课中练习题目的难度较低，基本为L1，L2难度


# 三、策略方案

## 题目推荐策略

### 整体流程

![board_R7zlwS1uOhjgUAbT5MZcdQNan05](https://static.test.xiaoluxue.cn/demo41/prd_images/1746846295430.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

根据您提供的PRD文档中的图片及其OCR解析文本，我对该图片内容进行分析总结如下：

1.  **图片解析与核心价值**

    此图片是一个 **流程图**，出自“PRD - 课中练习推题策略 - V1.0”的需求文档。它清晰地描绘了在互联网教育场景下，课中练习时系统向学生推送题目的核心判断逻辑和执行路径。

    *   **关键元素与组成部分**：
        *   **起始点**：课中推题。
        *   **判断节点**：是否答对、是否有相似题、是否还有题目。
        *   **处理节点**：推出相似题。
        *   **结束点**：结束。

    *   **层级化结构与关联**：
        1.  流程以“课中推题”开始。
        2.  首要判断学生“是否答对”当前题目。
            *   若答对，则进入“是否还有题目”的判断。
            *   若答错，则进入“是否有相似题”的判断。
        3.  对于答错的情况，判断“是否有相似题”。
            *   若有相似题，则执行“推出相似题”，然后进入“是否还有题目”的判断。
            *   若无相似题，则直接进入“是否还有题目”的判断。
        4.  最后判断“是否还有题目”。
            *   若有，则流程循环，暗示推送下一道题目（返回到推题的逻辑起点或下一个题目的推送）。
            *   若无，则流程“结束”。

    *   **核心作用与价值**：
        该流程的核心作用是实现一种智能化的、根据学生答题情况动态调整的练习推送机制。其价值在于：
        *   **个性化学习**：通过判断答题正误，并针对性地推送相似题，帮助学生巩固薄弱知识点。
        *   **提升学习效率**：避免了盲目刷题，使得练习更具针对性。
        *   **教学闭环**：确保学生在掌握当前知识点或完成所有练习后才结束，形成完整的学习反馈闭环。

2.  **功能模块拆解**

    *   **题目推送模块 (课中推题)**：负责向学生展示当前的练习题目。
    *   **答题结果判断模块 (是否答对)**：负责校验学生提交的答案，并判断其正确性。
    *   **相似题目检索与推送模块 (是否有相似题 -> 推出相似题)**：当学生答错题目时，此模块负责查询是否存在与当前题目知识点或难度相近的题目，并将其推送给学生。
    *   **练习状态管理模块 (是否还有题目)**：负责追踪当前练习组或题库中是否还有未完成的题目。
    *   **练习结束模块 (结束)**：负责在所有题目完成或满足特定条件后，终止当前的练习流程。

3.  **服务端需提供的功能与数据内容**

    为支持此推题策略，服务端需要提供以下功能和数据内容：
    *   服务端需要能够根据当前的教学进度和学生状态，提供初始的练习题目内容。
    *   对于学生提交的答案，服务端需要能够接收并判断其正确性，或者提供正确答案及判题逻辑供客户端或前端判断后将结果同步给服务端。
    *   当一道题目被学生答错时，服务端需要能够根据该题目的属性（如知识点、难度等），查询并返回是否存在相似的题目。
    *   如果存在相似题目，服务端需要能够提供该相似题目的具体内容以供推送。
    *   服务端需要能够维护学生当前练习的整体状态，明确告知系统是否还有后续的题目可以推送。
    *   如果还有后续题目，服务端需要能够提供下一道待推送题目的内容。

4.  **Mermaid 流程图描述**

    ```mermaid
    flowchart TD
        A[课中推题] --> B{是否答对};
        B -- 答错 --> C{是否有相似题};
        C -- 是 --> D[推出相似题];
        D --> E{是否还有题目};
        C -- 否 --> E;
        B -- 答对 --> E;
        E -- 是 --> A;
        E -- 否 --> F[结束];
    ```

【============== 图片解析 END ==============】



### 推荐策略

1. 一个练习组件中的多个题目，需要按后台配置顺序依次作答。
1. 相似题题组中，可随机推出一题
1. 如果用户答对，则继续推出下一题。
1. 如果用户答错，有相似题则推出相似题，没有相似题则继续推出下一题。
1. 答错后的相似题只推出一次，无论用户是否回答正确都继续推出后续题目


## 未来可拓展

1. 区分错误的严重程度或错误类型
如果学生答得非常离谱（比如选了明显错误选项），推两道不同角度的相似题；如果是小错误（比如粗心），推一道即可。
1. 个性化推题
基于实时答题表现调整题目数量的空间，比如连续答对三题可以略过后面一部分简单题，加快节奏。

























