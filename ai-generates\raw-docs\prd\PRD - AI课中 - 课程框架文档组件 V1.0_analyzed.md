PRD - AI课中 - 课程框架+文档组件 V1.0

**版本管理**

| 版本号 | 日期 | 变更人 | 变更类型 | 变更详情 |
| --- | --- | --- | --- | --- |
| V1.0 | 2025-04-08 | 钱晋菲 | 新建 | 新建文档 |
|  |  |  |  |  |
|  |  |  |  |  |

**关联需求**

| 关联需求名称 | 所属 PM | 需求进度 | 文档链接 |
| --- | --- | --- | --- |
| DemoV1.0 |  | 已上线 |  |
| DemoV1.1 |  | 已上线 |  |
| 课程配置 |  | 开发中 |  |
| 掌握度 V1.0 |  | 开发中 |  |
| 课中-练习组件 |  | 待开发 |  |

**设计稿**

视觉稿：待补充



# 一、背景和目标

## 需求背景

在前期两个版本的 demo 进校调研过程中，我们收集了一系列待解决的关键问题，如：板书内容过多，高亮时机和老师讲解对不上，不能双击播放暂停，自由/跟随模式的切换交互不好理解等，这些问题对课程体验造成了负面影响。因此，我们计划在正式版本中优先解决这些问题，以确保课程基础体验（包括学习与练习环节）的质量。

此外，正式版本还将整体迭代课中体验，让上课过程更接近1v1真人直播课，提供沉浸式地课堂体验，展示系统的个性化和智能型。

## 项目收益

优化课中体验，提升学习效果及课程满意度

## 覆盖用户

使用新AI课的全量用户

## 方案简述

本方案围绕AI课中学习体验升级，在保留原有课中基本结构的基础上，进行了优化与能力扩展，具体包括：

- 课程框架升级：优化开场页与结算页设计。
- 文档组件增强：引入同步播放勾画轨迹、快捷交互（双击、长按、倍速调整、10秒快退快进）等功能，提升学生自主学习流畅度。
本版本聚焦于打磨课中体验的底层能力，为后续引入积分体系、小组战队、互动讲题、评论等丰富玩法奠定基础。

## 未来会做什么

1. 框架中加入积分体系和小组战队。
1. 支持互动讲题，自动化逐步讲解题目，并在讲解过程中加入互动问题
1. 支持在文档中长按评论
1. 增加费曼组件、互动组件


# 二、名词说明

暂无



# 三、业务流程

整体课程结构和demo阶段类似，V1.0只支持 「文档组件」、「练习组件」、「答疑组件」

一节课由不同的可配置组件构成。组件之间不同的组合方式构成了不同的课程类型。

答疑组件在课中不能单独配置，需要挂载在「文档组件」/「练习组件」下

| 单节课程结构 | 组件类型 | 说明 |
| --- | --- | --- |
| ![in_table_board_FdxgwtizahKubpbiJmFcvfeVnmb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844222616.png) | 文档组件 | 课程内容为JS文档形式，支持学生在上课过程中自由浏览，支持点击/长按进行提问 |
|  | 练习组件 | 支持各类题型（一期仅支持单选），可按策略推送题目 |
|  | 答疑组件 | 基于大模型能力和学生进行实时对话，解答学生问题 |

![in_table_board_FdxgwtizahKubpbiJmFcvfeVnmb]

###### 图片分析
【============== 图片解析 BEGIN ==============】

根据提供的图片及其OCR文本内容，这是一张用于描述“单节课内容”结构和组件构成的**结构示意图**或**教学流程与组件对应图**。它出自AI课程的PRD文档，旨在清晰地展示一节课从开始到结束的各个环节以及每个环节所依赖的功能组件。

1.  **图片类型、关键元素、层级结构及核心作用价值**

    *   **图片类型**：本图属于一种**结构示意图**，它清晰地展示了“单节课内容”的顺序流程以及在每个流程节点上所使用的“文档组件”、“练习组件”、“功能页面”或“答疑组件”。
    *   **关键元素**：
        *   **单节课内容（Overall Structure）**: 顶层概念，代表一节完整课程的全部构成。
        *   **课程阶段（Lesson Stages）**: 包含课程开场页、课程引入、知识点1、知识点2、练习1、知识点3、知识点4、练习2、课程总结、学习报告。这些阶段按顺序组织了学习流程。
        *   **功能组件/页面（Functional Components/Pages）**: 包括功能页面、文档组件、练习组件、答疑组件。这些是实现各个课程阶段具体功能的模块。
    *   **层级化结构与关联**：
        1.  **最高层**：单节课内容。
        2.  **第二层**：顺序排列的课程阶段。这是一个线性序列，代表用户学习的路径。
            *   课程开场页
            *   课程引入
            *   知识点1
            *   知识点2
            *   练习1
            *   知识点3
            *   知识点4
            *   练习2
            *   课程总结
            *   学习报告
        3.  **第三层**：与第二层各阶段相对应的功能组件或页面。
            *   课程开场页 -> 功能页面
            *   课程引入 -> 文档组件, 答疑组件
            *   知识点1 -> 文档组件, 答疑组件
            *   知识点2 -> 文档组件, 答疑组件
            *   练习1 -> 练习组件, 答疑组件
            *   知识点3 -> 文档组件, 答疑组件
            *   知识点4 -> 文档组件, 答疑组件
            *   练习2 -> 练习组件, 答疑组件
            *   课程总结 -> 文档组件, 答疑组件
            *   学习报告 -> 功能页面
    *   **核心作用与价值**：此图的核心作用在于**标准化课程结构**和**明确各阶段功能实现方式**。它为产品设计、内容制作和技术开发团队提供了统一的蓝图，确保了课程体验的一致性和各个功能模块的清晰界定。通过组件化的方式，可以提高开发效率和内容复用性，同时保证学习流程的完整性和逻辑性。答疑组件的普遍存在表明了对学习过程中互动和支持的重视。

2.  **各组成部分拆解与功能概述**

    *   **单节课内容**：
        *   功能概述：作为一节课的整体容器，定义了课程的完整学习路径和体验。
    *   **课程开场页** (使用 **功能页面**)：
        *   功能概述：课程开始的引导性页面，可能包含课程标题、目标、预告等信息。
    *   **课程引入** (使用 **文档组件** 和 **答疑组件**)：
        *   功能概述：通过文档形式介绍课程的背景、重要性或引导性内容，并提供答疑支持。
    *   **知识点 (1, 2, 3, 4)** (均使用 **文档组件** 和 **答疑组件**)：
        *   功能概述：通过文档形式呈现具体的教学内容、理论知识、案例分析等，并提供答疑支持。
    *   **练习 (1, 2)** (均使用 **练习组件** 和 **答疑组件**)：
        *   功能概述：提供互动性的练习题目或操作任务，用于巩固所学知识点或技能，并提供答疑支持。
    *   **课程总结** (使用 **文档组件** 和 **答疑组件**)：
        *   功能概述：通过文档形式对本节课的主要内容进行回顾、梳理和总结，并提供答疑支持。
    *   **学习报告** (使用 **功能页面**)：
        *   功能概述：课程结束后展示学员的学习情况、练习结果、成就等信息的反馈页面。
    *   **文档组件**：
        *   功能概述：用于承载和展示图文、音视频等多媒体静态或动态教学内容的通用模块。
    *   **练习组件**：
        *   功能概述：用于实现在线的、可交互的练习题、测试题或操作型任务的模块。
    *   **答疑组件**：
        *   功能概述：在学习过程中，为学生提供提问、查看解答或与助教/教师互动的支持模块，贯穿于多个教学环节。
    *   **功能页面**：
        *   功能概述：指具有特定完整功能的独立页面，如开场引导或结果报告，其内容和交互可能相对固定或特定。

3.  **服务端需提供的功能和返回的数据内容**

    服务端需要为客户端提供构建“单节课内容”所需的全部结构化信息和具体内容。这包括：
    *   需要返回一节课内各个阶段的有序列表，明确每个阶段的类型（如开场页、知识点、练习、总结、报告等）以及每个阶段所关联的组件类型（功能页面、文档组件、练习组件）。
    *   针对“功能页面”类型的阶段（如课程开场页、学习报告），服务端需要提供驱动该页面展示所需的数据。例如，对于“学习报告”，需要提供该用户的学习数据，如练习得分、完成度等。
    *   针对使用“文档组件”的阶段（如课程引入、各知识点、课程总结），服务端需要提供该文档组件应显示的具体内容数据，这可能包括文本、图片URL、音视频URL、排版信息等。
    *   针对使用“练习组件”的阶段（如练习1、练习2），服务端需要提供练习题目的数据，包括题干、选项（如有）、正确答案（用于客户端即时判断或服务端批改）、题目类型、解析等。同时，服务端需具备接收用户练习作答数据并进行处理（如批改、记录）的功能。
    *   针对“答疑组件”，服务端需要支持提问的提交、存储，以及历史问答内容的拉取。这可能涉及到将问题与特定的课程阶段或知识点进行关联的功能。服务端需要管理用户的提问和对应的解答信息。

4.  **Mermaid 流程图描述**

    ```mermaid
    flowchart TD
        A[单节课内容] --> B["课程开场页 (功能页面)"];
        B --> C["课程引入 (文档组件, 答疑组件)"];
        C --> D["知识点1 (文档组件, 答疑组件)"];
        D --> E["知识点2 (文档组件, 答疑组件)"];
        E --> F["练习1 (练习组件, 答疑组件)"];
        F --> G["知识点3 (文档组件, 答疑组件)"];
        G --> H["知识点4 (文档组件, 答疑组件)"];
        H --> I["练习2 (练习组件, 答疑组件)"];
        I --> J["课程总结 (文档组件, 答疑组件)"];
        J --> K["学习报告 (功能页面)"];
    ```

【============== 图片解析 END ==============】





# 四、需求概览

整体需求规划详见：[AI课中需求盘点](https://wcng60ba718p.feishu.cn/wiki/Htv0wNEAxis9dAkBjTTcjcWlnTb)

本次方案在demo V1.0 & V1.1的基础上，主要围绕课程框架、文档组件、练习组件进行优化升级，旨在提升用户体验和学习效果。

| 模块 | 需求描述 | 优先级 |
| --- | --- | --- |
| 课程框架 |  | P0 |
|  |  |  |
|  |  |  |
| 文档组件 |  | P0 |
|  |  | P0 |

1. 课程开场页、结算页优化
1. 课程进度，支持用户在一节课的组件中自由切换进度
1. 退出/继续课程
1. 内容播放：
1. 增加勾画轨迹，和JS内容同步播放
1. 未播放的区域呈现模糊/弱化效果，让学生的注意力聚焦在当前内容块上
1. 默认展示字幕
1. 交互操作优化：
1. 增加双击、长按等快捷交互
1. 增加1.75和3倍速
1. 增加前进 / 后退 10s


# 五、详细产品方案

## 课程框架

相比Demo V1.0的改动点：

1. 去掉每个组件之间的串场动效
1. 结算页：
1. 增加“掌握度”、“答题情况”、“推荐学习”相关信息
1. 鼓励文案展示规则调整
1. 课程进度：保存用户答题记录，用户切换到已做过的练习模块时直接展示答题记录
| 模块/功能 | 需求描述 | 原型图 |
| --- | --- | --- |
| 开场页 | 注：不同学科有不同的开场页，页面框架不变，主要更换背景图和色值 | ![in_table_image_WVtsbsQ17oojarxxvYQcPWr0nqc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844224709.png) |
| 结算页 | 完成一节课全部内容的学习后，进入结算页。 | ![in_table_image_Wb2pba8ILoTQ5dxwpbtcrWt2npb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844225940.png) |
|  |  | ![in_table_image_P5GCbSKSfoYqb4x3uBscWcKfn9d](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844226411.png) |
|  |  | ![in_table_image_AvqMbn4onoSkCFxclcScqQCpnwg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844226812.png) |
|  |  | ![in_table_image_NdMMbWsS7owwOwxxhlUcTX5rnBc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844228176.png) |
|  |  | ![in_table_image_LdiGb9jhjob9OTxCdpIc7YrTnbd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844228587.png) |
|  |  | ![in_table_image_IqW3bjKbTogLnVxrMvucpnGMnWf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844229009.png) |
|  |  |  |
| 课程进度 | 用户可以在任何组件中，灵活调整学习进度。 |  |
| 退出/继续课程 |  |  |

开场页的作用：建立仪式感：进入课程直接看到开场页，会让学习者有“正式开始”的感觉，这种心理上的“仪式感”能提高学习的专注度

- 页面展示：
- 第X章：后台配置
- 课程序号：后台配置
- 课程名称：后台配置，取对应业务树末级知识点名称（25个字以内）
- 交互：进入开场页后自动播放IP动效+音频，播放完成后，自动进入 AI 课第一小节
![in_table_image_WVtsbsQ17oojarxxvYQcPWr0nqc]

###### 图片分析
【============== 图片解析 BEGIN ==============】

根据提供的图片及其OCR文本，这是一张**UI界面截图或设计稿**，展示了AI课程中课程框架文档组件的一个具体表现形式。

1.  **关键元素、组成部分及关联性分析**

    该图片展示了一个课程内容节点的UI呈现方式。从层级化结构来看：

    *   **顶层元素：章节信息（第一章）**
        *   作用：标示当前内容所属的主章节，帮助用户在课程的宏观结构中定位。
        *   价值：提供课程的整体脉络感，方便用户理解学习进度和内容所属范畴。
    *   **中层元素：小节编号（2.1.1）与小节标题（复数的概念）**
        *   **小节编号（2.1.1）**：
            *   作用：精确标示当前学习单元在章节内的具体位置，提供详细的层级索引。
            *   价值：支持精细化的课程导航和内容管理，便于学生快速跳转和老师组织教学。
        *   **小节标题（复数的概念）**：
            *   作用：点明当前学习单元的核心主题或知识点。
            *   价值：让用户一眼就能了解该单元的学习目标，提高学习效率。
    *   **底层元素：描述性文案占位（两行文案展示效果）**
        *   作用：预留了用于展示补充说明、简介或学习引导的文本区域，此处提示为两行文本的视觉效果。
        *   价值：可以为小节内容提供更丰富的上下文信息，如学习目标、前置知识、内容摘要等，辅助学生更好地理解和学习。

    **元素间关联：**
    “第一章”是“2.1.1 复数的概念”的父级或容器。“2.1.1”是“复数的概念”这一具体内容的唯一标识符和层级路径。而“两行文案展示效果”则是对“复数的概念”这一小节的补充描述区域。整体上，这些元素共同构成了一个结构清晰、信息明确的课程内容展示单元。

2.  **功能模块拆解及概述**

    *   **章节信息展示模块**：
        *   功能概述：用于显示当前内容所属的章节编号或名称。
    *   **小节编号展示模块**：
        *   功能概述：用于显示当前内容单元（如课时、知识点）在章节内的具体编号。
    *   **小节标题展示模块**：
        *   功能概述：用于显示当前内容单元的主题名称。
    *   **小节描述信息展示模块**：
        *   功能概述：用于显示对当前内容单元的补充说明或简介，并指明期望的文本行数限制或展示效果。

3.  **服务端需提供的功能和数据内容**

    服务端需要提供以下数据内容，以支持该课程框架组件的展示：
    *   课程的章节信息，例如章节的序号或名称。
    *   章节下各个小节（或课时、知识点）的唯一标识符或编号。
    *   每个小节的标题文本。
    *   每个小节的描述性文本内容，该文本内容应能适配前端提示的展示行数。

4.  **Mermaid 图表描述**

    该图片为UI界面截图，不属于流程图、时序图、类图、ER图、甘特图或饼图等适合使用Mermaid语法直接描述的图表类型。因此，不适用Mermaid进行描述。

【============== 图片解析 END ==============】



结算页的作用：

1. 恭喜完成课程（情绪价值）
1. 展示本次学习表现
1. 推荐后续学习路径
- 庆祝动效及文案：
1. 动效：
1. 课程包含多个IP角色，结算页随机选择某个IP
1. 每个IP有“开心”、“鼓励”两类动效，展示策略如下
![board_QBEwwqJVOh02Q9bwVXwcSqvmncd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844223173.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

好的，我们来一起分析这张关于“动效展示策略”的图片。

1.  **图片解析：**
    *   **图片类型：** 这是一张 **流程图**。
    *   **核心主题：** 该流程图出自AI课程的PRD文档，用于阐释“动效展示策略”，核心是根据学员的“正确率”来决定是否提升其“掌握度”以及展示何种“动效”。
    *   **关键元素与组成部分：**
        *   **触发条件/判断点：** “正确率 > 70%” 是核心的判断条件。
        *   **分支路径：**
            *   **路径一 (正确率 > 70%)：** 导致两个结果：“掌握度提升”和展示“开心动效”。
            *   **路径二 (其他情况，即正确率 <= 70%)：** 导致两个结果：“掌握度不变”和展示“鼓励动效”。
        *   **核心规则（批注内容）：** “外化掌握度不会出现下降的情况, 当实际的知识点掌握下降时。外化掌握度不变。” 这条规则强调了用户感知的掌握度（外化掌握度）的非递减性，即便内部评估的实际掌握度可能下降，对外展示的掌握度也不会降低，而是保持不变。
    *   **元素间关联：**
        *   整个流程始于对学员学习行为结果（正确率）的评估。
        *   评估结果通过条件判断（是否大于70%）分流到不同的处理路径。
        *   每条路径都包含对“掌握度”状态的调整（提升或不变）和相应的“动效”反馈（开心或鼓励）。
        *   “批注”中的规则是对“掌握度提升”和“掌握度不变”这两个环节的全局约束，确保用户体验的积极性。
    *   **核心作用与价值：**
        *   **作用：** 定义了在AI课程中，系统如何根据学生的答题正确率，动态调整其可见的知识点掌握程度，并配合不同的动画效果给予即时反馈。
        *   **价值：** 旨在通过正向激励（开心动效、掌握度提升）和安抚鼓励（鼓励动效、掌握度不变），提升学生的学习积极性和参与感，同时提供一个清晰、稳定的掌握度认知，避免因掌握度下降可能带来的挫败感。

2.  **功能模块拆解：**
    *   **正确率评估模块：**
        *   **功能概述：** 评估用户答题或交互的正确率，并将其与预设阈值（70%）进行比较。
    *   **掌握度提升处理模块：**
        *   **功能概述：** 当正确率大于70%时，负责提升用户的外化掌握度。
    *   **开心动效展示模块：**
        *   **功能概述：** 当正确率大于70%时，触发并展示开心类型的动画效果。
    *   **掌握度维持处理模块：**
        *   **功能概述：** 当正确率不大于70%（即其他情况）或根据批注规则（实际掌握度下降）时，负责维持用户当前的外化掌握度不变。
    *   **鼓励动效展示模块：**
        *   **功能概述：** 当正确率不大于70%（即其他情况）时，触发并展示鼓励类型的动画效果。

3.  **服务端需提供的功能与数据内容：**
    服务端需要根据接收到的用户学习行为数据（例如答题结果），计算出相应的正确率。基于此正确率，服务端需判断是否满足掌握度提升的条件。
    服务端需要返回的数据应包括：
    *   关于掌握度状态变更的信息，明确指示掌握度是提升还是保持不变。
    *   关于应展示何种动效类型的指示信息，指明是展示开心动效还是鼓励动效。
    *   最终的外化掌握度信息，无论其是提升后的新状态还是保持不变的当前状态。
    服务端在处理掌握度时，必须遵循外化掌握度不会下降的规则，即使用户的实际内部掌握度评估结果为下降，对外反馈的掌握度也应保持不变。

4.  **Mermaid 流程图描述：**

    ```mermaid
    graph TD
        A(开始评估动效展示策略) --> B{正确率 > 70% ?};
        B -- "是" --> C[掌握度提升];
        C --> E[展示开心动效];
        B -- "否 (其他情况)" --> D[掌握度不变];
        D --> F[展示鼓励动效];
        E --> G(策略执行完毕);
        F --> G;

        subgraph "重要规则"
            direction LR
            R1["注：外化掌握度不会出现下降的情况,</br>当实际的知识点掌握下降时,</br>外化掌握度不变。"]
        end
        classDef rule fill:#f9f,stroke:#333,stroke-width:1px,color:#333;
        class R1 rule;
    ```

【============== 图片解析 END ==============】



1. 文案：
1. 文案包括「主文案」「副文案」两部分内容
1. 文案内容和IP相关，每个IP维护一套反馈文案
1. 根据用户的学习表现，展示不同的反馈文案。
1. 如果同时命中两种情况，则随机选择其中的一种情况
1. 一种情况下有多条文案时，随机选择一条展示
![board_QJQEwg4J2hl7JfbidfIceBcxnQc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844223676.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

该图片为一张**分类结构图（或可视为简化的思维导图）**，出自PRD文档中关于AI课程反馈文案的设计部分。

1.  **图片关键元素、组成部分及层级化结构阐述：**

    *   **核心主题：** 反馈文案 (Feedback Copy/Text)
        *   **作用与价值：** 这是整个图片的核心，代表了AI课程中根据学员不同表现和状态，系统需要给出的各类文本反馈。其核心价值在于通过个性化、情境化的文案，提升用户体验、激励用户学习、明确指出学习状况。
    *   **一级分类（触发条件大类）：**
        *   **掌握度相关：** 根据学员对知识点掌握程度的变化或状态触发的反馈。
            *   **作用与价值：** 鼓励学员在知识掌握上取得进步，或庆祝其达成学习目标，强化正面学习行为。
        *   **正确率相关：** 根据学员答题、练习的正确率水平触发的反馈。
            *   **作用与价值：** 针对学员的答题表现提供即时反馈，无论是完美表现、优秀成绩还是有待提高，都能给予相应的情感支持或指导。
        *   **时长相关：** 根据学员学习时长与预期时长的对比情况触发的反馈。
            *   **作用与价值：** 关注学员的学习投入，对于超时学习的学员可能给予鼓励或提示。
        *   **其他情况：** 涵盖上述分类未能包含的特定学习情景。
            *   **作用与价值：** 作为补充类别，确保在一些通用或特殊学习节点（如完成学习）也有相应的反馈。
    *   **二级分类（具体触发情景/标签）：**
        *   隶属于“掌握度相关”：
            *   掌握度提升
            *   达成目标掌握度
        *   隶属于“正确率相关”：
            *   正确率100% (并关联标签如：完美通关, 无可挑剔)
            *   成绩优秀 (并关联标签如：你还会更强)
            *   正确率0% (并关联标签如：不可思议)
        *   隶属于“时长相关”：
            *   学习时长超过预计时长50%
        *   隶属于“其他情况”：
            *   完成学习
            *   **作用与价值：** 进一步细化了反馈文案的适用场景，使得反馈更加精准。标签（如“完美通关”）则可能是前端展示时的一个强调性提示或分类。
    *   **三级分类/要素（文案特性/内容示例）：**
        *   **IP (用户画像/人设)：** 学霸 (Top Student)、学渣 (Struggling Student)
            *   **作用与价值：** 表明反馈文案可能需要根据预设的学员画像进行差异化设计，以增强代入感和亲切度。
        *   **具体文案示例：** 如“进步神速。这么下去要超过我了”、“不可思议。连一个错误都没有”等。
            *   **作用与价值：** 提供了实际的文案内容，直观展示了不同情景和IP下的沟通风格和内容导向。

    **元素间关联：** 整体呈现为一个从“反馈文案”核心主题出发，逐级向下细分的树状层级结构。一级分类定义了反馈触发的大方向，二级分类细化了具体情景，三级则给出了文案的风格倾向（IP）及内容实例。

2.  **图片各组成部分拆解及功能模块概述：**

    *   **反馈文案系统：**
        *   **功能概述：** 根据用户在AI课程中的学习行为、表现数据和预设条件，动态匹配并展示相应的激励性、指导性或总结性文本信息。
    *   **掌握度反馈模块：**
        *   **功能概述：** 处理与用户知识点掌握程度相关的反馈。
        *   **子模块：掌握度提升反馈：** 当用户知识点掌握度较之前有所提升时，展示鼓励性文案。
        *   **子模块：达成目标掌握度反馈：** 当用户知识点掌握度达到预设目标时，展示祝贺性文案。
    *   **正确率反馈模块：**
        *   **功能概述：** 处理与用户答题或练习正确率相关的反馈。
        *   **子模块：正确率100%反馈：** 当用户取得满分或全部正确时，展示高度赞扬的文案（可能带有“完美通关”、“无可挑剔”等标签）。
        *   **子模块：成绩优秀反馈：** 当用户取得高正确率（但非100%）时，展示肯定并鼓励的文案（可能带有“你还会更强”等标签）。
        *   **子模块：正确率0%反馈：** 当用户正确率为零时，展示特定情景的文案（可能带有“不可思议”等标签，暗示幽默或引导反思）。
    *   **时长反馈模块：**
        *   **功能概述：** 处理与用户学习时长相关的反馈。
        *   **子模块：学习时长超预期反馈：** 当用户学习时长显著超过系统预计时长（如图中为超过50%）时，展示相应文案。
    *   **其他场景反馈模块：**
        *   **功能概述：** 处理未被特定模块覆盖的其他学习场景。
        *   **子模块：完成学习反馈：** 当用户完成某个学习单元或任务时，展示总结性或鼓励性文案。
    *   **用户IP/画像适配功能：**
        *   **功能概述：** 在上述各反馈模块中，能够根据用户的“学霸”或“学渣”等画像标签，选择和展示更具针对性的文案。

3.  **服务端需提供的功能和返回的数据内容描述：**

    服务端需要能够根据客户端上传的用户学习行为数据（如掌握度变化情况、具体掌握度数值、答题正确率、学习时长、学习任务完成状态）以及用户的IP画像标签，匹配并返回相应的反馈文案。
    具体来说，服务端需要提供：
    *   针对“掌握度提升”场景的反馈文案，可能需要根据用户IP画像区分不同版本的文案。
    *   针对“达成目标掌握度”场景的反馈文案，可能需要根据用户IP画像区分不同版本的文案。
    *   针对“正确率100%”场景的反馈文案，需包含文案本身以及可能的关联标签（如“完美通关”、“无可挑剔”），并可能根据用户IP画像区分不同版本的文案。
    *   针对“成绩优秀”场景的反馈文案，需包含文案本身以及可能的关联标签（如“你还会更强”），并可能根据用户IP画像区分不同版本的文案。
    *   针对“正确率0%”场景的反馈文案，需包含文案本身以及可能的关联标签（如“不可思议”），并可能根据用户IP画像区分不同版本的文案。
    *   针对“学习时长超过预计时长特定百分比”（如图中为50%）场景的反馈文案，可能需要根据用户IP画像区分不同版本的文案。
    *   针对“完成学习”场景的反馈文案。
    *   服务端需要存储一个反馈文案库，每条文案关联其触发条件（如掌握度提升、正确率区间、时长条件、完成状态等）和适用的用户IP画像。当接收到用户行为数据后，服务端逻辑会根据这些数据匹配到最合适的文案并返回给客户端。返回内容主要为文本字符串，以及可选的关联标签文本。

4.  **Mermaid 图描述：**

    此图最接近于一个思维导图或分类结构，可以使用 Mermaid 的 `graph TD` (自顶向下流程图/结构图) 来表示其层级关系：

    ```mermaid
    graph TD
        Root[反馈文案] --> C1[掌握度相关]
        Root --> C2[正确率相关]
        Root --> C3[时长相关]
        Root --> C4[其他情况]

        C1 --> S1_1[掌握度提升]
        S1_1 --> T1_1_1["文案 (IP:学霸): 进步神速..."]
        S1_1 --> T1_1_2["文案 (IP:学渣): 直接把..."]

        C1 --> S1_2[达成目标掌握度]
        S1_2 --> T1_2_1["文案 (IP:学霸): 这达成速度..."]
        S1_2 --> T1_2_2["文案 (IP:学渣): 哇。教科书级..."]
        S1_2 --> T1_2_3["文案: 女娲看了都直呼..."]

        C2 --> S2_1[正确率100%]
        S2_1 --- Tag2_1_1[标签: 完美通关]
        S2_1 --- Tag2_1_2[标签: 无可挑剔]
        Tag2_1_1 --> T2_1_1["文案 (IP:学霸): 不可思议..."]
        Tag2_1_1 --> T2_1_2["文案 (IP:学渣): 哇塞。你简直是..."]
        Tag2_1_1 --> T2_1_3["文案: 满分操作..."]
        Tag2_1_2 --> T2_1_4["文案 (IP:学霸): 你是怎么做到这么完美的"]

        C2 --> S2_2[成绩优秀]
        S2_2 --- Tag2_2_1[标签: 你还会更强]
        S2_2 --> T2_2_1["文案 (IP:学霸): 差一点就全对了..."]
        Tag2_2_1 --> T2_2_2["文案 (IP:学渣): 今天的你..."]

        C2 --> S2_3[正确率0%]
        S2_3 --- Tag2_3_1[标签: 不可思议]
        Tag2_3_1 --> T2_3_1["文案 (IP:学霸): 完美避开了所有正确答案"]

        C3 --> S3_1[学习时长超过预计时长50%]
        S3_1 --> T3_1_1["文案 (IP:学渣): 水滴石穿才是高手之路"]

        C4 --> S4_1[完成学习]
        S4_1 --> T4_1_1["(图片中此分支下未直接展示具体文案示例)"]

    ```

【============== 图片解析 END ==============】



![in_table_image_B1KqbxabioPL0kxg4BTcuAb0nWg]

###### 图片分析
【============== 图片解析 BEGIN ==============】

基于提供的图片及其OCR解析文本，这是一个App端的课程学习报告或学习总结界面。

1.  **图片类型、关键元素、组成部分及核心作用与价值**

    *   **图片类型**：UI界面设计图（或截图）。
    *   **出自需求文档**：该界面为AI课程产品中，用户完成一节课或一次学习任务后的总结反馈界面。

    *   **关键元素及组成部分（层级化阐述）**：
        1.  **顶部状态栏信息**：显示设备时间、网络状态、电量（根据OCR，这些信息非核心业务，但存在于截图）。
        2.  **核心学习成果数据展示区**：
            *   学习用时
            *   答题数
            *   正确率
            *   知识点掌握度（及本次学习提升数值）
        3.  **学习详情引导区**：
            *   答题情况入口（"查看详情 >"）
        4.  **推荐学习/练习区**：
            *   推荐类型标签（如："精选"、"典型"）
            *   具体推荐内容/题目指示（数字可能代表题目编号或推荐项）
            *   行动按钮（"去练习"）
            *   内容展开/收起控件（"展开"）
            *   专项练习推荐（"巩固练习"，包含预计题量和用时）
        5.  **用户反馈区**：
            *   问题（"本节课感受如何?"）
            *   反馈选项（"极差"、"较差"、"一般"、"满意"、"很棒"）
        6.  **底部导航/操作区**：
            *   返回课程按钮（"看课程"）
            *   完成/关闭按钮（"完成"）

    *   **元素间关联**：
        *   核心学习成果数据是对用户本次学习行为的量化总结。
        *   知识点掌握度及其提升值，是基于答题情况等数据综合评估的结果，更侧重能力变化。
        *   答题情况入口为用户提供了深入分析自己答题表现的路径，与正确率、答题数等数据关联。
        *   推荐学习/练习区是基于用户的学习成果（尤其是知识点掌握情况和答题表现）进行的个性化内容推荐，旨在巩固薄弱环节或拓展学习。
        *   用户反馈区收集用户对课程内容或体验的主观评价。
        *   底部导航区则提供了用户完成当前总结后的下一步操作路径。

    *   **核心作用与价值**：
        *   **对用户**：该界面清晰地向用户反馈了其学习效果、时长投入、知识点掌握程度及变化，帮助用户了解自己的学习状况。通过个性化推荐，引导用户进行针对性巩固练习，提升学习效率。同时，提供反馈渠道，让用户参与到课程优化中。
        *   **对产品/平台**：收集用户的学习数据（时长、答题、正确率等）和反馈数据，为优化课程内容、迭代AI推荐算法、提升用户学习体验提供数据支持。通过引导用户进行更多练习，提升用户粘性和学习深度。

2.  **各组成部分功能模块及简要功能概述**

    *   **学习数据统计模块**：
        *   功能概述：展示用户在本次学习中的核心数据，包括学习所用时间、完成的答题数量、答题的正确率。
    *   **知识点掌握度评估模块**：
        *   功能概述：展示用户对相关知识点的掌握程度，并显示本次学习带来的掌握度提升数值。
    *   **答题详情查看模块**：
        *   功能概述：提供入口，允许用户查看本次学习中所有答题的具体情况和分析。
    *   **个性化推荐学习模块**：
        *   功能概述：根据用户的学习表现，推荐相关的学习内容或练习题，如“精选”、“典型”题目，并提供“去练习”的直接入口。包含内容展开/折叠功能。
    *   **巩固练习模块**：
        *   功能概述：提供专项的巩固练习建议，并预估练习所需的题量和时间。
    *   **课程反馈收集模块**：
        *   功能概述：收集用户对本节课的主观感受评价，设有多个评价等级供用户选择。
    *   **导航操作模块**：
        *   功能概述：提供“看课程”返回课程内容和“完成”结束当前学习报告页面的操作。

3.  **服务端需提供的功能和返回的数据内容**

    服务端需要提供本次学习会话结束后的各项统计数据和推荐内容。具体包括：
    *   用户的学习时长信息。
    *   用户完成的答题总数量。
    *   用户的答题正确率。
    *   用户当前学习内容相关的知识点掌握度评估结果，以及本次学习活动后知识点掌握度的提升数值。
    *   用于“查看详情”的答题情况数据，至少包括每道题目的作答状态和正确性，以及可能的解析链接或数据。
    *   一个推荐学习内容列表，其中每个推荐项可能包含类型标签（如精选、典型），具体推荐内容的标识（如题目ID或内容ID），以及练习的跳转指令。
    *   一个巩固练习的信息，包括推荐的练习题数量估计和预计完成时间。
    *   能够接收并记录用户提交的课程感受反馈数据的接口。
    *   用于“看课程”导航的课程信息或链接。

4.  **Mermaid 图描述**

    该图片为UI界面设计图，不属于流程图、时序图、类图、ER图、甘特图或饼图的范畴，因此不适用Mermaid语法进行专项描述。

5.  **图片中不存在内容禁止进行总结**

    已严格遵守，所有总结均基于图片显示内容和OCR文本。

6.  **遵循原则**

    分析全程已尽量遵循第一性原理、KISS原则和MECE原则，基于图片内容和教育产品经验进行分析。

【============== 图片解析 END ==============】



![in_table_image_Wb2pba8ILoTQ5dxwpbtcrWt2npb]

###### 图片分析
【============== 图片解析 BEGIN ==============】

根据提供的图片及其OCR文本内容，分析如下：

1.  **图片类型与核心价值解析**

    此图片应被视为 **UI组件示意图**。它出自“PRD - AI课中 - 课程框架文档组件 V1.0”，展示了一个用于信息反馈或结果呈现的界面元素。

    *   **关键元素与组成部分**：
        *   **容器元素 (Implied)**: 最外层，承载所有内部视觉元素。
            *   **动效 (Animation/Effect)**: 顶层视觉元素，用于展示动态效果。
            *   **主文案 (Main Copy)**: 核心文本信息展示区域。
            *   **副文案 (Sub-Copy)**: 辅助文本信息展示区域。

    *   **元素间关联**:
        此UI组件是一个集成的视觉单元。
        *   "动效"通常位于组件的显著位置，用以吸引用户注意或表达特定状态（如成功、警告等）。
        *   "主文案"紧随其后或与之配合，传递最核心的信息，如OCR文本中示例的“成绩优秀!”。
        *   "副文案"是对主文案的补充说明或提供额外上下文，如OCR文本中示例的“不错啊,”。
        三者共同构成一个完整的信息传达模块。

    *   **核心作用与价值**:
        在“AI课中 - 课程框架”的上下文中，此组件的核心作用是向用户提供即时、清晰的反馈。例如，在完成一个学习任务、测验或互动后，该组件可以用来展示用户的表现（如“成绩优秀!”，“不错啊,”），从而增强用户体验、提供激励或引导。其价值在于提升课程的互动性和用户参与感，并及时传达关键信息。

2.  **功能模块拆解**

    *   **动效模块**:
        *   **功能概述**: 展示预设的动态视觉效果，用于增强界面的生动性、表达情感色彩或指示状态。
    *   **主文案模块**:
        *   **功能概述**: 显示主要的、总结性的文本信息。这是用户第一眼会关注的核心内容。
    *   **副文案模块**:
        *   **功能概述**: 显示次要的、补充性的文本信息，用以解释、扩展主文案内容或提供额外细节。

3.  **服务端数据需求**

    为实现此UI组件的功能，服务端需要提供以下数据内容：
    *   服务端需要提供用于主文案模块展示的具体文本内容。
    *   服务端需要提供用于副文案模块展示的具体文本内容。
    *   如果动效的类型或内容是根据不同场景动态变化的，服务端可能还需要提供指定动效类型的标识或动效资源信息。但仅从图片和OCR来看，此点不明确，若动效固定或由前端根据主副文案类型判断则无需服务端提供。为确保完整性，仅基于图片文字信息，若服务端需要控制动效内容，则需要返回相关动效的标识信息。

4.  **图表类型特定描述**

    此图片是一个UI组件的静态示意图，不属于流程图、时序图、类图、ER图、甘特图或饼图的范畴。因此，不适用Mermaid语法进行描述。

【============== 图片解析 END ==============】



- 学习表现（同demoV1.0）：
1. 学习用时：用户完成本节课学习的累计学习时长。
1. 答题数：用户在这节课本次学习中的累计答题数量
1. 正确率：答对的题目数量 / 答题数量
注：已学完的课程又重新学习，答题数量、正确率、时长均不累计。

![in_table_image_P5GCbSKSfoYqb4x3uBscWcKfn9d]

###### 图片分析
【============== 图片解析 BEGIN ==============】

根据提供的图片及其OCR文本内容，现进行解析总结如下：

1.  **图片类型、关键元素、组成部分、层级结构及核心作用与价值**

    *   **图片类型**：该图片属于一个**数据展示卡片**或**信息摘要组件**。
    *   **关键元素**：图片主要包含三个关键数据指标及其对应的数值：
        *   学习用时：45分钟
        *   答题数：27题
        *   正确率：55%
    *   **组成部分**：由三个独立的指标展示区域构成，每个区域包含一个标签（如“学习用时”）和一个具体值（如“45分钟”）。
    *   **层级结构**：此组件内部结构扁平，三个指标并列展示，没有明显的层级依赖关系。它们共同构成一个信息单元。
    *   **核心作用与价值**：作为AI课程框架文档中的组件，其核心作用是向用户（如学生、教师或管理员）直观、快速地展示与学习行为相关的核心量化数据。其价值在于：
        *   对于学生：提供即时的学习反馈，帮助了解自己的学习时长、练习量和掌握程度。
        *   对于课程/产品：作为衡量用户参与度和学习效果的一个基础数据参考点，可用于数据分析、个性化推荐或学习路径调整的依据。

2.  **各组成部分拆解与功能模块概述**

    *   **学习用时模块**：
        *   **功能概述**：显示用户在特定学习单元或整个课程中所花费的总时间。
    *   **答题数模块**：
        *   **功能概述**：显示用户在学习过程中已完成作答的题目总数量。
    *   **正确率模块**：
        *   **功能概述**：显示用户已答题目中，回答正确的题目所占的百分比。

3.  **服务端需提供的功能和返回的数据内容描述**

    服务端需要提供以下数据内容，以支持该组件的展示：
    *   需要提供用户累计的学习用时数据。
    *   需要提供用户累计完成的答题数量数据。
    *   需要提供用户答题的正确率数据。
    这些数据应代表用户在特定上下文（如某个课程、章节或时间段内）的统计结果。

4.  **Mermaid 图描述**

    该图片为静态数据显示，不属于流程图、时序图、类图、ER图、甘特图或饼图的范畴。其核心是展示几个独立的指标数据，不涉及过程流转、时间序列、对象关系或任务调度等。因此，不适用Mermaid语法进行描述。

【============== 图片解析 END ==============】



- 外化掌握度：
计算方式详见：[PRD - 掌握度策略 - V1.0](https://wcng60ba718p.feishu.cn/wiki/J3d0wrEaDiWRy6kj2uVcQKLfnmh)

1. 掌握度说明：在掌握度标题旁展示对掌握度的说话信息，用户点击 i ，展示说明信息。文案如下：
掌握度反映了你对本节课的掌握情况，和你的答题正确率、题目难度相关。

掌握度完成进度和你设定的学习目标相关，修改目标后进度会重新计算哦。

1. 掌握度完成进度：用户当前掌握度 / 目标掌握度，保留小数点后两位，[0 , 1]
1. 掌握度变化：本次学习后的进度 - 本次学习前的进度。
注：

1. 初始掌握度不用于进度计算，用户首次学习的掌握度变化 = 本次学习后的进度 - 0
1. 如果用户本次学习后掌握度下降，此处展示的掌握度不变化
![in_table_image_AvqMbn4onoSkCFxclcScqQCpnwg]

###### 图片分析
【============== 图片解析 BEGIN ==============】

1.  **图片类型与核心价值解析**
    *   **图片类型**: 信息展示UI组件截图。
    *   **解析**: 此图片来源于“AI课中 - 课程框架文档组件 V1.0”的需求文档，展示了一个用于呈现用户“知识点掌握度”和“本次学习提升”信息的UI组件。
    *   **关键元素与组成**:
        *   **主要标题/标签**: "知识点掌握度"
        *   **核心数据展示**: 一个表示掌握程度的百分比数值（图片中显示为 "35%"）。
        *   **辅助信息/标签**: "本次学习提升"
        *   **辅助数据**: 根据OCR文本，与“本次学习提升”相关的数值是 "359"。
    *   **层级化结构与关联**:
        *   该组件是一个独立的信息展示单元。
        *   "知识点掌握度"是顶层信息，其具体数值（如"35%"）是核心。
        *   "本次学习提升"是与"知识点掌握度"相关的补充信息，展示了最近一次学习行为带来的掌握度变化量。
    *   **核心作用与价值**: 作为“AI课中 - 课程框架文档组件 V1.0”的一部分，此组件的核心作用是在用户学习过程中，实时、直观地反馈其对知识点的掌握程度以及最近一次学习带来的进步。
        *   **对用户的价值**: 提供即时学习效果反馈，增强学习成就感和动力，帮助用户了解自身学习状况。
        *   **对系统的价值**: 作为一个关键的数据展示节点，为AI课程系统评估学习效果、进行个性化内容推荐或调整教学策略提供数据支持。

2.  **功能模块拆解**
    *   **知识点掌握度显示模块**:
        *   **功能概述**: 展示学员当前对于相关知识点的整体掌握程度的百分比。
    *   **本次学习提升显示模块**:
        *   **功能概述**: 展示学员在本次学习过程中，其知识点掌握度所提升的数值。

3.  **服务端数据需求描述**
    服务端需要提供以下数据内容：
    *   学员当前的知识点掌握度具体百分比数值。
    *   学员本次学习所带来的知识点掌握度提升的具体数值。

4.  **Mermaid 图表描述**
    此图片为UI信息展示组件截图，不适用Mermaid图表进行描述。

【============== 图片解析 END ==============】



- 答题情况：展示用户本次学习的答题正误情况
1. 正确和错误展示不同的样式
1. 交互 ：
1. 如果题目数量超过14个，页面可上划展示剩余题目
1. 点击查看详情，进入题目列表页面
1. 点击某个题目，直接进入题目详情页


- 题目列表
1. 展示题号、题目类型、题干、作答结果。
1. 题干：默认最多展示2行，超过部分展示…
1. 作答结果：包括回答正确、回答错误、部分正确，三种情况
1. 交互
1. 点击仅看错题，列表中只展示回答错误和部分正确的题目
1. 点击查看解析，进入对应题目的详情页


- 题目详情：点击题目对应的查看解析，展示题目详情
1. 用时：用户完成这道题目的用时
1. 序号：当前题目序号 / 列表中的全部题目数量
1. 加入错题本
1. 如果题目没有加入错题本，按钮展示「加入错题本」
1. 如果题目已加入错题本，按钮展示「已加入错题本」不可点击
1. 上一题：点击后展示上一题的详情。第一题不展示上一题按钮
1. 下一题：点击后展示下一题的详情。最后一题不展示下一题按钮
![in_table_image_L9SkbV6sko5FVbxYcfDcAC1qnpg]

###### 图片分析
【============== 图片解析 BEGIN ==============】

基于提供的图片及OCR文本，这是一个关于“AI课中 - 课程框架文档组件 V1.0”中“答题情况”模块的UI界面截图。

1.  **图片类型、关键元素、层级结构、核心作用与价值解析**

    *   **图片类型**: UI界面组件示意图。
    *   **关键元素**:
        *   组件标题：“答题情况”
        *   导航链接：“查看详情 >”
        *   题目状态指示列表：包含数字（1, 2, 3, 5, 6, 8, 10, 11, 12, 13, 14）和特殊字符“牛”。
    *   **层级化结构**:
        *   **顶层**: “答题情况”组件整体。
            *   **上部**: 标题区域，包含左侧的“答题情况”文字和右侧的“查看详情 >”导航链接。
            *   **下部**: 内容区域，展示一个由多个方格组成的题目状态列表，每个方格内显示一个题目序号或特殊状态标识（如“牛”）。
    *   **核心作用与价值**:
        该组件的核心作用是向用户（学员）直观地展示其在一系列练习题或测验中的作答概况。通过数字标识题目，并通过特殊字符（如“牛”，通常表示“正确”、“优秀”或“已完成且表现良好”）高亮特定题目的状态。这有助于学员快速了解自己的进度和表现亮点。“查看详情”功能则提供了深入了解具体答题分析的入口。在互联网教育场景下，这种即时、概括性的反馈对于激励学生、帮助他们定位学习重点至关重要。

2.  **图片各组成部分的功能模块拆解及简要功能概述**

    *   **标题模块**:
        *   包含元素: “答题情况”文字。
        *   功能概述: 清晰标明此组件的用途，即展示用户的答题统计或状态信息。
    *   **题目状态展示模块**:
        *   包含元素: 数字序列（1, 2, 3, 5, 6, 8, 10, 11, 12, 13, 14）和特殊字符“牛”。这些元素排列在一个网格状视图中。
        *   功能概述: 每一个数字代表一个题目的标识。特殊字符“牛”代表对应题目的特定状态，结合上下文“答题情况”，极大概率表示该题回答正确或表现优秀。其他数字可能仅表示题目序号，其显示本身可能代表已作答或已查看。
    *   **导航链接模块**:
        *   包含元素: “查看详情 >”文字链接。
        *   功能概述: 提供一个交互入口，用户点击后可以跳转到更详细的答题分析页面，了解每道题的具体作答情况、正误、解析等。

3.  **服务端需提供的功能和返回的数据内容**

    服务端需要提供以下数据内容以支持该“答题情况”组件的显示：
    *   需要提供组件的标题文本，例如“答题情况”。
    *   需要提供一个用于导航至详细页面的链接地址或标识。
    *   需要提供一个题目状态列表。
    *   对于列表中的每一项题目，需要提供其唯一标识符或序号，如图中所示的数字。
    *   对于列表中的每一项题目，需要提供其具体状态的表示，这种表示可以直接是题目序号本身，或者是如“牛”之类的特殊字符来指示特定状态（例如正确、优秀等）。服务端应明确“牛”字符所代表的业务含义。

4.  **Mermaid 图表描述**
    此图片为UI界面组件示意图，不属于流程图、时序图、类图、ER图、甘特图或饼图的范畴，因此不适用Mermaid语法进行描述。

【============== 图片解析 END ==============】



- 题目列表：在结算页点击查看详情，进入题目列表页面
![in_table_image_MGPobJyOlop6YVxdI4kcucIsn2A]

###### 图片分析
【============== 图片解析 BEGIN ==============】

作为互联网产品经理，该图片展示的是一个移动应用内的 **UI界面原型图**，具体为“题目列表”或“练习回顾”界面的设计。它出自AI课程的PRD文档，用于展示用户在完成练习或测试后查看题目、作答情况及解析的交互界面。

**核心元素与关联：**

1.  **页面标题 (题目列表):** 位于界面顶部，明确告知用户当前所处的功能模块。
2.  **筛选控件 (仅看错题 Toggle开关):** 位于标题栏下方，允许用户筛选题目列表，只显示回答错误的题目。这是核心的筛选交互。
3.  **题目列表区域:** 占据页面的主体，以卡片形式纵向排列展示各个题目。
    *   **题目卡片:** 每个卡片代表一道题目，是信息展示的基本单元。
        *   **题目序号:** 标识题目在列表中的顺序。
        *   **题目类型 (如：单选):** 指明题目的作答形式。
        *   **题干内容:** 显示题目的具体文字描述。
        *   **作答状态 (如：回答正确, 回答错误):** 直观展示用户对该题的作答结果。
        *   **查看解析入口 (查看解析 >):** 引导用户查看该题的详细解答和分析。

**整体作用与价值：**

此界面在AI课程中扮演着学习反馈和巩固知识的关键角色。它允许用户：
*   **回顾练习内容：** 系统性地查看已完成的题目。
*   **定位薄弱环节：** 通过“仅看错题”功能，快速找到自己掌握不牢固的知识点。
*   **深入理解学习：** 通过“查看解析”功能，获取详细的题目讲解，弥补知识盲区，从而提升学习效果。

**各组成部分功能模块拆解：**

*   **1. 页面标题模块:**
    *   **功能概述:** 展示当前页面的名称为“题目列表”。
*   **2. 错题筛选模块:**
    *   **功能概述:** 提供“仅看错题”的开关，用户可激活此开关以仅查看作答错误的题目，再次点击则恢复查看所有题目。
*   **3. 题目信息展示模块 (列表形式):**
    *   **功能概述:** 以列表形式展示一系列题目。每个题目单元包含以下子模块：
        *   **3.1 题目序号:**
            *   **功能概述:** 显示题目的编号。
        *   **3.2 题目类型:**
            *   **功能概述:** 标明题目的类型，如“单选”。
        *   **3.3 题干文本:**
            *   **功能概述:** 显示题目的具体问题描述。
        *   **3.4 作答结果状态:**
            *   **功能概述:** 展示用户对该题的作答结果，如“回答正确”或“回答错误”。
        *   **3.5 解析查看入口:**
            *   **功能概述:** 提供一个“查看解析 >”的链接或按钮，用户点击后可跳转至该题的详细解析页面。

**服务端需提供的功能和数据内容描述：**

服务端需要能够根据请求参数（例如是否筛选错题、所属的练习或测试ID等）返回题目列表数据。
对于题目列表中的每一道题目，服务端需要提供以下信息：
*   题目的唯一标识符。
*   题目的序号或顺序。
*   题目的类型信息。
*   题目的完整题干内容。
*   用户对该题目的作答状态信息（例如：正确、错误、未作答等）。
*   该题目是否拥有解析内容，以便前端判断是否展示查看解析的入口。
*   当用户触发“仅看错题”筛选时，服务端需要支持根据此条件过滤并返回相应的题目数据。

【============== 图片解析 END ==============】



- 题目详情：在题目列表页点击查看解析，展示题目详情
![in_table_image_NdMMbWsS7owwOwxxhlUcTX5rnBc]

###### 图片分析
【============== 图片解析 BEGIN ==============】

根据您提供的图片及其OCR解析文本，这是一张**在线教育产品中的“做题/练习”界面截图或UI设计稿**。

1.  **图片类型、关键元素、组成部分及核心作用**

    *   **图片类型**: UI界面截图/设计稿，展示了在线教育产品中用户进行练习答题时的具体界面。
    *   **核心定位**: 该界面是AI课程中学生进行知识点练习、自我检测和巩固学习成果的核心环节。
    *   **关键元素与层级结构**:
        *   **顶部状态区**:
            *   **计时信息**: 显示用户作答当前题目或整个练习所花费的时间（如“用时01:24”）。
            *   **进度指示**: 显示当前题目在整个练习中的位置（如“3/5”，表示共5题，当前为第3题）。
        *   **题目内容区**:
            *   **题型标识**: 明确题目类型（如“单选”）。
            *   **题干**: 展示题目问题的主体内容（如“关于空间几何, 以下哪个描述是正确的?”）。
            *   **选项列表**: 列出可供选择的答案（如A、B、C、D选项及各自内容）。
        *   **题目辅助信息/解析区** (通常在作答后或查看解析时展示):
            *   **题目解析**: 提供对该题目的详细解答和分析说明。
            *   **考察知识**: 指出该题目所考察的相关知识点。
            *   **猜你想问**: 提供与该题目相关的常见问题或扩展内容。
        *   **操作功能区**:
            *   **加入错题本**: 允许用户将当前题目收藏到错题本中以便后续复习。
            *   **导航按钮**: 包括“上一题”和“下一题”，用于在题目间切换。
    *   **核心作用与价值**:
        *   **知识检验**: 用户通过答题检验对知识点的掌握程度。
        *   **学习反馈**: “题目解析”、“考察知识”为用户提供即时或延后的学习反馈，帮助理解薄弱环节。
        *   **个性化学习辅助**: “加入错题本”功能支持个性化复习；“猜你想问”可能引导用户进行更深入的探究。
        *   **学习导航**: 题目切换功能保证了练习流程的顺畅性。

2.  **各组成部分功能模块及简要概述**

    *   **计时模块**:
        *   功能概述: 记录并显示用户解题所用时间。
    *   **进度显示模块**:
        *   功能概述: 显示用户在当前练习集中的答题进度。
    *   **题目展示模块**:
        *   功能概述: 清晰呈现题目类型、题干内容和所有选项。
    *   **答案选择模块** (图中未直接展示交互过程，但为必备功能):
        *   功能概述: 允许用户从选项中选择一个或多个答案。
    *   **题目解析模块**:
        *   功能概述: 提供题目的正确答案、解题思路和详细步骤。
    *   **知识点关联模块**:
        *   功能概述: 指明该题目所对应的具体知识点。
    *   **智能问答/推荐模块 ("猜你想问")**:
        *   功能概述: 根据当前题目内容，推荐相关的常见问题或扩展知识。
    *   **错题管理模块 ("加入错题本")**:
        *   功能概述: 允许用户将题目添加至个人错题集合。
    *   **题目导航模块 ("上一题", "下一题")**:
        *   功能概述: 支持用户在练习题目之间进行前后切换。

3.  **服务端需提供的功能和返回的数据内容**

    服务端需要提供以下数据内容以支持该界面的完整功能：
    *   关于当前练习或题目的计时信息，例如已用时长。
    *   当前题目的序号以及练习的总题目数量。
    *   当前题目的类型，例如单选题。
    *   当前题目的题干文本内容。
    *   当前题目的所有选项内容列表，每个选项包含其标识（如A, B, C, D）和文本描述。
    *   当前题目的正确答案信息（用于后续判断对错及展示解析）。
    *   当前题目的详细解析文本内容。
    *   与当前题目关联的考察知识点列表或文本描述。
    *   为“猜你想问”功能提供的相关问题及其解答列表。
    *   用户与当前题目相关的状态信息，例如是否已加入错题本。
    *   用于导航的上一题和下一题的标识或可访问性状态。

4.  **Mermaid 图表描述**

    该图片为UI界面截图，不适用于Mermaid中的flowchart、sequenceDiagram、classDiagram、erDiagram、gantt或pieChart等图表类型进行直接描述。

【============== 图片解析 END ==============】



- 推荐学习：
![board_C4lPw82zBhogmqbDXRfcewf9n2c](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844224132.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

好的，我们来分析一下这张关于 AI 课程中课程框架文档组件 V1.0 的图片。

1.  **图片类型、关键元素、结构及核心作用**

    *   **图片类型**: 这是一张 **流程图**。
    *   **关键元素**:
        *   **起始判断**: 用户掌握度是否达到目标掌握度。
        *   **条件分支**: 根据掌握度、巩固练习完成情况、加练情况进行路径选择。
        *   **推荐结果**: 推荐学习策略、推荐巩固练习、推荐巩固练习加练、推荐拓展练习。
    *   **层级化结构与关联**:
        1.  **顶层判断**: “掌握度是否达到目标掌握度？”
            *   **是**: 直接导向“推荐学习策略”（其中一种策略可能是“无推荐学习内容”，表示当前模块已达标）。
            *   **否**: 进入下一层级判断。
        2.  **第二层判断 (当掌握度未达标时)**: “用户是否完成巩固练习？”
            *   **否**: 导向“推荐巩固练习”。
            *   **是**: 进入下一层级判断。
        3.  **第三层判断 (当掌握度未达标且已完成巩固练习时)**: “是否有未完成的加练？”
            *   **是**: 导向“推荐巩固练习加练”。
            *   **否 (没有加练/加练已完成)**: 导向“推荐拓展练习”。
    *   **核心作用与价值**: 此流程图清晰地定义了在 AI 课程中，系统根据用户的学习掌握程度和练习完成情况，动态推荐下一步学习内容的决策逻辑。其核心价值在于实现个性化学习路径推荐，确保用户针对性地补强薄弱环节或进行能力拓展，从而提升学习效率和效果。

2.  **各组成部分功能模块拆解**

    *   **掌握度评估模块**:
        *   **功能概述**: 判断用户当前知识点/技能的掌握水平是否达到了预设的目标水平。
    *   **学习策略推荐模块**:
        *   **功能概述**: 当用户掌握度达标时，系统推荐的宏观学习建议或指引，可能包括无需额外学习当前模块或者转向新模块的建议。
    *   **巩固练习状态检查模块**:
        *   **功能概述**: 检查用户是否完成了针对当前学习内容的巩固练习。
    *   **巩固练习推荐模块**:
        *   **功能概述**: 当用户未完成巩固练习时，向用户推荐相应的巩固练习内容。
    *   **加练状态检查模块**:
        *   **功能概述**: 检查用户是否存在未完成的巩固练习相关的加练任务，或是否已完成所有加练任务。
    *   **巩固练习加练推荐模块**:
        *   **功能概述**: 当用户已完成巩固练习但仍有未完成的加练时，向用户推荐这些加练内容。
    *   **拓展练习推荐模块**:
        *   **功能概述**: 当用户掌握度未达标，但已完成巩固练习及所有相关加练（或无加练）时，向用户推荐拓展性练习内容，以进一步深化理解或拔高能力。

3.  **服务端需提供的功能和数据内容**

    服务端需要提供以下数据和支持相应的功能：
    *   需要提供用户对于特定知识点或课程单元的当前掌握度数据。
    *   需要提供该知识点或课程单元预设的目标掌握度数据。
    *   需要提供用户针对特定知识点或课程单元的巩固练习的完成状态信息。
    *   需要提供与巩固练习相关的加练任务的存在状态以及用户的完成状态信息。
    *   服务端需要能够根据用户的掌握度、巩固练习完成情况、加练完成情况，结合预设的推荐逻辑，返回相应的推荐指令，例如推荐学习策略、推荐具体的巩固练习内容、推荐具体的巩固练习加练内容或推荐具体的拓展练习内容。
    *   服务端需要能够提供不同类型的学习内容，包括巩固练习、巩固练习的加练以及拓展练习的具体内容或其标识。
    *   服务端需要能够提供学习策略的具体内容或指引。

4.  **Mermaid 流程图描述**

    ```mermaid
    graph TD
        A["掌握度评估"] --> B{"掌握度是否达到目标掌握度?"};
        B -- "是" --> C["推荐学习策略 (OCR提及: 无推荐学习内容)"];
        B -- "否" --> D{"用户是否完成巩固练习?"};
        D -- "否" --> E["推荐巩固练习"];
        D -- "是" --> F{"是否有未完成的加练?"};
        F -- "是" --> G["推荐巩固练习加练"];
        F -- "否 (没有加练/加练已完成)" --> H["推荐拓展练习"];
    ```

【============== 图片解析 END ==============】



1. 根据策略，展示不同的学习模块名称、预估题目数量
1. 模块名称：巩固练习、巩固练习-加练、拓展练习
1. 无推荐学习内容时，展示默认文案“掌握度已达成，暂无推荐学习内容”
1. 用户点击去练习，进入对应的学习模块
![in_table_image_LdiGb9jhjob9OTxCdpIc7YrTnbd]

###### 图片分析
【============== 图片解析 BEGIN ==============】

1.  **图片类型与核心价值解析**

    该图片为 **UI 组件图**，出自 AI 课中课程框架文档的需求部分。

    *   **关键元素、组成部分及层级化结构：**
        1.  **顶层/容器层：** "推荐学习" - 标识整个组件的功能或内容分类。
        2.  **内容层/行动引导层 (并列关系)：**
            *   **行动点 (左侧)：** "去练习" - 按钮形式，引导用户进行下一步操作。
            *   **描述信息 (右侧)：**
                *   "巩固练习" - 对练习类型的说明。
                *   "约12题" - 对练习量的说明。

        层级关系为："推荐学习" 作为该组件的标题或分类，其下包含一个可交互的 "去练习" 按钮，以及对该练习的补充说明 "巩固练习" 和 "约12题"。

    *   **核心作用与价值：**
        该组件的核心作用是在课程学习过程中，向用户推荐相关的练习机会，以巩固所学知识。其价值在于：
        *   **主动引导：** 提醒用户进行练习，强化学习闭环。
        *   **信息明确：** 清晰告知用户练习的类型（巩固练习）和大致体量（约12题），帮助用户设置预期。
        *   **提高参与度：** 通过便捷的 "去练习" 入口，降低用户参与练习的门槛。

2.  **功能模块拆解与概述**

    *   **标题模块 (推荐学习):**
        *   功能概述: 展示该组件的主题或推荐内容的类别，此处为推荐学习相关的练习。
    *   **行动模块 (去练习):**
        *   功能概述: 提供一个可点击的交互入口，用户点击后将跳转至相应的练习页面或开始练习流程。
    *   **练习类型描述模块 (巩固练习):**
        *   功能概述: 说明推荐练习的性质或目的，此处指明是用于巩固知识的练习。
    *   **练习量描述模块 (约12题):**
        *   功能概述: 告知用户该练习包含的大致题目数量，帮助用户预估所需时间或精力。

3.  **服务端需提供的功能和返回的数据内容**

    服务端需要提供以下数据内容来支持该“推荐学习”组件的动态展示：
    *   需要提供组件的标题文本。
    *   需要提供行动按钮的显示文本。
    *   需要提供与行动按钮关联的跳转逻辑或练习资源的标识信息。
    *   需要提供对推荐练习的类型描述文本。
    *   需要提供对推荐练习的题目数量或大致范围的描述文本。

4.  **Mermaid 图表描述**

    由于该图片是 UI 组件的静态展示，不属于流程图、时序图等适合用特定 Mermaid 语法直接转换的图表类型。若要以图表形式表示其结构，可以使用 Mermaid 的 `graph` (基本流程图) 来表示其组成关系：

    ```mermaid
    graph TD
        A["推荐学习 (组件标题)"] --> B["去练习 (行动按钮)"];
        A --> C["巩固练习 (练习类型描述)"];
        A --> D["约12题 (练习量描述)"];
    ```

【============== 图片解析 END ==============】



- 课程反馈
1. 展示极差、较差、一般、满意、很棒，5类评价
1. 用户点击某个icon后，展示对应的反馈弹窗
1. 提交按钮默认为不可点击状态，用户选择了某个选项后，切换为可提交状态
1. 提交后，toast “感谢反馈，我们会持续改进！”
![in_table_image_IqW3bjKbTogLnVxrMvucpnGMnWf]

###### 图片分析
【============== 图片解析 BEGIN ==============】

作为互联网产品经理，我们审视这张来源于“PRD - AI课中 - 课程框架文档组件 V1.0”的图片。

1.  **图片类型、关键元素、结构及核心作用**

    *   **图片类型**: UI设计稿/界面截图。
    *   **关键元素**:
        *   标题/问题文本：“本节课感受如何?”
        *   评价选项（从左到右/从上到下）："极差"、"较差"、"一般"、"满意"、"很棒"。
    *   **层级化结构**:
        *   **课程反馈模块 (顶层)**
            *   **反馈引导问题区**: 包含引导用户进行反馈的文本内容 (“本节课感受如何?”)。
            *   **反馈选项区**: 包含一组预设的、供用户选择的反馈级别。
                *   选项1: "极差"
                *   选项2: "较差"
                *   选项3: "一般"
                *   选项4: "满意"
                *   选项5: "很棒"
    *   **核心作用与价值**: 此组件的核心作用是收集用户（学员）在完成一节课程后的即时感受和评价。这对于互联网教育产品至关重要，其价值在于：
        *   **教学质量监控**: 快速了解学员对课程内容的接受度和满意度。
        *   **课程迭代优化**: 收集到的反馈数据是迭代和优化课程内容、教学方法、讲师表现等的重要依据。
        *   **用户体验提升**: 提供便捷的反馈渠道，让用户感受到被重视，提升用户粘性。
        *   **个性化推荐辅助**: 长期积累的数据可能有助于分析不同用户对不同类型课程的偏好。

2.  **各组成部分功能模块拆解**

    *   **反馈引导语展示模块**:
        *   **功能概述**: 用于向用户清晰展示本次反馈的主题或问题，即“本节课感受如何?”。
    *   **反馈评级选项模块**:
        *   **功能概述**: 提供一组预定义的、具有明确层级关系的评价选项，用户可以通过点选其中一个选项来表达对课程的感受。包含“极差”、“较差”、“一般”、“满意”、“很棒”五个具体的互动选项。
    *   **用户选择交互模块 (隐含)**:
        *   **功能概述**: 虽然未直接显示提交按钮，但此模块 подразумевает (implies) 用户能够点选其中一个评级选项，并且系统能够记录用户的选择。

3.  **服务端需提供的功能和数据内容**

    服务端需要提供以下功能和数据内容以支持该组件的正常运作：
    *   需要能够下发用于反馈引导的文本信息，即具体的提问文本。
    *   需要能够下发一组评价选项的文本描述，这些描述将作为用户可选择的反馈等级。
    *   当用户完成选择并提交反馈时，服务端需要能够接收并记录用户针对特定课程或课节所选择的评价等级数据。
    *   服务端需要能够关联用户的身份信息、课程或课节的唯一标识以及用户提交的评价数据。

4.  **Mermaid 图表描述**

    该图片为UI界面截图，不属于流程图、时序图、类图、ER图、甘特图或饼图。因此，不适用Mermaid语法进行直接描述其视觉内容。如果需要描述用户与此组件的交互流程，可以简化为一个简单的用户操作流程，但图片本身并非流程图。

    若要描述**用户使用此组件进行反馈的简化流程**，可以如下表示：

    ```mermaid
    graph TD
        A[用户看到课程反馈界面] --> B{本节课感受如何?};
        B --> C1[选择: 极差];
        B --> C2[选择: 较差];
        B --> C3[选择: 一般];
        B --> C4[选择: 满意];
        B --> C5[选择: 很棒];
        C1 --> D[(提交反馈)];
        C2 --> D;
        C3 --> D;
        C4 --> D;
        C5 --> D;
    ```

    请注意：上述Mermaid图描述的是基于该UI组件的**用户交互流程**，并非对图片本身视觉结构的直接转换。图片本身是一个静态的UI组件展示。

【============== 图片解析 END ==============】



极差&较差

![image_NB1vbY3jfoxkbsxNTv2cxUGxnwc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844229475.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

该图片为用户反馈界面的UI截图，属于课程框架文档中的一个具体组件设计。

1.  **图片类型解析与核心价值**

    *   **图片类型**: UI界面截图/组件设计图。
    *   **关键元素与组成部分**:
        *   **整体评价**: 包含五个等级的评价选项：“极差”、“较差”、“一般”、“满意”、“很棒”。
        *   **分类标签反馈**:
            *   **讲解**: 可选标签“太简略”、“枯燥乏味”。
            *   **解析**: 可选标签“太简略”、“枯燥乏味”。
            *   **其他**: 可选标签“缺乏针对性”、“弹窗太多”。
        *   **开放式文本反馈**: “还有其他想说_”的文本输入框。
        *   **操作按钮**: “放弃评价”和“提交”两个按钮。
    *   **层级化结构与关联**:
        1.  用户首先进行整体评价（五个等级中选择一个）。
        2.  接着，用户可以选择一个或多个预设的分类标签来具体说明评价的原因，这些标签按“讲解”、“解析”、“其他”分类。
        3.  用户还可以通过开放式文本框输入更个性化或未被标签覆盖的反馈。
        4.  最后，用户选择“提交”以发送反馈，或“放弃评价”以取消操作。
    *   **核心作用与价值**: 此组件的核心作用是收集学生对AI课程特定环节（如课程讲解、题目解析等）的即时反馈。其价值在于：
        *   **量化与质化结合**: 通过等级评价实现反馈的量化，通过标签和文本输入实现反馈的质化。
        *   **结构化反馈**: 预设标签有助于引导用户提供更具体的反馈，便于后续数据分析和问题定位。
        *   **用户体验优化**: 为用户提供便捷的反馈渠道，以持续迭代和优化课程内容、形式及产品体验。
        *   **数据驱动决策**: 收集到的反馈数据是产品优化的重要依据，帮助团队了解用户痛点和满意点。

2.  **功能模块拆解**

    *   **整体评价选择模块**:
        *   功能概述: 允许用户从五个预设等级（极差、较差、一般、满意、很棒）中选择一个作为对当前内容的总体评价。
    *   **“讲解”类标签反馈模块**:
        *   功能概述: 用户可针对“讲解”方面选择一个或多个预设问题标签，如“太简略”、“枯燥乏味”。
    *   **“解析”类标签反馈模块**:
        *   功能概述: 用户可针对“解析”方面选择一个或多个预设问题标签，如“太简略”、“枯燥乏味”。
    *   **“其他”类标签反馈模块**:
        *   功能概述: 用户可针对其他方面选择一个或多个预设问题标签，如“缺乏针对性”、“弹窗太多”。
    *   **开放式文本输入模块**:
        *   功能概述: 提供一个文本输入框，供用户输入预设标签未能覆盖的其他意见或详细说明。
    *   **反馈操作模块**:
        *   功能概述: 包含“放弃评价”按钮（用于取消当前反馈操作）和“提交”按钮（用于将填写的反馈信息发送给服务端）。

3.  **服务端需提供的功能和返回的数据内容**

    服务端需要能够接收并存储用户提交的反馈信息。具体来说：
    *   服务端需要能够接收用户选择的整体评价等级信息。
    *   服务端需要能够接收用户选择的各项分类下的具体标签信息，应能区分标签所属的分类（如“讲解”类下的“太简略”，“解析”类下的“枯燥乏味”，“其他”类下的“缺乏针对性”等）。用户可能选择多个标签。
    *   服务端需要能够接收用户在开放式文本框中输入的文本内容。
    *   服务端需要能够将上述反馈信息与对应的用户、课程、课时或其他具体反馈对象关联起来（此关联信息不在此UI组件本身，但为反馈系统必备）。

    在用户打开此反馈组件时，服务端可能需要提供以下数据以供前端渲染（虽然图中未直接显示，但这是常见实践）：
    *   可选的整体评价等级列表。
    *   不同分类下的可选标签列表，包括分类名称（如“讲解”、“解析”、“其他”）及其对应的标签名称（如“太简略”、“枯燥乏味”、“缺乏针对性”、“弹窗太多”）。

4.  **Mermaid 图表描述**

    该图片为UI组件截图，不适用于流程图、时序图、类图、ER图、甘特图或饼图的Mermaid语法描述。它展示的是一个静态的界面设计。

【============== 图片解析 END ==============】



一般

![image_IPINbKITOowin3xNiPMckRrynkf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844230011.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

好的，我们来分析一下这张关于“AI课中 - 课程框架文档组件 V1.0”中的图片。

1.  **图片类型、关键元素、层级结构及核心作用**

    *   **图片类型**：这是一张 **UI界面截图**，具体呈现的是一个用户反馈或课程评价的表单界面。
    *   **关键元素与组成部分**：
        *   **整体满意度评价**：顶部的五个描述词（极差、较差、一般、满意、很棒）代表了用户对课程或某个环节的整体评分等级。
        *   **分类标签化反馈**：
            *   **讲解**：包含“有点简略”、“枯燥乏味”、“题有点多”、“题有点少”、“有点困难”、“有点简单”等预设标签。
            *   **解析**：包含“有点简略”、“枯燥乏味”等预设标签。
            *   **其他**：包含“弹窗有点多”、“针对性一般”、“收获一般”等预设标签。
        *   **开放式文本反馈**：
            *   **反馈**：提供一个文本输入区域，提示为“还有其他想说_”，供用户输入更具体的、未被标签覆盖的意见。
        *   **操作按钮**：底部的“放弃评价”和“提交”按钮。
    *   **层级化结构与元素间关联**：
        1.  用户首先进行**整体满意度评价**（如选择“满意”）。
        2.  然后，用户可以针对不同维度（**讲解、解析、其他**）选择更具体的预设**标签**来阐述原因或提供细节。用户可以选择一个或多个标签。
        3.  如果预设标签不足以表达，用户可以在**开放式文本反馈区（反馈）**输入补充内容。
        4.  最后，用户通过**操作按钮**决定是提交评价还是放弃。
    *   **核心作用与价值**：
        此组件的核心作用是**收集用户（学员）对AI课程内容、讲解方式、题目、系统体验等方面的多维度、结构化及非结构化反馈**。
        其价值在于：
        *   为课程优化提供数据支持：通过分析用户反馈，可以精准定位课程的优点与不足，指导后续迭代方向。
        *   提升用户参与感：提供便捷的反馈渠道，让用户感受到被重视。
        *   量化评价：将部分主观感受通过预设标签进行初步量化，便于统计分析。
        *   补充定性洞察：开放式文本输入为深入理解用户具体问题提供了可能。

2.  **功能模块拆解及简要概述**

    *   **整体满意度评估模块**：
        *   功能概述：允许用户从五个预设等级（极差、较差、一般、满意、很棒）中选择一个，以表达对当前内容的总体评价。
    *   **“讲解”维度反馈模块**：
        *   功能概述：允许用户针对课程内容的“讲解”方面，通过点选预设标签（如“有点简略”、“枯燥乏味”、“题有点多”、“题有点少”、“有点困难”、“有点简单”）提供具体反馈。
    *   **“解析”维度反馈模块**：
        *   功能概述：允许用户针对课程内容的“解析”部分，通过点选预设标签（如“有点简略”、“枯燥乏味”）提供具体反馈。
    *   **“其他”维度反馈模块**：
        *   功能概述：允许用户针对未能被“讲解”或“解析”覆盖的其他方面（如“弹窗有点多”、“针对性一般”、“收获一般”），通过点选预设标签提供反馈。
    *   **开放式文本输入模块 (反馈)**：
        *   功能概述：提供一个文本输入框，允许用户输入自定义的、更详细或预设标签未能涵盖的反馈意见。
    *   **评价提交与取消模块**：
        *   功能概述：提供“提交”按钮以发送评价数据，“放弃评价”按钮以取消当前操作并关闭评价界面。

3.  **服务端需提供的功能和返回的数据内容**

    服务端需要具备接收并存储用户评价数据的功能。当用户提交评价时，服务端需要能够接收以下数据内容：
    *   用户选择的整体满意度等级的文本描述。
    *   用户在“讲解”维度下选择的一个或多个反馈标签的文本描述集合。
    *   用户在“解析”维度下选择的一个或多个反馈标签的文本描述集合。
    *   用户在“其他”维度下选择的一个或多个反馈标签的文本描述集合。
    *   用户在“反馈”文本框中输入的完整文本内容。

    此外，为了动态渲染此评价组件，服务端在加载该组件时，可能需要提供以下数据给前端：
    *   整体满意度评价的各等级选项列表。
    *   反馈的分类列表（如讲解、解析、其他）。
    *   每个分类下可供选择的具体标签项列表。
    *   开放式文本输入框的默认提示文本。

4.  **Mermaid 图表描述**

    该图片为UI界面截图，不适合直接转换为Mermaid的flowchart, sequenceDiagram, classDiagram, erDiagram, gantt, 或 pieChart。它本身是产品设计的一部分，用于展示用户交互界面和信息结构。

【============== 图片解析 END ==============】



满意&很棒

![image_Ej44bHWiGoIzdyxDINBcT551nFC](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844230501.png)

- 操作按钮：
1. 回看课程：点击后重新进入课程
1. 所有组件都是已解锁状态，用户可以自由学习任何组件
1. 保留历史答题记录，用户进入练习组件时看到的是答题结果页面。
1. 完成：用户点击后返回课程入口页面。
- 展示：
1. 按照组成一节课程的“文档组件、练习组件”的排序进行展示。
1. 分为“已解锁”、“学习中”、“待解锁”三种状态。
- 已解锁：用户学习过的组件，全部为已解锁状态
- 待解锁：还没有学习的组件为待解锁状态
- 学习中：用户当前正在学习的组件
- 如果用户「回看课程」，则所有的组件都为解锁状
- 交互：
- 点击「课程进度」按钮，展示进度，暂停讲解
- 点击屏幕其他区域，收起进度，继续讲解
- 点击“已解锁”状态的组件，可直接跳转到对应组件的内容。
- 组件跳转逻辑：
1. 用户点击进入视频组件，从上次跳出的进度开始播放视频。如果上次视频已播完，则自动从头开始播放。
1. 用户点击进入练习组件：
1. 如果上次的题目没有答完，从上次跳出的题目开始继续答题
1. 如果上次的题目已全部答完，从第一题开始展示用户历史答题记录。
1. 用户可点击「下一题」，逐个题目查看后，进入下一个课程组件。
1. 用户跳转到之前学习过的组件，完成后自动进入课程中的下一个组件（视频/练习组件）
1. 在组件间切换，不影响学习进度的统计，学习进度还是按“已学完的组件数量/一节课中文档+练习组件的数量之和”计算。
- 文档组件入口：
![image_G72ZbIuhno1CguxxK1EcS6U3n3d](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844230999.png)

- 练习组件入口
![image_Wj2mbKm98onhyqxuQOXcdSW3npb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844231491.png)

- 课程进度
![image_TlI4bWfnFooISex1mYPcUE9Hnuf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844232054.png)

- 历史答题记录
![image_HmXSbDdSnoSjajx6iuPcKVOunZc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844232817.png)

- 退出课程：在上课过程中，点击左上角的退出按钮，记录用户的当前进度，返回课程入口页。
- 退出后再次进入课程的进度策略如下：
- 文档组件：从退出的时间点继续播放，默认进入跟随状态
- 练习组件：进入用户退出时未答完的题目，且保存退出前的答题结果
- 答疑组件：进入调起答疑组件的 文档 / 练习组件，默认不进入答疑组件。当用户再次点击答疑组件时，可查看历史对话记录。
- 文档组件：从退出的时间点继续播放，默认进入跟随状态
- 练习组件：进入用户退出时查看题目解析的页面


## 文档组件

相比Demo V1.1的改动点：

1. 内容播放：
1. 增加勾画轨迹，和JS内容同步播放
1. 默认展示字幕
1. 交互操作：
1. 增加双击、长按等快捷交互
1. 增加1.75和3倍速
1. 增加前进 / 后退 10s
| 模块/功能 | 需求描述 | 原型图 |
| --- | --- | --- |
| 模式切换 | 进入课程后，默认进入「跟随模式」当用户滑动JS动画内容时，进入「自由模式」右上角常驻展示「问一问」和「课程进度」 |  |
| 内容播放 |  |  |
| 快捷交互 | 跟随模式和自由模式均支持以下快捷交互 |  |
| 操作面板 | 跟随模式和自由模式均支持唤起操作面板，单击文档以外区域可唤起操作面板。 | ![in_table_image_UEyNbKjIqoO8cPx4xbqcyAdJnSc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844236175.png) |
| 进入下一组件 | 分为三种情况 |  |

跟随模式

![image_AZEdbJEhgo6NC9xhyWAcv2njnAo](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844233304.png)



toast提示

![image_BpvgbYREKoeNkExjuvFca1qlnvd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844234185.png)

自由模式

![image_IrKybk0AboY613xigFbcQq4hnib](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844234691.png)

- 跟随模式
1. 跟随模式下，数字人视频、勾画轨迹和JS 动画内容自动播放，三个内容间的进度保持一致。
1. 播放时，板书对应的内容块（句子）下方展示进度线，表示目前讲到这里了。
1. 如果老师讲解内容在多个句子中上下来回跳跃，进度线始终在最后的一个句子后面，不跟着讲解上下跳跃。
- 自由模式
1. 进入自由模式，数字人视频默认正常播放，进度不受影响。
1. 板书不跟随进度自动播放，但是仍然展示进度线，提醒学生当前讲到哪里。
1. 不播放勾画轨迹
1. 单击没有评论的文档内容 - 从这里学
1. 没有评论的文档内容：当前内容所在的句子高亮展示，并出现「从这里学」按钮（同demoV1.1）
1. 用户点击「从这里学」，数字人视频&勾画轨迹从句子对应的时间戳开始播放。
1. 自由模式下点击「从这里学」，自动切换为跟随模式
1. 单击有评论的文档内容 - 展示评论内容（本期不做）
1. 单击文档以外区域 - 唤起操作面板
1. 长按文档内容：选中文字/图片内容，唤起问一问、评论操作（本期不做）
1. 用户可拖拽高亮条，选择文字
1. 点击问一问，进入答疑组件
1. 点击评论，唤起键盘，用户可输入内容进行发表
1. 长按文档以外区域：3倍速播放内容，松手后恢复原始速度
1. 双击 - 切换播放 / 暂停状态
单击文档-从这里学-文字

![image_RDuzbOPUYoyDHTxWwdqcQDslnwb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844235185.png)

单击文档-从这里学-公式

![image_KgQob6BNFoxTTOxHT5ScxdZWnYf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844235723.png)

- 页面元素：
1. 左上角展示「退出学习」
1. 数字人区域展示“视频播控”：包括字幕、播放/暂停、倍速、前进 / 后退 10s
- 视频播控：
1. 字幕：点击切换字幕开关
1. 暂停/播放：点击暂停按钮，暂停所有内容的播放，再次点击时恢复播放。
1. 视频倍速：倍速需保持数字人、JS板书、勾画内容的播放进度一致。
1. 点击倍速按钮，可选择倍速播放，支持「0.75、1.0、1.25、1.5、1.75、2.0」6种倍速选择。
1. 全局倍速，设置倍速后对一节课中的所有视频组件生效。退出课程下次重新进入课程，恢复正常速度。
1. 前进 / 后退 10s：点击后，数字人、JS板书、勾画内容进度同步变化
![analyze_in_table_image_UEyNbKjIqoO8cPx4xbqcyAdJnSc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844236175.png)

1. 从文档组件进入文档组件
1. 在「跟随模式」下正常学习，数字人视频播完后，自动进入下一个组件，无转场动效
1. 在「跟随模式」和「自由模式」向上滑动文档，进入下一学习内容。
1. 当前片段的JS动画滑动到底部时，会出现“阻尼效果”，用户继续向上滑动将切换至下一个组件的内容
1. 进入下一课程组件后，默认为跟随模式。
1. 从文档组件进入练习组件
「跟随模式」和「自由模式」下均展示切换动效

1. 从练习组件进入文档组件
「跟随模式」和「自由模式」下均展示切换动效





# 六、数据需求

## 埋点需求

**pv/uv采集**：所有事件默认都要带上pv/uv统计。

**时间戳记录**：每条事件自动带上timestamp字段，方便后续行为序列分析。

**用户身份信息**：带上 user_id、school_id作为隐式参数。



## 数据存储需求

| 模块 | 数据项 |
| --- | --- |
| 整体 |  |
| 文档组件 |  |
| 视频组件 |  |
| 答疑组件 |  |

1. 每个学生在每节课的答题数量、正确率、学习时长、获得的积分
1. 每个学生在每节课的学习进度（计算口径同课程进度）
1. 每个文档的时长、用户实际播放时长
1. 每个视频的时长、用户实际播放时长
1. 用户对话明细
1. 每个会话的对话轮数（每个视频/题目记为一个新会话）
1. 每一轮用户的输入方式


