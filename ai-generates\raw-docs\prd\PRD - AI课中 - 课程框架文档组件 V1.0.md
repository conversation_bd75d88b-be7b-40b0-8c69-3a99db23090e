PRD - AI课中 - 课程框架+文档组件 V1.0

**版本管理**

| 版本号 | 日期 | 变更人 | 变更类型 | 变更详情 |
| --- | --- | --- | --- | --- |
| V1.0 | 2025-04-08 | 钱晋菲 | 新建 | 新建文档 |
|  |  |  |  |  |
|  |  |  |  |  |

**关联需求**

| 关联需求名称 | 所属 PM | 需求进度 | 文档链接 |
| --- | --- | --- | --- |
| DemoV1.0 |  | 已上线 |  |
| DemoV1.1 |  | 已上线 |  |
| 课程配置 |  | 开发中 |  |
| 掌握度 V1.0 |  | 开发中 |  |
| 课中-练习组件 |  | 待开发 |  |

**设计稿**

视觉稿：待补充



# 一、背景和目标

## 需求背景

在前期两个版本的 demo 进校调研过程中，我们收集了一系列待解决的关键问题，如：板书内容过多，高亮时机和老师讲解对不上，不能双击播放暂停，自由/跟随模式的切换交互不好理解等，这些问题对课程体验造成了负面影响。因此，我们计划在正式版本中优先解决这些问题，以确保课程基础体验（包括学习与练习环节）的质量。

此外，正式版本还将整体迭代课中体验，让上课过程更接近1v1真人直播课，提供沉浸式地课堂体验，展示系统的个性化和智能型。

## 项目收益

优化课中体验，提升学习效果及课程满意度

## 覆盖用户

使用新AI课的全量用户

## 方案简述

本方案围绕AI课中学习体验升级，在保留原有课中基本结构的基础上，进行了优化与能力扩展，具体包括：

- 课程框架升级：优化开场页与结算页设计。
- 文档组件增强：引入同步播放勾画轨迹、快捷交互（双击、长按、倍速调整、10秒快退快进）等功能，提升学生自主学习流畅度。
本版本聚焦于打磨课中体验的底层能力，为后续引入积分体系、小组战队、互动讲题、评论等丰富玩法奠定基础。

## 未来会做什么

1. 框架中加入积分体系和小组战队。
1. 支持互动讲题，自动化逐步讲解题目，并在讲解过程中加入互动问题
1. 支持在文档中长按评论
1. 增加费曼组件、互动组件


# 二、名词说明

暂无



# 三、业务流程

整体课程结构和demo阶段类似，V1.0只支持 「文档组件」、「练习组件」、「答疑组件」

一节课由不同的可配置组件构成。组件之间不同的组合方式构成了不同的课程类型。

答疑组件在课中不能单独配置，需要挂载在「文档组件」/「练习组件」下

| 单节课程结构 | 组件类型 | 说明 |
| --- | --- | --- |
| ![in_table_board_FdxgwtizahKubpbiJmFcvfeVnmb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844222616.png) | 文档组件 | 课程内容为JS文档形式，支持学生在上课过程中自由浏览，支持点击/长按进行提问 |
|  | 练习组件 | 支持各类题型（一期仅支持单选），可按策略推送题目 |
|  | 答疑组件 | 基于大模型能力和学生进行实时对话，解答学生问题 |

![analyze_in_table_board_FdxgwtizahKubpbiJmFcvfeVnmb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844222616.png)



# 四、需求概览

整体需求规划详见：[AI课中需求盘点](https://wcng60ba718p.feishu.cn/wiki/Htv0wNEAxis9dAkBjTTcjcWlnTb)

本次方案在demo V1.0 & V1.1的基础上，主要围绕课程框架、文档组件、练习组件进行优化升级，旨在提升用户体验和学习效果。

| 模块 | 需求描述 | 优先级 |
| --- | --- | --- |
| 课程框架 |  | P0 |
|  |  |  |
|  |  |  |
| 文档组件 |  | P0 |
|  |  | P0 |

1. 课程开场页、结算页优化
1. 课程进度，支持用户在一节课的组件中自由切换进度
1. 退出/继续课程
1. 内容播放：
1. 增加勾画轨迹，和JS内容同步播放
1. 未播放的区域呈现模糊/弱化效果，让学生的注意力聚焦在当前内容块上
1. 默认展示字幕
1. 交互操作优化：
1. 增加双击、长按等快捷交互
1. 增加1.75和3倍速
1. 增加前进 / 后退 10s


# 五、详细产品方案

## 课程框架

相比Demo V1.0的改动点：

1. 去掉每个组件之间的串场动效
1. 结算页：
1. 增加“掌握度”、“答题情况”、“推荐学习”相关信息
1. 鼓励文案展示规则调整
1. 课程进度：保存用户答题记录，用户切换到已做过的练习模块时直接展示答题记录
| 模块/功能 | 需求描述 | 原型图 |
| --- | --- | --- |
| 开场页 | 注：不同学科有不同的开场页，页面框架不变，主要更换背景图和色值 | ![in_table_image_WVtsbsQ17oojarxxvYQcPWr0nqc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844224709.png) |
| 结算页 | 完成一节课全部内容的学习后，进入结算页。 | ![in_table_image_Wb2pba8ILoTQ5dxwpbtcrWt2npb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844225940.png) |
|  |  | ![in_table_image_P5GCbSKSfoYqb4x3uBscWcKfn9d](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844226411.png) |
|  |  | ![in_table_image_AvqMbn4onoSkCFxclcScqQCpnwg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844226812.png) |
|  |  | ![in_table_image_NdMMbWsS7owwOwxxhlUcTX5rnBc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844228176.png) |
|  |  | ![in_table_image_LdiGb9jhjob9OTxCdpIc7YrTnbd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844228587.png) |
|  |  | ![in_table_image_IqW3bjKbTogLnVxrMvucpnGMnWf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844229009.png) |
|  |  |  |
| 课程进度 | 用户可以在任何组件中，灵活调整学习进度。 |  |
| 退出/继续课程 |  |  |

开场页的作用：建立仪式感：进入课程直接看到开场页，会让学习者有“正式开始”的感觉，这种心理上的“仪式感”能提高学习的专注度

- 页面展示：
- 第X章：后台配置
- 课程序号：后台配置
- 课程名称：后台配置，取对应业务树末级知识点名称（25个字以内）
- 交互：进入开场页后自动播放IP动效+音频，播放完成后，自动进入 AI 课第一小节
![analyze_in_table_image_WVtsbsQ17oojarxxvYQcPWr0nqc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844224709.png)

结算页的作用：

1. 恭喜完成课程（情绪价值）
1. 展示本次学习表现
1. 推荐后续学习路径
- 庆祝动效及文案：
1. 动效：
1. 课程包含多个IP角色，结算页随机选择某个IP
1. 每个IP有“开心”、“鼓励”两类动效，展示策略如下
![board_QBEwwqJVOh02Q9bwVXwcSqvmncd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844223173.png)

1. 文案：
1. 文案包括「主文案」「副文案」两部分内容
1. 文案内容和IP相关，每个IP维护一套反馈文案
1. 根据用户的学习表现，展示不同的反馈文案。
1. 如果同时命中两种情况，则随机选择其中的一种情况
1. 一种情况下有多条文案时，随机选择一条展示
![board_QJQEwg4J2hl7JfbidfIceBcxnQc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844223676.png)

![analyze_in_table_image_B1KqbxabioPL0kxg4BTcuAb0nWg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844225319.png)

![analyze_in_table_image_Wb2pba8ILoTQ5dxwpbtcrWt2npb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844225940.png)

- 学习表现（同demoV1.0）：
1. 学习用时：用户完成本节课学习的累计学习时长。
1. 答题数：用户在这节课本次学习中的累计答题数量
1. 正确率：答对的题目数量 / 答题数量
注：已学完的课程又重新学习，答题数量、正确率、时长均不累计。

![analyze_in_table_image_P5GCbSKSfoYqb4x3uBscWcKfn9d](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844226411.png)

- 外化掌握度：
计算方式详见：[PRD - 掌握度策略 - V1.0](https://wcng60ba718p.feishu.cn/wiki/J3d0wrEaDiWRy6kj2uVcQKLfnmh)

1. 掌握度说明：在掌握度标题旁展示对掌握度的说话信息，用户点击 i ，展示说明信息。文案如下：
掌握度反映了你对本节课的掌握情况，和你的答题正确率、题目难度相关。

掌握度完成进度和你设定的学习目标相关，修改目标后进度会重新计算哦。

1. 掌握度完成进度：用户当前掌握度 / 目标掌握度，保留小数点后两位，[0 , 1]
1. 掌握度变化：本次学习后的进度 - 本次学习前的进度。
注：

1. 初始掌握度不用于进度计算，用户首次学习的掌握度变化 = 本次学习后的进度 - 0
1. 如果用户本次学习后掌握度下降，此处展示的掌握度不变化
![analyze_in_table_image_AvqMbn4onoSkCFxclcScqQCpnwg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844226812.png)

- 答题情况：展示用户本次学习的答题正误情况
1. 正确和错误展示不同的样式
1. 交互 ：
1. 如果题目数量超过14个，页面可上划展示剩余题目
1. 点击查看详情，进入题目列表页面
1. 点击某个题目，直接进入题目详情页


- 题目列表
1. 展示题号、题目类型、题干、作答结果。
1. 题干：默认最多展示2行，超过部分展示…
1. 作答结果：包括回答正确、回答错误、部分正确，三种情况
1. 交互
1. 点击仅看错题，列表中只展示回答错误和部分正确的题目
1. 点击查看解析，进入对应题目的详情页


- 题目详情：点击题目对应的查看解析，展示题目详情
1. 用时：用户完成这道题目的用时
1. 序号：当前题目序号 / 列表中的全部题目数量
1. 加入错题本
1. 如果题目没有加入错题本，按钮展示「加入错题本」
1. 如果题目已加入错题本，按钮展示「已加入错题本」不可点击
1. 上一题：点击后展示上一题的详情。第一题不展示上一题按钮
1. 下一题：点击后展示下一题的详情。最后一题不展示下一题按钮
![analyze_in_table_image_L9SkbV6sko5FVbxYcfDcAC1qnpg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844227227.png)

- 题目列表：在结算页点击查看详情，进入题目列表页面
![analyze_in_table_image_MGPobJyOlop6YVxdI4kcucIsn2A](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844227701.png)

- 题目详情：在题目列表页点击查看解析，展示题目详情
![analyze_in_table_image_NdMMbWsS7owwOwxxhlUcTX5rnBc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844228176.png)

- 推荐学习：
![board_C4lPw82zBhogmqbDXRfcewf9n2c](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844224132.png)

1. 根据策略，展示不同的学习模块名称、预估题目数量
1. 模块名称：巩固练习、巩固练习-加练、拓展练习
1. 无推荐学习内容时，展示默认文案“掌握度已达成，暂无推荐学习内容”
1. 用户点击去练习，进入对应的学习模块
![analyze_in_table_image_LdiGb9jhjob9OTxCdpIc7YrTnbd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844228587.png)

- 课程反馈
1. 展示极差、较差、一般、满意、很棒，5类评价
1. 用户点击某个icon后，展示对应的反馈弹窗
1. 提交按钮默认为不可点击状态，用户选择了某个选项后，切换为可提交状态
1. 提交后，toast “感谢反馈，我们会持续改进！”
![analyze_in_table_image_IqW3bjKbTogLnVxrMvucpnGMnWf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844229009.png)

极差&较差

![image_NB1vbY3jfoxkbsxNTv2cxUGxnwc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844229475.png)

一般

![image_IPINbKITOowin3xNiPMckRrynkf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844230011.png)

满意&很棒

![image_Ej44bHWiGoIzdyxDINBcT551nFC](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844230501.png)

- 操作按钮：
1. 回看课程：点击后重新进入课程
1. 所有组件都是已解锁状态，用户可以自由学习任何组件
1. 保留历史答题记录，用户进入练习组件时看到的是答题结果页面。
1. 完成：用户点击后返回课程入口页面。
- 展示：
1. 按照组成一节课程的“文档组件、练习组件”的排序进行展示。
1. 分为“已解锁”、“学习中”、“待解锁”三种状态。
- 已解锁：用户学习过的组件，全部为已解锁状态
- 待解锁：还没有学习的组件为待解锁状态
- 学习中：用户当前正在学习的组件
- 如果用户「回看课程」，则所有的组件都为解锁状
- 交互：
- 点击「课程进度」按钮，展示进度，暂停讲解
- 点击屏幕其他区域，收起进度，继续讲解
- 点击“已解锁”状态的组件，可直接跳转到对应组件的内容。
- 组件跳转逻辑：
1. 用户点击进入视频组件，从上次跳出的进度开始播放视频。如果上次视频已播完，则自动从头开始播放。
1. 用户点击进入练习组件：
1. 如果上次的题目没有答完，从上次跳出的题目开始继续答题
1. 如果上次的题目已全部答完，从第一题开始展示用户历史答题记录。
1. 用户可点击「下一题」，逐个题目查看后，进入下一个课程组件。
1. 用户跳转到之前学习过的组件，完成后自动进入课程中的下一个组件（视频/练习组件）
1. 在组件间切换，不影响学习进度的统计，学习进度还是按“已学完的组件数量/一节课中文档+练习组件的数量之和”计算。
- 文档组件入口：
![image_G72ZbIuhno1CguxxK1EcS6U3n3d](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844230999.png)

- 练习组件入口
![image_Wj2mbKm98onhyqxuQOXcdSW3npb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844231491.png)

- 课程进度
![image_TlI4bWfnFooISex1mYPcUE9Hnuf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844232054.png)

- 历史答题记录
![image_HmXSbDdSnoSjajx6iuPcKVOunZc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844232817.png)

- 退出课程：在上课过程中，点击左上角的退出按钮，记录用户的当前进度，返回课程入口页。
- 退出后再次进入课程的进度策略如下：
- 文档组件：从退出的时间点继续播放，默认进入跟随状态
- 练习组件：进入用户退出时未答完的题目，且保存退出前的答题结果
- 答疑组件：进入调起答疑组件的 文档 / 练习组件，默认不进入答疑组件。当用户再次点击答疑组件时，可查看历史对话记录。
- 文档组件：从退出的时间点继续播放，默认进入跟随状态
- 练习组件：进入用户退出时查看题目解析的页面


## 文档组件

相比Demo V1.1的改动点：

1. 内容播放：
1. 增加勾画轨迹，和JS内容同步播放
1. 默认展示字幕
1. 交互操作：
1. 增加双击、长按等快捷交互
1. 增加1.75和3倍速
1. 增加前进 / 后退 10s
| 模块/功能 | 需求描述 | 原型图 |
| --- | --- | --- |
| 模式切换 | 进入课程后，默认进入「跟随模式」当用户滑动JS动画内容时，进入「自由模式」右上角常驻展示「问一问」和「课程进度」 |  |
| 内容播放 |  |  |
| 快捷交互 | 跟随模式和自由模式均支持以下快捷交互 |  |
| 操作面板 | 跟随模式和自由模式均支持唤起操作面板，单击文档以外区域可唤起操作面板。 | ![in_table_image_UEyNbKjIqoO8cPx4xbqcyAdJnSc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844236175.png) |
| 进入下一组件 | 分为三种情况 |  |

跟随模式

![image_AZEdbJEhgo6NC9xhyWAcv2njnAo](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844233304.png)



toast提示

![image_BpvgbYREKoeNkExjuvFca1qlnvd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844234185.png)

自由模式

![image_IrKybk0AboY613xigFbcQq4hnib](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844234691.png)

- 跟随模式
1. 跟随模式下，数字人视频、勾画轨迹和JS 动画内容自动播放，三个内容间的进度保持一致。
1. 播放时，板书对应的内容块（句子）下方展示进度线，表示目前讲到这里了。
1. 如果老师讲解内容在多个句子中上下来回跳跃，进度线始终在最后的一个句子后面，不跟着讲解上下跳跃。
- 自由模式
1. 进入自由模式，数字人视频默认正常播放，进度不受影响。
1. 板书不跟随进度自动播放，但是仍然展示进度线，提醒学生当前讲到哪里。
1. 不播放勾画轨迹
1. 单击没有评论的文档内容 - 从这里学
1. 没有评论的文档内容：当前内容所在的句子高亮展示，并出现「从这里学」按钮（同demoV1.1）
1. 用户点击「从这里学」，数字人视频&勾画轨迹从句子对应的时间戳开始播放。
1. 自由模式下点击「从这里学」，自动切换为跟随模式
1. 单击有评论的文档内容 - 展示评论内容（本期不做）
1. 单击文档以外区域 - 唤起操作面板
1. 长按文档内容：选中文字/图片内容，唤起问一问、评论操作（本期不做）
1. 用户可拖拽高亮条，选择文字
1. 点击问一问，进入答疑组件
1. 点击评论，唤起键盘，用户可输入内容进行发表
1. 长按文档以外区域：3倍速播放内容，松手后恢复原始速度
1. 双击 - 切换播放 / 暂停状态
单击文档-从这里学-文字

![image_RDuzbOPUYoyDHTxWwdqcQDslnwb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844235185.png)

单击文档-从这里学-公式

![image_KgQob6BNFoxTTOxHT5ScxdZWnYf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844235723.png)

- 页面元素：
1. 左上角展示「退出学习」
1. 数字人区域展示“视频播控”：包括字幕、播放/暂停、倍速、前进 / 后退 10s
- 视频播控：
1. 字幕：点击切换字幕开关
1. 暂停/播放：点击暂停按钮，暂停所有内容的播放，再次点击时恢复播放。
1. 视频倍速：倍速需保持数字人、JS板书、勾画内容的播放进度一致。
1. 点击倍速按钮，可选择倍速播放，支持「0.75、1.0、1.25、1.5、1.75、2.0」6种倍速选择。
1. 全局倍速，设置倍速后对一节课中的所有视频组件生效。退出课程下次重新进入课程，恢复正常速度。
1. 前进 / 后退 10s：点击后，数字人、JS板书、勾画内容进度同步变化
![analyze_in_table_image_UEyNbKjIqoO8cPx4xbqcyAdJnSc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746844236175.png)

1. 从文档组件进入文档组件
1. 在「跟随模式」下正常学习，数字人视频播完后，自动进入下一个组件，无转场动效
1. 在「跟随模式」和「自由模式」向上滑动文档，进入下一学习内容。
1. 当前片段的JS动画滑动到底部时，会出现“阻尼效果”，用户继续向上滑动将切换至下一个组件的内容
1. 进入下一课程组件后，默认为跟随模式。
1. 从文档组件进入练习组件
「跟随模式」和「自由模式」下均展示切换动效

1. 从练习组件进入文档组件
「跟随模式」和「自由模式」下均展示切换动效





# 六、数据需求

## 埋点需求

**pv/uv采集**：所有事件默认都要带上pv/uv统计。

**时间戳记录**：每条事件自动带上timestamp字段，方便后续行为序列分析。

**用户身份信息**：带上 user_id、school_id作为隐式参数。



## 数据存储需求

| 模块 | 数据项 |
| --- | --- |
| 整体 |  |
| 文档组件 |  |
| 视频组件 |  |
| 答疑组件 |  |

1. 每个学生在每节课的答题数量、正确率、学习时长、获得的积分
1. 每个学生在每节课的学习进度（计算口径同课程进度）
1. 每个文档的时长、用户实际播放时长
1. 每个视频的时长、用户实际播放时长
1. 用户对话明细
1. 每个会话的对话轮数（每个视频/题目记为一个新会话）
1. 每一轮用户的输入方式


