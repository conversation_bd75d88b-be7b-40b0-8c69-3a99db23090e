---
description: 
globs: backend/app/domain/services/**/*.py
alwaysApply: false
---
# 领域服务 (Domain Service) 代码生成规则

> **核心原则：简洁优先**。代码应当保持简单、聚焦和可维护。

## 边界
- 仅接受 / 返回领域对象或原生类型；禁止直接 I/O、ORM  
- 不持有可变状态；实体状态变更通过实体方法完成  
- 外部依赖通过 Port/Adapter 接口注入  
- 抛出 `DomainError` 子类，不抛技术异常    

## 约定
- 类名以 `Service` 结尾  
- 一个服务只关注一个领域概念  
- 核心算法可用 `@staticmethod`  
- 入/出参只允许值对象或实体  
- 常量放在类级别，便于测试覆盖 

## 示例代码
```python
class AuthenticationService:
    """认证领域服务——无状态"""

    TOKEN_BYTES = 48  # 常量

    @staticmethod
    def validate_credentials(user: User | None, password: str) -> None:
        if not user or not user.verify_password(password) or not user.is_active():
            raise InvalidCredentialsError()

    @staticmethod
    def generate_token_value() -> str:
        token_bytes = secrets.token_bytes(AuthenticationService.TOKEN_BYTES)
        return base64.urlsafe_b64encode(token_bytes).decode().rstrip("=")
```